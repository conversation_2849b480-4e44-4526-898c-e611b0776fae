/**
 * Intercontinental Tracking Map Styles
 * Additional styles for the intercontinental tracking map
 */

/* Transport hub markers */
.airport-dot {
    background: #00bcd4; /* Light blue for airports */
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.seaport-dot {
    background: #3f51b5; /* Indigo for seaports */
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Transport hub icons */
.airport-dot i, .seaport-dot i {
    color: white;
    font-size: 12px;
}

.airport-dot i {
    transform: rotate(45deg);
}

/* Transport mode paths */
.air-path {
    stroke-dasharray: 10, 10;
    animation: dash 30s linear infinite;
}

.sea-path {
    stroke-dasharray: 5, 10;
    animation: dash 40s linear infinite;
}

.rail-path {
    stroke-dasharray: 1, 10;
    animation: dash 20s linear infinite;
}

/* Transport mode badges in popups */
.air-mode {
    background-color: #00bcd4;
    color: white;
}

.sea-mode {
    background-color: #3f51b5;
    color: white;
}

.rail-mode {
    background-color: #9c27b0;
    color: white;
}

.road-mode {
    background-color: #5c2be2;
    color: white;
}

/* Marker type badges in popups */
.airport-type, .seaport-type {
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    margin-right: 5px;
    display: inline-flex;
    align-items: center;
}

.airport-type {
    background-color: #00bcd4;
    color: white;
}

.seaport-type {
    background-color: #3f51b5;
    color: white;
}

/* Enhanced map legend */
.map-legend {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(5px);
    padding: 10px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.map-legend .legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.map-legend .legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.map-legend .legend-color.airport {
    background-color: #00bcd4;
}

.map-legend .legend-color.seaport {
    background-color: #3f51b5;
}

/* Animation for dashed lines */
@keyframes dash {
    to {
        stroke-dashoffset: 1000;
    }
}

/* Routing mode selector */
.routing-mode-selector {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    background: white;
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.routing-mode-selector label {
    margin-right: 10px;
    font-weight: bold;
    font-size: 0.9rem;
}

.routing-mode-selector select {
    padding: 5px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .airport-dot, .seaport-dot {
        width: 20px;
        height: 20px;
    }
    
    .airport-dot i, .seaport-dot i {
        font-size: 10px;
    }
    
    .routing-mode-selector {
        top: auto;
        bottom: 10px;
        right: 10px;
    }
}
