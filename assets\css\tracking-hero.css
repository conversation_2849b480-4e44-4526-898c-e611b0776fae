/* Enhanced Hero Section for Tracking Page */

.hero.gradient-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 80%), 
                url('https://images.unsplash.com/photo-1586528116493-d3f4b2d8e1b7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
    background-blend-mode: multiply;
    padding: 120px 0 100px;
    position: relative;
    overflow: hidden;
}

.hero.gradient-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/textures/subtle-dots.png');
    opacity: 0.1;
    z-index: 1;
}

.hero.gradient-hero .hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.hero.gradient-hero h1 {
    font-size: 3.2rem;
    margin-bottom: 20px;
    color: white;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    animation: fadeInDown 0.8s ease-out;
}

.hero.gradient-hero p {
    font-size: 1.3rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    animation: fadeInUp 0.8s ease-out;
}

.hero.gradient-hero .cta-buttons {
    animation: fadeIn 1s ease-out;
    animation-delay: 0.3s;
    animation-fill-mode: both;
}

.hero.gradient-hero .btn.glass-btn {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.hero.gradient-hero .btn.glass-btn:hover {
    background-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.hero.gradient-hero .btn.glass-btn i {
    margin-right: 8px;
}

.hero.gradient-hero .primary-btn.glass-btn {
    background-color: rgba(var(--secondary-color-rgb, 0, 212, 95), 0.2);
}

.hero.gradient-hero .primary-btn.glass-btn:hover {
    background-color: rgba(var(--secondary-color-rgb, 0, 212, 95), 0.3);
}

/* Animation keyframes */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Responsive styles */
@media (max-width: 768px) {
    .hero.gradient-hero {
        padding: 100px 0 80px;
    }
    
    .hero.gradient-hero h1 {
        font-size: 2.5rem;
    }
    
    .hero.gradient-hero p {
        font-size: 1.1rem;
    }
    
    .hero.gradient-hero .cta-buttons {
        flex-direction: column;
        gap: 15px;
    }
    
    .hero.gradient-hero .btn.glass-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .hero.gradient-hero h1 {
        font-size: 2rem;
    }
    
    .hero.gradient-hero p {
        font-size: 1rem;
    }
}

/* Dark theme adjustments */
.dark-theme .hero.gradient-hero {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #2a1a6c 80%), 
                url('https://images.unsplash.com/photo-1586528116493-d3f4b2d8e1b7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
    background-blend-mode: multiply;
}

.dark-theme .hero.gradient-hero::before {
    opacity: 0.05;
}
