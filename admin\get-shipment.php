<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if(!isLoggedIn()) {
    echo json_encode(['success' => false, 'message' => 'Authentication required']);
    exit;
}

// Get shipment ID
$shipmentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($shipmentId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid shipment ID']);
    exit;
}

// Get shipment details
try {
    $db->query("SELECT * FROM shipments WHERE id = :id");
    $db->bind(':id', $shipmentId);
    $shipment = $db->single();
    
    if (!$shipment) {
        echo json_encode(['success' => false, 'message' => 'Shipment not found']);
        exit;
    }
    
    // Return shipment data as JSON
    echo json_encode([
        'success' => true,
        'shipment' => $shipment
    ]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
