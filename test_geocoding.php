<?php
/**
 * Test script for geocoding functionality
 * 
 * This script tests the geocoding functionality to ensure it's working correctly.
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/geocoding.php';

// Only allow access to admins
if (!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Geocoding Test';

// Initialize geocoding helper
$geocoder = new GeocodingHelper();

// Test addresses
$testAddresses = [
    'New York, NY, USA',
    'London, UK',
    'Sydney, Australia',
    'Tokyo, Japan',
    'Paris, France'
];

// Test coordinates
$testCoordinates = [
    ['lat' => 40.7128, 'lng' => -74.0060, 'name' => 'New York'],
    ['lat' => 51.5074, 'lng' => -0.1278, 'name' => 'London'],
    ['lat' => -33.8688, 'lng' => 151.2093, 'name' => 'Sydney'],
    ['lat' => 35.6762, 'lng' => 139.6503, 'name' => 'Tokyo'],
    ['lat' => 48.8566, 'lng' => 2.3522, 'name' => 'Paris']
];

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-map-marked-alt"></i> Geocoding Test</h1>
            <div class="admin-actions">
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Geocoding Test Results</h2>
                </div>
                <div class="card-body">
                    <h3>Geocoding (Address to Coordinates)</h3>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Address</th>
                                    <th>Result</th>
                                    <th>Provider</th>
                                    <th>Map</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($testAddresses as $address): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($address); ?></td>
                                        <?php
                                        $result = $geocoder->geocode($address);
                                        if ($result): ?>
                                            <td>
                                                <strong>Latitude:</strong> <?php echo $result['latitude']; ?><br>
                                                <strong>Longitude:</strong> <?php echo $result['longitude']; ?>
                                            </td>
                                            <td><?php echo ucfirst($result['provider']); ?></td>
                                            <td>
                                                <a href="https://www.openstreetmap.org/?mlat=<?php echo $result['latitude']; ?>&mlon=<?php echo $result['longitude']; ?>&zoom=15" target="_blank" class="btn outline-btn">
                                                    <i class="fas fa-map-marker-alt"></i> View
                                                </a>
                                            </td>
                                        <?php else: ?>
                                            <td colspan="3" class="text-danger">Failed to geocode</td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <h3>Reverse Geocoding (Coordinates to Address)</h3>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Coordinates</th>
                                    <th>Result</th>
                                    <th>Provider</th>
                                    <th>Map</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($testCoordinates as $coord): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($coord['name']); ?></strong><br>
                                            <strong>Latitude:</strong> <?php echo $coord['lat']; ?><br>
                                            <strong>Longitude:</strong> <?php echo $coord['lng']; ?>
                                        </td>
                                        <?php
                                        $result = $geocoder->reverseGeocode($coord['lat'], $coord['lng']);
                                        if ($result): ?>
                                            <td><?php echo htmlspecialchars($result['formatted_address']); ?></td>
                                            <td><?php echo ucfirst($result['provider']); ?></td>
                                            <td>
                                                <a href="https://www.openstreetmap.org/?mlat=<?php echo $coord['lat']; ?>&mlon=<?php echo $coord['lng']; ?>&zoom=15" target="_blank" class="btn outline-btn">
                                                    <i class="fas fa-map-marker-alt"></i> View
                                                </a>
                                            </td>
                                        <?php else: ?>
                                            <td colspan="3" class="text-danger">Failed to reverse geocode</td>
                                        <?php endif; ?>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <h3>Distance Calculation</h3>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>From</th>
                                    <th>To</th>
                                    <th>Distance (km)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Calculate distances between each pair of test coordinates
                                for ($i = 0; $i < count($testCoordinates) - 1; $i++) {
                                    $from = $testCoordinates[$i];
                                    $to = $testCoordinates[$i + 1];
                                    $distance = $geocoder->getDistance($from['lat'], $from['lng'], $to['lat'], $to['lng']);
                                ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($from['name']); ?></td>
                                        <td><?php echo htmlspecialchars($to['name']); ?></td>
                                        <td><?php echo number_format($distance, 2); ?> km</td>
                                    </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2>Manual Geocoding Test</h2>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3>Geocode an Address</h3>
                            <form id="geocode-form" class="test-form">
                                <div class="form-group">
                                    <label for="address">Address</label>
                                    <input type="text" id="address" name="address" class="form-control" placeholder="Enter an address to geocode">
                                </div>
                                <button type="submit" class="btn primary-btn">Geocode</button>
                            </form>
                            <div id="geocode-result" class="result-box"></div>
                        </div>
                        <div class="col-md-6">
                            <h3>Reverse Geocode Coordinates</h3>
                            <form id="reverse-geocode-form" class="test-form">
                                <div class="form-group">
                                    <label for="latitude">Latitude</label>
                                    <input type="text" id="latitude" name="latitude" class="form-control" placeholder="Enter latitude">
                                </div>
                                <div class="form-group">
                                    <label for="longitude">Longitude</label>
                                    <input type="text" id="longitude" name="longitude" class="form-control" placeholder="Enter longitude">
                                </div>
                                <button type="submit" class="btn primary-btn">Reverse Geocode</button>
                            </form>
                            <div id="reverse-geocode-result" class="result-box"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.result-box {
    margin-top: 20px;
    padding: 15px;
    border-radius: 5px;
    background-color: var(--glass-bg);
    border: var(--glass-border);
    min-height: 100px;
}

.test-form {
    margin-bottom: 20px;
}

.loading {
    text-align: center;
    padding: 20px;
}

.error {
    color: #dc3545;
    padding: 10px;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 5px;
}

.success {
    color: #28a745;
}

.map-link {
    display: inline-block;
    margin-top: 10px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Geocode form
    const geocodeForm = document.getElementById('geocode-form');
    const geocodeResult = document.getElementById('geocode-result');
    
    geocodeForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const address = document.getElementById('address').value.trim();
        if (!address) {
            geocodeResult.innerHTML = '<div class="error">Please enter an address</div>';
            return;
        }
        
        geocodeResult.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Geocoding...</div>';
        
        fetch('geocode.php?address=' + encodeURIComponent(address))
            .then(response => response.json())
            .then(data => {
                if (data.success && data.result) {
                    const result = data.result;
                    geocodeResult.innerHTML = `
                        <div class="success">
                            <h4>Geocoding Successful</h4>
                            <p><strong>Provider:</strong> ${result.provider}</p>
                            <p><strong>Latitude:</strong> ${result.latitude}</p>
                            <p><strong>Longitude:</strong> ${result.longitude}</p>
                            <a href="https://www.openstreetmap.org/?mlat=${result.latitude}&mlon=${result.longitude}&zoom=15" target="_blank" class="btn outline-btn map-link">
                                <i class="fas fa-map-marker-alt"></i> View on Map
                            </a>
                        </div>
                    `;
                } else {
                    geocodeResult.innerHTML = `<div class="error">Geocoding failed: ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                geocodeResult.innerHTML = `<div class="error">Request error: ${error.message}</div>`;
            });
    });
    
    // Reverse geocode form
    const reverseGeocodeForm = document.getElementById('reverse-geocode-form');
    const reverseGeocodeResult = document.getElementById('reverse-geocode-result');
    
    reverseGeocodeForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const latitude = document.getElementById('latitude').value.trim();
        const longitude = document.getElementById('longitude').value.trim();
        
        if (!latitude || !longitude) {
            reverseGeocodeResult.innerHTML = '<div class="error">Please enter both latitude and longitude</div>';
            return;
        }
        
        reverseGeocodeResult.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Reverse Geocoding...</div>';
        
        fetch(`reverse_geocode.php?lat=${latitude}&lng=${longitude}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.result) {
                    const result = data.result;
                    reverseGeocodeResult.innerHTML = `
                        <div class="success">
                            <h4>Reverse Geocoding Successful</h4>
                            <p><strong>Provider:</strong> ${result.provider}</p>
                            <p><strong>Address:</strong> ${result.formatted_address}</p>
                            <a href="https://www.openstreetmap.org/?mlat=${latitude}&mlon=${longitude}&zoom=15" target="_blank" class="btn outline-btn map-link">
                                <i class="fas fa-map-marker-alt"></i> View on Map
                            </a>
                        </div>
                    `;
                } else {
                    reverseGeocodeResult.innerHTML = `<div class="error">Reverse geocoding failed: ${data.error || 'Unknown error'}</div>`;
                }
            })
            .catch(error => {
                reverseGeocodeResult.innerHTML = `<div class="error">Request error: ${error.message}</div>`;
            });
    });
});
</script>

<?php include_once 'includes/footer.php'; ?>
