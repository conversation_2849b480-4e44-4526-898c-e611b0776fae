/* Admin Dashboard specific overrides */
/* These styles will only affect elements within .admin-dashboard-container */

.admin-dashboard-container {
    max-width: 100%;
    overflow-x: hidden;
}

.admin-dashboard-container .grid,
.admin-dashboard-container .card-grid,
.admin-dashboard-container .metrics-grid,
.admin-dashboard-container .audience-grid,
.admin-dashboard-container .challenges-grid,
.admin-dashboard-container .segments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)) !important;
    gap: 1rem !important;
    margin-bottom: 1rem !important;
}

/* Reset any inherited styles that might be causing the issue */
.admin-dashboard-container * {
    box-sizing: border-box;
}

/* Ensure content stays within viewport */
.admin-dashboard-container .page-content {
    max-width: 100vw;
    padding: 1rem;
}

/* Maintain responsive behavior */
@media (max-width: 1200px) {
    .admin-dashboard-container .grid,
    .admin-dashboard-container .card-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (max-width: 768px) {
    .admin-dashboard-container .grid,
    .admin-dashboard-container .card-grid {
        grid-template-columns: 1fr !important;
    }
}