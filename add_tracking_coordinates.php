<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>Adding Tracking Coordinates</h1>";

try {
    // Get shipments without tracking updates that have coordinates
    $stmt = $conn->query("
        SELECT s.id, s.tracking_number, s.origin, s.destination, s.status
        FROM shipments s
        LEFT JOIN (
            SELECT shipment_id, COUNT(*) as update_count
            FROM tracking_updates
            WHERE latitude IS NOT NULL AND longitude IS NOT NULL
            GROUP BY shipment_id
        ) t ON s.id = t.shipment_id
        WHERE t.update_count IS NULL OR t.update_count = 0
        LIMIT 10
    ");

    $shipments = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($shipments)) {
        echo "<p>No shipments found that need coordinates.</p>";
    } else {
        echo "<p>Found " . count($shipments) . " shipments that need coordinates.</p>";

        // Sample coordinates for different cities
        $cityCoordinates = [
            'New York, USA' => ['lat' => 40.7128, 'lng' => -74.0060],
            'Los Angeles, USA' => ['lat' => 34.0522, 'lng' => -118.2437],
            'Chicago, USA' => ['lat' => 41.8781, 'lng' => -87.6298],
            'Houston, USA' => ['lat' => 29.7604, 'lng' => -95.3698],
            'Phoenix, USA' => ['lat' => 33.4484, 'lng' => -112.0740],
            'Philadelphia, USA' => ['lat' => 39.9526, 'lng' => -75.1652],
            'San Antonio, USA' => ['lat' => 29.4241, 'lng' => -98.4936],
            'San Diego, USA' => ['lat' => 32.7157, 'lng' => -117.1611],
            'Dallas, USA' => ['lat' => 32.7767, 'lng' => -96.7970],
            'San Jose, USA' => ['lat' => 37.3382, 'lng' => -121.8863],
            'London, UK' => ['lat' => 51.5074, 'lng' => -0.1278],
            'Paris, France' => ['lat' => 48.8566, 'lng' => 2.3522],
            'Berlin, Germany' => ['lat' => 52.5200, 'lng' => 13.4050],
            'Madrid, Spain' => ['lat' => 40.4168, 'lng' => -3.7038],
            'Rome, Italy' => ['lat' => 41.9028, 'lng' => 12.4964],
            'Tokyo, Japan' => ['lat' => 35.6762, 'lng' => 139.6503],
            'Beijing, China' => ['lat' => 39.9042, 'lng' => 116.4074],
            'Sydney, Australia' => ['lat' => -33.8688, 'lng' => 151.2093],
            'Rio de Janeiro, Brazil' => ['lat' => -22.9068, 'lng' => -43.1729],
            'Cairo, Egypt' => ['lat' => 30.0444, 'lng' => 31.2357],
            'Miami, USA' => ['lat' => 25.7617, 'lng' => -80.1918],
            'Seattle, USA' => ['lat' => 47.6062, 'lng' => -122.3321],
            'Manchester, UK' => ['lat' => 53.4808, 'lng' => -2.2426],
            'Lyon, France' => ['lat' => 45.7640, 'lng' => 4.8357]
        ];

        // Process each shipment
        foreach ($shipments as $shipment) {
            echo "<h2>Processing Shipment #" . $shipment['id'] . " (" . $shipment['tracking_number'] . ")</h2>";

            // Get origin and destination coordinates
            $origin = $shipment['origin'];
            $destination = $shipment['destination'];

            $originCoords = isset($cityCoordinates[$origin]) ? $cityCoordinates[$origin] : null;
            $destCoords = isset($cityCoordinates[$destination]) ? $cityCoordinates[$destination] : null;

            if (!$originCoords) {
                echo "<p>No coordinates found for origin: " . $origin . ". Using default.</p>";
                $originCoords = ['lat' => 40.7128, 'lng' => -74.0060]; // Default to New York
            }

            if (!$destCoords) {
                echo "<p>No coordinates found for destination: " . $destination . ". Using default.</p>";
                $destCoords = ['lat' => 34.0522, 'lng' => -118.2437]; // Default to Los Angeles
            }

            // Create tracking updates with coordinates based on status
            $updates = [];

            // Always add origin update
            $updates[] = [
                'location' => $origin,
                'status' => 'pending',
                'lat' => $originCoords['lat'],
                'lng' => $originCoords['lng'],
                'notes' => 'Shipment received at origin facility',
                'timestamp' => date('Y-m-d H:i:s', strtotime('-' . rand(5, 10) . ' days'))
            ];

            // If in transit or beyond, add intermediate points along a realistic route
            if (in_array($shipment['status'], ['in_transit', 'in-transit', 'delayed', 'delivered'])) {
                // Define major transit hubs based on geography
                $transitHubs = [];

                // Determine transit hubs based on origin and destination
                // This simulates realistic shipping routes through major hubs
                $originCountry = explode(',', $origin)[1] ?? 'Unknown';
                $destCountry = explode(',', $destination)[1] ?? 'Unknown';

                // For US domestic shipments
                if (strpos($originCountry, 'USA') !== false && strpos($destCountry, 'USA') !== false) {
                    // East to West Coast
                    if (strpos($origin, 'New York') !== false && strpos($destination, 'Los Angeles') !== false) {
                        $transitHubs = [
                            ['city' => 'Chicago, USA', 'coords' => $cityCoordinates['Chicago, USA']],
                            ['city' => 'Denver, USA', 'coords' => ['lat' => 39.7392, 'lng' => -104.9903]]
                        ];
                    }
                    // West to East Coast
                    else if (strpos($origin, 'Los Angeles') !== false && strpos($destination, 'New York') !== false) {
                        $transitHubs = [
                            ['city' => 'Dallas, USA', 'coords' => $cityCoordinates['Dallas, USA']],
                            ['city' => 'Atlanta, USA', 'coords' => ['lat' => 33.7490, 'lng' => -84.3880]]
                        ];
                    }
                    // Default US route
                    else {
                        // Find a midpoint hub
                        $midLat = ($originCoords['lat'] + $destCoords['lat']) / 2;
                        $midLng = ($originCoords['lng'] + $destCoords['lng']) / 2;

                        // Find nearest hub to midpoint
                        $nearestHub = 'Chicago, USA'; // Default hub
                        $minDistance = PHP_FLOAT_MAX;

                        $majorUSHubs = [
                            'Chicago, USA', 'Dallas, USA', 'Atlanta, USA', 'Denver, USA',
                            'Houston, USA', 'Phoenix, USA', 'Philadelphia, USA'
                        ];

                        foreach ($majorUSHubs as $hub) {
                            if (isset($cityCoordinates[$hub])) {
                                $distance = sqrt(pow($midLat - $cityCoordinates[$hub]['lat'], 2) +
                                                pow($midLng - $cityCoordinates[$hub]['lng'], 2));
                                if ($distance < $minDistance) {
                                    $minDistance = $distance;
                                    $nearestHub = $hub;
                                }
                            }
                        }

                        $transitHubs = [['city' => $nearestHub, 'coords' => $cityCoordinates[$nearestHub]]];
                    }
                }
                // International shipments
                else {
                    // Add appropriate international hubs based on countries
                    if (strpos($originCountry, 'UK') !== false && strpos($destCountry, 'USA') !== false) {
                        $transitHubs = [['city' => 'London, UK', 'coords' => $cityCoordinates['London, UK']]];
                    }
                    else if (strpos($originCountry, 'France') !== false) {
                        $transitHubs = [['city' => 'Paris, France', 'coords' => $cityCoordinates['Paris, France']]];
                    }
                    // Add more international routes as needed
                }

                // If no specific route was found, create some intermediate points
                if (empty($transitHubs)) {
                    // Calculate intermediate points
                    $numPoints = rand(1, 3); // 1-3 intermediate points

                    for ($i = 1; $i <= $numPoints; $i++) {
                        // Calculate position along the route (0.25, 0.5, 0.75)
                        $fraction = $i / ($numPoints + 1);

                        // Linear interpolation between origin and destination
                        $lat = $originCoords['lat'] + $fraction * ($destCoords['lat'] - $originCoords['lat']);
                        $lng = $originCoords['lng'] + $fraction * ($destCoords['lng'] - $originCoords['lng']);

                        // Add some randomness to make it look more like a real route
                        $lat += (rand(-10, 10) / 100);
                        $lng += (rand(-10, 10) / 100);

                        // Find nearest city (simplified - just for demo)
                        $nearestCity = 'Transit Point ' . $i;
                        $minDistance = PHP_FLOAT_MAX;

                        foreach ($cityCoordinates as $city => $coords) {
                            $distance = sqrt(pow($lat - $coords['lat'], 2) + pow($lng - $coords['lng'], 2));
                            if ($distance < $minDistance) {
                                $minDistance = $distance;
                                $nearestCity = $city;
                            }
                        }

                        $transitHubs[] = ['city' => $nearestCity, 'coords' => ['lat' => $lat, 'lng' => $lng]];
                    }
                }

                // Add transit points to updates
                foreach ($transitHubs as $index => $hub) {
                    $status = 'in_transit';
                    $notes = 'Shipment in transit through ' . $hub['city'];

                    // If delayed status, make one of the points delayed
                    if ($shipment['status'] === 'delayed' && $index === count($transitHubs) - 1) {
                        $status = 'delayed';
                        $notes = 'Shipment delayed at ' . $hub['city'] . ' due to weather conditions';
                    }

                    // Calculate a realistic timestamp based on position in route
                    $dayOffset = count($transitHubs) - $index;
                    $timestamp = date('Y-m-d H:i:s', strtotime('-' . $dayOffset . ' days'));

                    $updates[] = [
                        'location' => $hub['city'],
                        'status' => $status,
                        'lat' => $hub['coords']['lat'],
                        'lng' => $hub['coords']['lng'],
                        'notes' => $notes,
                        'timestamp' => $timestamp
                    ];
                }
            }

            // If delivered, add destination point
            if ($shipment['status'] === 'delivered') {
                $updates[] = [
                    'location' => $destination,
                    'status' => 'delivered',
                    'lat' => $destCoords['lat'],
                    'lng' => $destCoords['lng'],
                    'notes' => 'Shipment delivered successfully',
                    'timestamp' => date('Y-m-d H:i:s', strtotime('-' . rand(0, 1) . ' days'))
                ];
            }

            // Sort updates by timestamp
            usort($updates, function($a, $b) {
                return strtotime($b['timestamp']) - strtotime($a['timestamp']);
            });

            // Insert tracking updates
            $insertStmt = $conn->prepare("
                INSERT INTO tracking_updates
                (shipment_id, location, status, latitude, longitude, notes, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");

            $addedCount = 0;
            foreach ($updates as $update) {
                $result = $insertStmt->execute([
                    $shipment['id'],
                    $update['location'],
                    $update['status'],
                    $update['lat'],
                    $update['lng'],
                    $update['notes'],
                    $update['timestamp']
                ]);

                if ($result) {
                    $addedCount++;
                    echo "<p>Added tracking update: " . $update['location'] . " (" . $update['status'] . ")</p>";
                }
            }

            echo "<p>Added " . $addedCount . " tracking updates with coordinates for shipment #" . $shipment['id'] . ".</p>";
        }

        echo "<h2>Summary</h2>";
        echo "<p>Successfully processed " . count($shipments) . " shipments.</p>";
    }

    echo "<p><a href='tracking/index.php'>Go to Tracking Page</a></p>";

} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
