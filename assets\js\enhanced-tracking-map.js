// Enhanced Tracking Map with Leaflet and Ant Path
document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced tracking map script loaded');

    // Check if tracking map element exists
    const mapElement = document.getElementById('tracking-map');
    if (!mapElement) {
        console.log('Map element not found');
        return;
    }

    // Check if tracking updates exist
    if (typeof trackingUpdates === 'undefined') {
        console.log('No tracking updates defined');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    console.log('Tracking updates:', trackingUpdates);

    // Function to check if any tracking update has coordinates
    function hasCoordinates(updates) {
        if (!updates || updates.length === 0) return false;

        for (let i = 0; i < updates.length; i++) {
            if (updates[i].latitude && updates[i].longitude) {
                return true;
            }
        }
        return false;
    }

    // Check if tracking updates have coordinates
    if (!hasCoordinates(trackingUpdates)) {
        console.log('No coordinates found in tracking updates');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    // Initialize the map with a dark theme
    const map = L.map('tracking-map', {
        zoomControl: false // We'll add it in a different position
    }).setView([39.8283, -98.5795], 4);

    // Add zoom control to the top-right
    L.control.zoom({
        position: 'topright'
    }).addTo(map);

    // Add the Mapbox Streets tile layer (more modern look)
    L.tileLayer('https://api.mapbox.com/styles/v1/{id}/tiles/{z}/{x}/{y}?access_token={accessToken}', {
        attribution: '© <a href="https://www.mapbox.com/about/maps/">Mapbox</a> © <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>',
        id: 'mapbox/streets-v11',
        accessToken: 'pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw', // Public token for demo
        tileSize: 512,
        zoomOffset: -1,
        maxZoom: 19
    }).addTo(map);

    // Custom marker icons with Font Awesome
    const markerIcons = {
        origin: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin origin-pin'>
                    <i class='fas fa-warehouse'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        destination: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin destination-pin'>
                    <i class='fas fa-flag-checkered'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        current: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin current-pin'>
                    <i class='fas fa-truck'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        transit: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin transit-pin'>
                    <i class='fas fa-exchange-alt'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        delayed: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin delayed-pin'>
                    <i class='fas fa-exclamation-triangle'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        })
    };

    // Process tracking updates
    let pathPoints = [];
    const markers = [];
    let generatedRoute = false;

    // Check if we need to generate a route
    // This happens when we only have origin and destination points
    if (trackingUpdates && trackingUpdates.length === 2) {
        // Check if both points have coordinates
        const hasOrigin = trackingUpdates[0].latitude && trackingUpdates[0].longitude;
        const hasDestination = trackingUpdates[1].latitude && trackingUpdates[1].longitude;

        if (hasOrigin && hasDestination) {
            console.log('Generating route between origin and destination');

            // Extract origin and destination
            const origin = [parseFloat(trackingUpdates[0].latitude), parseFloat(trackingUpdates[0].longitude)];
            const destination = [parseFloat(trackingUpdates[1].latitude), parseFloat(trackingUpdates[1].longitude)];

            // Generate a route with intermediate points
            const routeOptions = {
                numPoints: 3, // Number of intermediate points
                deviation: 0.3, // Maximum deviation from straight line
                startDate: new Date(trackingUpdates[0].timestamp),
                endDate: trackingUpdates[1].timestamp ? new Date(trackingUpdates[1].timestamp) : null,
                originLocation: trackingUpdates[0].location,
                destinationLocation: trackingUpdates[1].location
            };

            // Generate complete route
            const completeRoute = generateCompleteRoute(origin, destination, routeOptions);

            // Replace tracking updates with generated route
            trackingUpdates = completeRoute;
            generatedRoute = true;

            console.log('Generated route:', completeRoute);
        }
    }

    // Sort tracking updates by timestamp (oldest first for proper path creation)
    trackingUpdates.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Process tracking updates
    trackingUpdates.forEach((update, index) => {
        // Skip if no coordinates
        if (!update.latitude || !update.longitude) {
            return;
        }

        const lat = parseFloat(update.latitude);
        const lng = parseFloat(update.longitude);

        // Add to path points
        pathPoints.push([lat, lng]);

        // Determine icon based on status and position
        let icon;
        const status = update.status.toLowerCase().replace('-', '_');
        const isFirst = index === 0; // First chronologically (origin)
        const isLast = index === trackingUpdates.length - 1; // Last chronologically (current/destination)

        // Determine if this is origin, destination, or transit point
        if (isFirst) {
            // Origin point (first chronologically)
            icon = markerIcons.origin;
        } else if (isLast) {
            // Current location (last chronologically)
            if (status === 'delivered') {
                icon = markerIcons.destination;
            } else if (status === 'delayed') {
                icon = markerIcons.delayed;
            } else {
                icon = markerIcons.current;
            }
        } else {
            // Transit points
            if (status === 'delayed') {
                icon = markerIcons.delayed;
            } else {
                icon = markerIcons.transit;
            }
        }

        // Create marker
        const marker = L.marker([lat, lng], {
            icon: icon,
            title: update.location
        }).addTo(map);

        // Format timestamp
        const timestamp = new Date(update.timestamp);
        const formattedDate = timestamp.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
        const formattedTime = timestamp.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // Create popup content
        const popupContent = `
            <div class="map-info-window">
                <h3>${update.status}</h3>
                <p><strong>Location:</strong> ${update.location}</p>
                <p><strong>Time:</strong> ${formattedDate} at ${formattedTime}</p>
                ${update.notes ? `<p><strong>Notes:</strong> ${update.notes}</p>` : ''}
            </div>
        `;

        // Add popup to marker
        marker.bindPopup(popupContent);

        // Store marker
        markers.push(marker);
    });

    // Create path lines between points if we have multiple points
    if (pathPoints.length > 1) {
        // Create an Ant Path for animated route
        // First, make sure the Ant Path plugin is loaded
        if (typeof L.Polyline.AntPath === 'function') {
            // Create the ant path with animation
            const antPath = L.polyline.antPath(pathPoints, {
                delay: 800,
                dashArray: [10, 20],
                weight: 5,
                color: "#5c2be2",
                pulseColor: "#00d45f",
                paused: false,
                reverse: false,
                hardwareAccelerated: true
            }).addTo(map);
        } else {
            // Fallback to regular polyline with custom styling if Ant Path is not available
            const path = L.polyline(pathPoints, {
                color: '#5c2be2',
                weight: 4,
                opacity: 0.7,
                lineJoin: 'round',
                className: 'ant-path',
                dashArray: '10, 20'
            }).addTo(map);

            // Add direction arrows to the path
            L.polylineDecorator(path, {
                patterns: [
                    {
                        offset: 25,
                        repeat: 150,
                        symbol: L.Symbol.arrowHead({
                            pixelSize: 15,
                            polygon: false,
                            pathOptions: {
                                stroke: true,
                                color: '#fff',
                                weight: 2
                            }
                        })
                    }
                ]
            }).addTo(map);
        }

        // Fit map to path
        map.fitBounds(L.latLngBounds(pathPoints), {
            padding: [50, 50]
        });

        // Add pins for origin and destination
        // Origin pin (special styling)
        const originPoint = pathPoints[0]; // First chronologically
        const originMarker = L.marker(originPoint, {
            icon: markerIcons.origin,
            zIndexOffset: 1000
        }).addTo(map);

        // Add a label for origin
        L.marker(originPoint, {
            icon: L.divIcon({
                className: 'map-label',
                html: '<div>Origin</div>',
                iconSize: [80, 20],
                iconAnchor: [40, -10]
            })
        }).addTo(map);

        // Current location pin (special styling)
        const currentPoint = pathPoints[pathPoints.length - 1]; // Last chronologically
        const currentMarker = L.marker(currentPoint, {
            icon: markerIcons.current,
            zIndexOffset: 1000
        }).addTo(map);

        // Add a label for current location
        L.marker(currentPoint, {
            icon: L.divIcon({
                className: 'map-label',
                html: '<div>Current Location</div>',
                iconSize: [120, 20],
                iconAnchor: [60, -10]
            })
        }).addTo(map);

        // Add distance indicator
        if (pathPoints.length >= 2) {
            // Calculate total distance
            let totalDistance = 0;
            for (let i = 0; i < pathPoints.length - 1; i++) {
                const from = L.latLng(pathPoints[i][0], pathPoints[i][1]);
                const to = L.latLng(pathPoints[i+1][0], pathPoints[i+1][1]);
                totalDistance += from.distanceTo(to);
            }

            // Convert to miles (approximate)
            const distanceMiles = Math.round(totalDistance / 1609.34);

            // Add distance info box
            const distanceInfo = L.control({position: 'bottomleft'});
            distanceInfo.onAdd = function() {
                const div = L.DomUtil.create('div', 'distance-info');
                div.innerHTML = `<i class="fas fa-route"></i> Total Distance: ~${distanceMiles} miles`;
                return div;
            };
            distanceInfo.addTo(map);
        }
    } else if (pathPoints.length === 1) {
        // If only one point, center on it
        map.setView(pathPoints[0], 10);
    }

    // Add shipment progress calculation
    const progressElement = document.getElementById('shipment-progress-bar');
    if (progressElement && pathPoints.length > 1) {
        // Calculate progress percentage based on distance traveled
        let totalDistance = 0;
        let traveledDistance = 0;

        for (let i = 0; i < pathPoints.length - 1; i++) {
            const from = L.latLng(pathPoints[i][0], pathPoints[i][1]);
            const to = L.latLng(pathPoints[i+1][0], pathPoints[i+1][1]);
            const segmentDistance = from.distanceTo(to);
            totalDistance += segmentDistance;

            // Only count traveled segments
            if (i < pathPoints.length - 2) {
                traveledDistance += segmentDistance;
            }
        }

        const progressPercentage = Math.min(Math.round((traveledDistance / totalDistance) * 100), 100);
        progressElement.style.width = `${progressPercentage}%`;

        // Update progress text
        const progressTextElement = document.getElementById('progress-percentage');
        if (progressTextElement) {
            progressTextElement.textContent = `${progressPercentage}%`;
        }
    }

    // Add delivery countdown if estimated delivery date is available
    const countdownElement = document.getElementById('delivery-countdown');
    if (countdownElement && typeof shipmentData !== 'undefined' && shipmentData.estimated_delivery) {
        const updateCountdown = function() {
            const now = new Date();
            const deliveryDate = new Date(shipmentData.estimated_delivery);
            const timeLeft = deliveryDate - now;

            if (timeLeft <= 0) {
                countdownElement.innerHTML = '<p>Delivery time has passed</p>';
                return;
            }

            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

            countdownElement.innerHTML = `
                <div class="countdown-timer">
                    <div class="countdown-item">
                        <div class="countdown-value">${days}</div>
                        <div class="countdown-label">Days</div>
                    </div>
                    <div class="countdown-item">
                        <div class="countdown-value">${hours}</div>
                        <div class="countdown-label">Hours</div>
                    </div>
                    <div class="countdown-item">
                        <div class="countdown-value">${minutes}</div>
                        <div class="countdown-label">Minutes</div>
                    </div>
                </div>
            `;
        };

        // Update immediately and then every minute
        updateCountdown();
        setInterval(updateCountdown, 60000);
    }

    console.log('Enhanced map initialized successfully');
});
