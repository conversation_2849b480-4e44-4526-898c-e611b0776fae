// Admin JavaScript - Modular Version

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any admin-specific functionality

    // Handle form submissions with confirmation
    const confirmForms = document.querySelectorAll('.confirm-form');
    confirmForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const message = this.getAttribute('data-confirm') || 'Are you sure you want to perform this action?';
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Handle delete buttons with confirmation
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm') || 'Are you sure you want to delete this item?';
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Initialize datepickers if available
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.datepicker', {
            enableTime: false,
            dateFormat: 'Y-m-d'
        });

        flatpickr('.datetimepicker', {
            enableTime: true,
            dateFormat: 'Y-m-d H:i'
        });
    }

    // Initialize select2 if available
    if (typeof $.fn !== 'undefined' && typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            theme: 'bootstrap4'
        });
    }

    // Add refresh button functionality for dashboard modules
    const refreshButtons = document.querySelectorAll('.refresh-stats');
    refreshButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get the module to refresh
            const module = this.getAttribute('data-module');
            if (!module) {
                console.error('No module specified for refresh button');
                return;
            }
            
            // Find the closest container
            const container = this.closest('.stats-container, .chart-container, .performance-metrics, .activity-feed, .recent-shipments');
            if (!container) {
                console.error('No container found for refresh button');
                return;
            }
            
            // Add loading state
            const loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'loading-overlay';
            loadingOverlay.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            container.appendChild(loadingOverlay);
            
            // Make AJAX request to refresh module
            fetch('refresh-module.php?module=' + module, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Remove loading overlay
                loadingOverlay.remove();
                
                if (data.status === 'error') {
                    showMessage(data.message, 'error');
                    return;
                }
                
                // Handle different module refreshes
                switch(module) {
                    case 'status_chart':
                    case 'monthly_chart':
                    case 'destinations_chart':
                        // Update chart with new data
                        updateChart(module, data.data);
                        break;
                        
                    default:
                        // For other modules, just reload the page
                        // In a more advanced implementation, we would update the DOM directly
                        window.location.reload();
                        break;
                }
                
                // Show success message
                showMessage('Data refreshed successfully', 'success');
            })
            .catch(error => {
                // Remove loading overlay
                loadingOverlay.remove();
                
                // Show error message
                showMessage('Error refreshing data: ' + error.message, 'error');
            });
        });
    });

    // Add toggle functionality for sidebar on mobile
    const sidebarToggle = document.getElementById('sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            const sidebar = document.querySelector('.dashboard-sidebar');
            if (sidebar) {
                sidebar.classList.toggle('active');
                this.classList.toggle('active');
            }
        });
    }

    // Add tooltip functionality
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(tooltip => {
        tooltip.addEventListener('mouseenter', function() {
            const tooltipText = this.getAttribute('data-tooltip');
            const tooltipEl = document.createElement('div');
            tooltipEl.className = 'tooltip';
            tooltipEl.textContent = tooltipText;
            document.body.appendChild(tooltipEl);

            const rect = this.getBoundingClientRect();
            tooltipEl.style.top = rect.top - tooltipEl.offsetHeight - 10 + 'px';
            tooltipEl.style.left = rect.left + (rect.width / 2) - (tooltipEl.offsetWidth / 2) + 'px';
            tooltipEl.style.opacity = '1';
        });

        tooltip.addEventListener('mouseleave', function() {
            const tooltipEl = document.querySelector('.tooltip');
            if (tooltipEl) {
                tooltipEl.style.opacity = '0';
                setTimeout(() => {
                    tooltipEl.remove();
                }, 300);
            }
        });
    });
});

/**
 * Update a chart with new data
 * 
 * @param {string} module The module name
 * @param {object} data The new chart data
 */
function updateChart(module, data) {
    let chart;
    
    switch(module) {
        case 'status_chart':
            chart = window.statusChart;
            break;
        case 'monthly_chart':
            chart = window.monthlyChart;
            break;
        case 'destinations_chart':
            chart = window.destinationsChart;
            break;
    }
    
    if (chart) {
        chart.data = data;
        chart.update();
    } else {
        console.error('Chart not found:', module);
    }
}

/**
 * Show a message to the user
 * 
 * @param {string} message The message to show
 * @param {string} type The message type (success, error, info)
 */
function showMessage(message, type = 'info') {
    const messageEl = document.createElement('div');
    messageEl.className = 'message ' + type;
    messageEl.textContent = message;
    document.body.appendChild(messageEl);
    
    // Auto-hide the message
    setTimeout(() => {
        messageEl.style.opacity = '0';
        setTimeout(() => {
            messageEl.remove();
        }, 500);
    }, 3000);
}
