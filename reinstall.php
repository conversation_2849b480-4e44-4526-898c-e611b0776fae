<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Only start session if it hasn't been started yet
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Clear any existing sessions
$_SESSION = array();
if (session_status() === PHP_SESSION_ACTIVE) {
    session_destroy();
}

// Function to delete a file safely
function safeDeleteFile($file) {
    if (file_exists($file)) {
        if (unlink($file)) {
            echo "Successfully deleted: $file<br>";
        } else {
            echo "Failed to delete: $file<br>";
        }
    } else {
        echo "File does not exist: $file<br>";
    }
}

// Function to check and fix functions.php
function fixFunctionsPhp() {
    $functionsFile = 'includes/functions.php';
    if (file_exists($functionsFile)) {
        $content = file_get_contents($functionsFile);
        
        // Remove any content after the last PHP closing tag
        $lastPhpClose = strrpos($content, '?>');
        if ($lastPhpClose !== false) {
            $content = substr($content, 0, $lastPhpClose + 2);
        }
        
        // Ensure the file ends with a proper PHP closing tag
        if (substr(trim($content), -2) !== '?>') {
            $content = rtrim($content) . "\n// End of file\n?>";
        }
        
        // Write the fixed content back
        if (file_put_contents($functionsFile, $content)) {
            echo "Successfully fixed functions.php<br>";
        } else {
            echo "Failed to fix functions.php<br>";
        }
    }
}

// Check if the user confirmed the reinstall
if (isset($_POST['confirm']) && $_POST['confirm'] === 'yes') {
    echo "<h2>Starting Reinstallation Process</h2>";
    
    // Delete config file
    safeDeleteFile('includes/config.php');
    
    // Fix functions.php
    fixFunctionsPhp();
    
    // Redirect to install.php
    echo "<p>Redirecting to installation page...</p>";
    echo "<script>setTimeout(function() { window.location.href = 'install.php'; }, 3000);</script>";
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TransLogix Reinstallation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Your existing CSS styles */
        :root {
            --primary-color: #5c2be2;
            --text-color: #333;
            --bg-color: #fff;
            --glass-bg: rgba(255, 255, 255, 0.9);
            --glass-border: 1px solid rgba(255, 255, 255, 0.2);
        }

        body.dark-theme {
            --text-color: #fff;
            --bg-color: #1a1a1a;
            --glass-bg: rgba(0, 0, 0, 0.8);
            --glass-border: 1px solid rgba(255, 255, 255, 0.1);
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .install-wrapper {
            background: var(--glass-bg);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }

        .btn-danger {
            background: #dc3545;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-warning {
            background: #ffc107;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-wrapper">
            <h1>TransLogix Reinstallation</h1>
            
            <div class="alert alert-warning">
                <h3><i class="fas fa-exclamation-triangle"></i> Warning</h3>
                <p>This will delete your current configuration and reinstall the application. Any existing data will be lost.</p>
                <p>Are you sure you want to proceed?</p>
            </div>
            
            <form method="POST" action="reinstall.php">
                <input type="hidden" name="confirm" value="yes">
                <button type="submit" class="btn btn-danger">Yes, Reinstall</button>
                <a href="index.php" class="btn">Cancel</a>
            </form>
        </div>
    </div>
</body>
</html>
