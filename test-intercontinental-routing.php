<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Test Intercontinental Routing';

// Process form submission
$testOrigin = '';
$testDestination = '';
$testPreference = 'auto';
$showResults = false;
$originCoords = null;
$destinationCoords = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $testOrigin = sanitize($_POST['test_origin']);
    $testDestination = sanitize($_POST['test_destination']);
    $testPreference = sanitize($_POST['test_preference']);
    
    // Geocode the origin and destination
    require_once 'includes/geocoding.php';
    $geocoder = new GeocodingHelper();
    
    $originGeocode = $geocoder->geocode($testOrigin);
    $destinationGeocode = $geocoder->geocode($testDestination);
    
    if ($originGeocode && $destinationGeocode) {
        $originCoords = [
            'latitude' => $originGeocode['latitude'],
            'longitude' => $originGeocode['longitude']
        ];
        
        $destinationCoords = [
            'latitude' => $destinationGeocode['latitude'],
            'longitude' => $destinationGeocode['longitude']
        ];
        
        $showResults = true;
    } else {
        setErrorNotification('Could not geocode one or both locations. Please try different locations.');
    }
}

// Include header
include_once 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero gradient-hero textured">
    <div class="container">
        <div class="hero-content">
            <h1>Test Intercontinental Routing</h1>
            <p>Test the intercontinental routing functionality with different locations</p>
        </div>
    </div>
</section>

<!-- Test Form Section -->
<section class="test-form-section">
    <div class="container">
        <div class="test-box">
            <form class="test-form" method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                <div class="form-group">
                    <label for="test-origin">Origin Location</label>
                    <input type="text" id="test-origin" name="test_origin" placeholder="Enter origin location (e.g., New York, USA)" value="<?php echo htmlspecialchars($testOrigin); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="test-destination">Destination Location</label>
                    <input type="text" id="test-destination" name="test_destination" placeholder="Enter destination location (e.g., London, UK)" value="<?php echo htmlspecialchars($testDestination); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="test-preference">Routing Preference</label>
                    <select id="test-preference" name="test_preference">
                        <option value="auto" <?php echo $testPreference === 'auto' ? 'selected' : ''; ?>>Auto-detect</option>
                        <option value="air" <?php echo $testPreference === 'air' ? 'selected' : ''; ?>>Prefer Airports</option>
                        <option value="sea" <?php echo $testPreference === 'sea' ? 'selected' : ''; ?>>Prefer Seaports</option>
                    </select>
                </div>
                
                <button type="submit" class="btn primary-btn"><i class="fas fa-map-marked-alt"></i> Test Routing</button>
            </form>
            
            <div class="test-examples">
                <p>Example locations:</p>
                <ul>
                    <li>New York, USA → London, UK</li>
                    <li>Tokyo, Japan → Sydney, Australia</li>
                    <li>Cape Town, South Africa → Rio de Janeiro, Brazil</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<?php if($showResults): ?>
<!-- Test Results Section -->
<section class="test-results textured-section">
    <div class="container">
        <div class="results-header">
            <h2>Routing Test Results</h2>
            <p>Testing route from <strong><?php echo htmlspecialchars($testOrigin); ?></strong> to <strong><?php echo htmlspecialchars($testDestination); ?></strong> with <strong><?php echo ucfirst($testPreference); ?></strong> preference</p>
        </div>
        
        <!-- Create fake tracking updates for the map -->
        <script>
            const trackingUpdates = [
                {
                    id: 'origin',
                    shipment_id: 'test',
                    status: 'Shipment picked up',
                    location: '<?php echo htmlspecialchars($testOrigin); ?>',
                    timestamp: '<?php echo date('Y-m-d H:i:s'); ?>',
                    latitude: '<?php echo $originCoords['latitude']; ?>',
                    longitude: '<?php echo $originCoords['longitude']; ?>',
                    notes: 'Test origin point'
                },
                {
                    id: 'destination',
                    shipment_id: 'test',
                    status: 'In transit',
                    location: '<?php echo htmlspecialchars($testDestination); ?>',
                    timestamp: '<?php echo date('Y-m-d H:i:s', strtotime('+3 days')); ?>',
                    latitude: '<?php echo $destinationCoords['latitude']; ?>',
                    longitude: '<?php echo $destinationCoords['longitude']; ?>',
                    notes: 'Test destination point'
                }
            ];
            
            const shipmentData = {
                id: 'test',
                tracking_number: 'TEST123456',
                status: 'in_transit',
                origin: '<?php echo htmlspecialchars($testOrigin); ?>',
                destination: '<?php echo htmlspecialchars($testDestination); ?>',
                customer_name: 'Test Customer',
                estimated_delivery: '<?php echo date('Y-m-d H:i:s', strtotime('+3 days')); ?>'
            };
        </script>
        
        <!-- Tracking Map -->
        <div class="tracking-map-container">
            <h2>Route Visualization</h2>
            <div class="map-section-grid">
                <div id="tracking-map" style="height: 500px; width: 100%; position: relative; z-index: 1; background-color: #f8f9fa;"
                     data-tracking-updates='<?php echo json_encode($trackingUpdates); ?>'
                     data-shipment='<?php echo json_encode($shipmentData); ?>'
                     data-routing-preference='<?php echo $testPreference; ?>'
                     data-enable-intercontinental='1'></div>
                <div class="current-location-info">
                    <div class="location-card">
                        <h3>Test Information</h3>
                        
                        <div class="location-details">
                            <div class="location-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div>
                                    <h4>Origin</h4>
                                    <p><?php echo htmlspecialchars($testOrigin); ?></p>
                                    <p class="coordinates"><?php echo $originCoords['latitude']; ?>, <?php echo $originCoords['longitude']; ?></p>
                                </div>
                            </div>
                            
                            <div class="location-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div>
                                    <h4>Destination</h4>
                                    <p><?php echo htmlspecialchars($testDestination); ?></p>
                                    <p class="coordinates"><?php echo $destinationCoords['latitude']; ?>, <?php echo $destinationCoords['longitude']; ?></p>
                                </div>
                            </div>
                            
                            <div class="location-item">
                                <i class="fas fa-cog"></i>
                                <div>
                                    <h4>Routing Preference</h4>
                                    <p><?php echo ucfirst($testPreference); ?></p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Shipment Progress -->
                        <div class="shipment-progress">
                            <h4>Shipment Progress</h4>
                            <div class="progress-bar-container">
                                <div id="shipment-progress-bar" class="progress-bar" style="width: 0%"></div>
                            </div>
                            <div class="progress-labels">
                                <span>Origin</span>
                                <span id="progress-percentage">0%</span>
                                <span>Destination</span>
                            </div>
                        </div>
                        
                        <div class="test-actions">
                            <a href="test-intercontinental-routing.php" class="btn outline-btn">Test Another Route</a>
                            <a href="admin/tracking-settings.php" class="btn secondary-btn">Tracking Settings</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Initialize tracking map -->
<?php
    // Add Leaflet CSS and JS
    echo "<link rel='stylesheet' href='https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'>";
    echo "<link rel='stylesheet' href='https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css'>";
    echo "<script src='https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'></script>";
    echo "<script src='https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js'></script>";
    echo "<script src='https://unpkg.com/@mapbox/polyline@1.1.1/src/polyline.js'></script>";
    echo "<script src='https://unpkg.com/leaflet-control-geocoder@2.4.0/dist/Control.Geocoder.js'></script>";
    echo "<script src='https://unpkg.com/leaflet-polylinedecorator@1.6.0/dist/leaflet.polylineDecorator.js'></script>";
    echo "<script src='https://cdn.jsdelivr.net/npm/leaflet-ant-path@1.3.0/dist/leaflet-ant-path.min.js'></script>";
    echo "<script src='" . SITE_URL . "/assets/js/polyline.encoded.min.js'></script>";
    echo "<script src='" . SITE_URL . "/assets/js/notifications.js'></script>";
    echo "<script src='" . SITE_URL . "/assets/js/map-path-fix.js'></script>";
    echo "<script src='" . SITE_URL . "/assets/js/custom-ant-path.js'></script>";
    
    // Add custom polyline decoder function
    echo "<script>
        // Add polyline decoder if not available
        if (!L.Polyline.fromEncoded) {
            L.Polyline.fromEncoded = function(encoded, options) {
                return new L.Polyline(polyline.decode(encoded), options);
            };
        }
    </script>";
    
    // Load intercontinental routing scripts
    echo "<script src='" . SITE_URL . "/assets/js/intercontinental-routing.js'></script>";
    echo "<script>";
    readfile(__DIR__ . '/assets/js/intercontinental-tracking-map.js');
    echo "</script>";
?>
<?php endif; ?>

<style>
/* Additional styles for the test page */
.test-form-section {
    padding: 40px 0;
}

.test-box {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin-bottom: 30px;
}

.test-form {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.test-form .form-group:last-of-type {
    grid-column: span 1;
}

.test-form button {
    grid-column: span 3;
}

.test-examples {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.test-examples ul {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 10px;
    padding-left: 20px;
}

.results-header {
    text-align: center;
    margin-bottom: 30px;
}

.coordinates {
    font-family: monospace;
    font-size: 0.9em;
    color: #666;
}

.test-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

@media (max-width: 768px) {
    .test-form {
        grid-template-columns: 1fr;
    }
    
    .test-form .form-group:last-of-type {
        grid-column: span 1;
    }
    
    .test-form button {
        grid-column: span 1;
    }
}
</style>

<?php include_once 'includes/footer.php'; ?>
