<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Set up page
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Missing Tables - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .update-container {
            max-width: 800px;
            margin: 50px auto;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .update-header {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
        }
        
        .update-header h1 {
            margin: 0;
            font-size: 24px;
        }
        
        .update-header p {
            margin: 10px 0 0;
            opacity: 0.8;
        }
        
        .update-content {
            padding: 30px;
        }
        
        .log {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .log p {
            margin: 5px 0;
        }
        
        .log .success {
            color: #28a745;
            background: none;
            padding: 0;
        }
        
        .log .error {
            color: #dc3545;
            background: none;
            padding: 0;
        }
        
        .log .info {
            color: #0d6efd;
        }
        
        .update-footer {
            padding: 20px 30px;
            background-color: #f8f9fa;
            text-align: center;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="update-container">
        <div class="update-header">
            <h1><i class="fas fa-database"></i> Create Missing Tables</h1>
            <p>Creating geocoding_cache and system_settings tables</p>
        </div>

        <div class="update-content">
            <div class="log">
<?php
// Run the database update
try {
    global $conn;
    
    // Check if geocoding_cache table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'geocoding_cache'");
    $geocodingCacheExists = $stmt->rowCount() > 0;
    
    if (!$geocodingCacheExists) {
        // Create geocoding_cache table
        $createGeoCacheSQL = "CREATE TABLE geocoding_cache (
            id INT AUTO_INCREMENT PRIMARY KEY,
            query_type ENUM('geocode', 'reverse') NOT NULL,
            query_string VARCHAR(255) NOT NULL,
            result TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (query_type, query_string(191))
        )";
        
        if ($conn->exec($createGeoCacheSQL)) {
            echo "<p class='success'>✓ Successfully created geocoding_cache table.</p>";
        } else {
            echo "<p class='error'>✗ Failed to create geocoding_cache table.</p>";
        }
    } else {
        echo "<p class='info'>ℹ geocoding_cache table already exists.</p>";
    }
    
    // Check if system_settings table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'system_settings'");
    $systemSettingsExists = $stmt->rowCount() > 0;
    
    if (!$systemSettingsExists) {
        // Create system_settings table
        $createSettingsSQL = "CREATE TABLE system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(100) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_description TEXT,
            setting_group VARCHAR(50) DEFAULT 'general',
            setting_type VARCHAR(20) DEFAULT 'text',
            setting_label VARCHAR(100),
            is_public TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        if ($conn->exec($createSettingsSQL)) {
            echo "<p class='success'>✓ Successfully created system_settings table.</p>";
            
            // Insert default settings
            $defaultSettings = [
                // Geocoding settings
                [
                    'setting_key' => 'enable_geocoding',
                    'setting_value' => '1',
                    'setting_description' => 'Enable automatic geocoding of locations',
                    'setting_group' => 'geocoding',
                    'setting_type' => 'boolean',
                    'setting_label' => 'Enable Geocoding',
                    'is_public' => 1
                ],
                [
                    'setting_key' => 'geocoding_provider',
                    'setting_value' => 'nominatim',
                    'setting_description' => 'Preferred geocoding provider (nominatim, opencage, etc.)',
                    'setting_group' => 'geocoding',
                    'setting_type' => 'select',
                    'setting_label' => 'Geocoding Provider',
                    'is_public' => 1
                ],
                [
                    'setting_key' => 'opencage_api_key',
                    'setting_value' => '',
                    'setting_description' => 'API key for OpenCage Geocoder',
                    'setting_group' => 'geocoding',
                    'setting_type' => 'text',
                    'setting_label' => 'OpenCage API Key',
                    'is_public' => 0
                ],
                
                // General settings
                [
                    'setting_key' => 'company_name',
                    'setting_value' => 'TrackLogistics',
                    'setting_description' => 'Company name displayed throughout the site',
                    'setting_group' => 'general',
                    'setting_type' => 'text',
                    'setting_label' => 'Company Name',
                    'is_public' => 1
                ],
                [
                    'setting_key' => 'company_email',
                    'setting_value' => '<EMAIL>',
                    'setting_description' => 'Primary contact email for the company',
                    'setting_group' => 'general',
                    'setting_type' => 'email',
                    'setting_label' => 'Company Email',
                    'is_public' => 1
                ],
                [
                    'setting_key' => 'company_phone',
                    'setting_value' => '+****************',
                    'setting_description' => 'Primary contact phone for the company',
                    'setting_group' => 'general',
                    'setting_type' => 'text',
                    'setting_label' => 'Company Phone',
                    'is_public' => 1
                ],
                [
                    'setting_key' => 'company_address',
                    'setting_value' => '123 Shipping Lane, Logistics City, LC 12345',
                    'setting_description' => 'Physical address of the company',
                    'setting_group' => 'general',
                    'setting_type' => 'textarea',
                    'setting_label' => 'Company Address',
                    'is_public' => 1
                ],
                
                // System settings
                [
                    'setting_key' => 'maintenance_mode',
                    'setting_value' => '0',
                    'setting_description' => 'Enable maintenance mode to temporarily disable the site',
                    'setting_group' => 'system',
                    'setting_type' => 'boolean',
                    'setting_label' => 'Maintenance Mode',
                    'is_public' => 1
                ],
                
                // Notification settings
                [
                    'setting_key' => 'enable_email_notifications',
                    'setting_value' => '1',
                    'setting_description' => 'Enable email notifications for shipment updates',
                    'setting_group' => 'notifications',
                    'setting_type' => 'boolean',
                    'setting_label' => 'Enable Email Notifications',
                    'is_public' => 1
                ],
                [
                    'setting_key' => 'notification_sender_email',
                    'setting_value' => '<EMAIL>',
                    'setting_description' => 'Email address used as sender for notifications',
                    'setting_group' => 'notifications',
                    'setting_type' => 'email',
                    'setting_label' => 'Notification Sender Email',
                    'is_public' => 0
                ],
                [
                    'setting_key' => 'admin_email',
                    'setting_value' => '<EMAIL>',
                    'setting_description' => 'Email address for receiving admin notifications',
                    'setting_group' => 'notifications',
                    'setting_type' => 'email',
                    'setting_label' => 'Admin Email',
                    'is_public' => 0
                ]
            ];
            
            $insertStmt = $conn->prepare("INSERT INTO system_settings
                (setting_key, setting_value, setting_description, setting_group, setting_type, setting_label, is_public)
                VALUES (?, ?, ?, ?, ?, ?, ?)");
            
            $insertedCount = 0;
            foreach ($defaultSettings as $setting) {
                try {
                    $insertStmt->execute([
                        $setting['setting_key'],
                        $setting['setting_value'],
                        $setting['setting_description'],
                        $setting['setting_group'],
                        $setting['setting_type'],
                        $setting['setting_label'],
                        $setting['is_public']
                    ]);
                    $insertedCount++;
                } catch (PDOException $e) {
                    echo "<p class='error'>✗ Error inserting setting '{$setting['setting_key']}': " . $e->getMessage() . "</p>";
                }
            }
            
            echo "<p class='success'>✓ Added $insertedCount default settings to system_settings table.</p>";
        } else {
            echo "<p class='error'>✗ Failed to create system_settings table.</p>";
        }
    } else {
        echo "<p class='info'>ℹ system_settings table already exists.</p>";
        
        // Check if we need to add any missing settings
        $requiredSettings = [
            'enable_geocoding', 
            'geocoding_provider', 
            'opencage_api_key',
            'maintenance_mode'
        ];
        
        $missingSettings = [];
        foreach ($requiredSettings as $key) {
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM system_settings WHERE setting_key = ?");
            $stmt->execute([$key]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] == 0) {
                $missingSettings[] = $key;
            }
        }
        
        if (!empty($missingSettings)) {
            echo "<p class='info'>ℹ Found " . count($missingSettings) . " missing settings: " . implode(', ', $missingSettings) . "</p>";
            
            // Add missing geocoding settings
            if (in_array('enable_geocoding', $missingSettings)) {
                $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, setting_type, setting_label, is_public) 
                                      VALUES ('enable_geocoding', '1', 'Enable automatic geocoding of locations', 'geocoding', 'boolean', 'Enable Geocoding', 1)");
                if ($stmt->execute()) {
                    echo "<p class='success'>✓ Added missing setting: enable_geocoding</p>";
                }
            }
            
            if (in_array('geocoding_provider', $missingSettings)) {
                $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, setting_type, setting_label, is_public) 
                                      VALUES ('geocoding_provider', 'nominatim', 'Preferred geocoding provider', 'geocoding', 'select', 'Geocoding Provider', 1)");
                if ($stmt->execute()) {
                    echo "<p class='success'>✓ Added missing setting: geocoding_provider</p>";
                }
            }
            
            if (in_array('opencage_api_key', $missingSettings)) {
                $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, setting_type, setting_label, is_public) 
                                      VALUES ('opencage_api_key', '', 'API key for OpenCage Geocoder', 'geocoding', 'text', 'OpenCage API Key', 0)");
                if ($stmt->execute()) {
                    echo "<p class='success'>✓ Added missing setting: opencage_api_key</p>";
                }
            }
            
            if (in_array('maintenance_mode', $missingSettings)) {
                $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_description, setting_group, setting_type, setting_label, is_public) 
                                      VALUES ('maintenance_mode', '0', 'Enable maintenance mode', 'system', 'boolean', 'Maintenance Mode', 1)");
                if ($stmt->execute()) {
                    echo "<p class='success'>✓ Added missing setting: maintenance_mode</p>";
                }
            }
        } else {
            echo "<p class='info'>ℹ All required settings are present in the system_settings table.</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "<p class='error'>✗ Database error: " . $e->getMessage() . "</p>";
}
?>
            </div>
            
            <p>The database has been updated to include the geocoding_cache and system_settings tables. These tables are essential for the proper functioning of the geocoding features and system configuration.</p>
            
            <p><strong>Next Steps:</strong></p>
            <ul>
                <li>Go to the admin dashboard to configure system settings</li>
                <li>Set up geocoding settings in the admin panel</li>
                <li>Test the geocoding functionality with tracking updates</li>
            </ul>
        </div>

        <div class="update-footer">
            <a href="admin/index.php" class="btn">Go to Admin Dashboard</a>
        </div>
    </div>
</body>
</html>
