/* Maintenance page specific fixes */

/* Fix spacing between buttons in the hero section */
.admin-hero .admin-actions {
    gap: 40px; /* Significantly increased spacing */
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 25px;
    padding: 15px 0;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
}

/* Add specific styling to each button to ensure they stand out */
.admin-hero .admin-actions .btn {
    min-width: 200px; /* Ensure buttons have a good width */
    text-align: center;
    padding: 12px 24px; /* Larger padding */
    border-radius: 10px; /* Slightly rounded corners */
    font-weight: 500;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add subtle shadow */
    transition: all 0.3s ease;
}

/* Enhance hover effects */
.admin-hero .admin-actions .btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Specific styling for the legacy tools section */
.admin-hero .legacy-tools-action {
    margin-top: 20px; /* Add space between button groups */
}

/* Specific styling for the outline button (Show Legacy Tools) */
.admin-hero .admin-actions .outline-btn {
    border: 2px solid rgba(255, 255, 255, 0.3);
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    position: relative;
    backdrop-filter: blur(5px);
    width: auto; /* Allow the button to size to content */
    min-width: 220px; /* Ensure minimum width */
}

.admin-hero .admin-actions .outline-btn:hover {
    border-color: rgba(255, 255, 255, 0.5);
    background-color: rgba(255, 255, 255, 0.2);
}

/* Make buttons more responsive */
@media (max-width: 768px) {
    .admin-hero .admin-actions {
        gap: 30px; /* Still maintain good spacing on tablets */
        max-width: 100%;
    }

    .admin-hero .admin-actions .btn {
        min-width: 180px; /* Ensure buttons have a minimum width */
        margin-bottom: 15px;
        padding: 10px 20px;
    }

    .admin-hero .legacy-tools-action {
        margin-top: 10px; /* Less space needed on smaller screens */
    }
}

/* For very small screens */
@media (max-width: 480px) {
    .admin-hero .admin-actions {
        flex-direction: column;
        width: 100%;
        gap: 15px; /* Reduced gap for vertical stacking */
    }

    .admin-hero .admin-actions .btn {
        width: 100%;
        max-width: 300px; /* Limit maximum width on small screens */
        margin-left: auto;
        margin-right: auto;
    }
}
