<?php
/**
 * Geocoding Helper Class
 *
 * Provides methods for geocoding (converting addresses to coordinates) and
 * reverse geocoding (converting coordinates to addresses) using various providers.
 */
class GeocodingHelper {
    // Default provider (not used directly anymore, but kept for backward compatibility)
    private $provider;

    // API keys for different providers
    private $apiKeys = [];

    // Cache duration in seconds (default: 30 days)
    private $cacheDuration = 2592000;

    // Database connection
    private $db;

    /**
     * Constructor
     *
     * @param object $db Database connection object
     */
    public function __construct($db = null) {
        global $conn, $db;
        $this->db = $db ?: ($conn ?: $GLOBALS['db']);

        // Load API keys from settings
        $this->loadApiKeys();

        // Ensure the geocoding cache table exists
        $this->ensureCacheTableExists();
    }

    /**
     * Load API keys from system settings
     */
    private function loadApiKeys() {
        try {
            // Check if settings table exists
            $tableCheck = $this->db->query("SHOW TABLES LIKE 'system_settings'");
            $tableExists = $tableCheck && $tableCheck->rowCount() > 0;

            if (!$tableExists) {
                // Table doesn't exist yet, use defaults
                return;
            }

            // Get OpenCage API key from settings
            $stmt = $this->db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'opencage_api_key'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && !empty($result['setting_value'])) {
                $this->apiKeys['opencage'] = $result['setting_value'];
            }

            // Get preferred geocoding provider from settings
            $stmt = $this->db->prepare("SELECT setting_value FROM system_settings WHERE setting_key = 'geocoding_provider'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result && !empty($result['setting_value'])) {
                $this->provider = $result['setting_value'];
            }
        } catch (PDOException $e) {
            error_log("Error loading geocoding API keys: " . $e->getMessage());
        }
    }

    /**
     * Ensure the geocoding cache table exists
     */
    private function ensureCacheTableExists() {
        try {
            // Check if the table exists
            $stmt = $this->db->query("SHOW TABLES LIKE 'geocoding_cache'");
            if (!$stmt) {
                // Query failed, likely due to database connection issues
                error_log("Failed to check if geocoding_cache table exists");
                return;
            }

            $tableExists = $stmt->rowCount() > 0;

            if (!$tableExists) {
                // Create the cache table
                $this->db->query("CREATE TABLE geocoding_cache (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    query_type ENUM('geocode', 'reverse') NOT NULL,
                    query_string VARCHAR(255) NOT NULL,
                    result TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX (query_type, query_string(191))
                )");
                $this->db->execute();

                error_log("Created geocoding_cache table");
            }
        } catch (PDOException $e) {
            error_log("Error creating geocoding cache table: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Unexpected error with geocoding cache table: " . $e->getMessage());
        }
    }

    /**
     * Geocode an address to get coordinates
     *
     * @param string $address The address to geocode
     * @param bool $useCache Whether to use cached results
     * @return array|null Array with lat/lng or null if geocoding failed
     */
    public function geocode($address, $useCache = true) {
        if (empty($address)) {
            error_log("Geocoding failed: Empty address provided");
            return null;
        }

        try {
            // Normalize the address
            $address = trim($address);
            error_log("Geocoding address: $address");

            // Check cache first if enabled
            if ($useCache) {
                try {
                    $cachedResult = $this->getFromCache('geocode', $address);
                    if ($cachedResult !== null) {
                        error_log("Using cached geocoding result for: $address");
                        return $cachedResult;
                    }
                } catch (Exception $e) {
                    // If cache retrieval fails, continue with live geocoding
                    error_log("Cache retrieval failed: " . $e->getMessage());
                }
            }

            // Geocode using the preferred provider
            $result = null;

            // Try Nominatim first (it's free and doesn't require an API key)
            error_log("Attempting to geocode with Nominatim");
            $result = $this->geocodeWithNominatim($address);

            // If Nominatim fails and we have an OpenCage API key, try that
            if ($result === null && !empty($this->apiKeys['opencage'])) {
                error_log("Nominatim geocoding failed, trying OpenCage");
                $result = $this->geocodeWithOpenCage($address);
            }

            // Log the result
            if ($result === null) {
                error_log("All geocoding attempts failed for address: $address");
            } else {
                error_log("Successfully geocoded address: $address with provider: {$result['provider']}");
                error_log("Geocoding result: lat={$result['latitude']}, lng={$result['longitude']}");
            }

            // Cache the result if successful
            if ($result !== null && $useCache) {
                try {
                    $this->saveToCache('geocode', $address, $result);
                    error_log("Cached geocoding result for: $address");
                } catch (Exception $e) {
                    // If caching fails, just log the error and continue
                    error_log("Failed to cache geocoding result: " . $e->getMessage());
                }
            }

            return $result;
        } catch (Exception $e) {
            error_log("Geocoding failed with exception: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Reverse geocode coordinates to get an address
     *
     * @param float $latitude Latitude
     * @param float $longitude Longitude
     * @param bool $useCache Whether to use cached results
     * @return array|null Array with address components or null if reverse geocoding failed
     */
    public function reverseGeocode($latitude, $longitude, $useCache = true) {
        if (empty($latitude) || empty($longitude)) {
            error_log("Reverse geocoding failed: Empty coordinates provided");
            return null;
        }

        try {
            // Normalize coordinates
            $latitude = (float)$latitude;
            $longitude = (float)$longitude;

            // Validate coordinates
            if ($latitude < -90 || $latitude > 90 || $longitude < -180 || $longitude > 180) {
                error_log("Reverse geocoding failed: Invalid coordinates ($latitude, $longitude)");
                return null;
            }

            error_log("Reverse geocoding coordinates: $latitude, $longitude");

            // Create a cache key
            $cacheKey = "$latitude,$longitude";

            // Check cache first if enabled
            if ($useCache) {
                try {
                    $cachedResult = $this->getFromCache('reverse', $cacheKey);
                    if ($cachedResult !== null) {
                        error_log("Using cached reverse geocoding result for: $latitude, $longitude");
                        return $cachedResult;
                    }
                } catch (Exception $e) {
                    // If cache retrieval fails, continue with live geocoding
                    error_log("Cache retrieval failed: " . $e->getMessage());
                }
            }

            // Reverse geocode using the preferred provider
            $result = null;

            // Try Nominatim first (it's free and doesn't require an API key)
            error_log("Attempting to reverse geocode with Nominatim");
            $result = $this->reverseGeocodeWithNominatim($latitude, $longitude);

            // If Nominatim fails and we have an OpenCage API key, try that
            if ($result === null && !empty($this->apiKeys['opencage'])) {
                error_log("Nominatim reverse geocoding failed, trying OpenCage");
                $result = $this->reverseGeocodeWithOpenCage($latitude, $longitude);
            }

            // Log the result
            if ($result === null) {
                error_log("All reverse geocoding attempts failed for coordinates: $latitude, $longitude");
            } else {
                error_log("Successfully reverse geocoded coordinates: $latitude, $longitude with provider: {$result['provider']}");
                error_log("Reverse geocoding result: {$result['formatted_address']}");
            }

            // Cache the result if successful
            if ($result !== null && $useCache) {
                try {
                    $this->saveToCache('reverse', $cacheKey, $result);
                    error_log("Cached reverse geocoding result for: $latitude, $longitude");
                } catch (Exception $e) {
                    // If caching fails, just log the error and continue
                    error_log("Failed to cache reverse geocoding result: " . $e->getMessage());
                }
            }

            return $result;
        } catch (Exception $e) {
            error_log("Reverse geocoding failed with exception: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Geocode an address using OpenCage Geocoder
     *
     * @param string $address The address to geocode
     * @return array|null Array with lat/lng or null if geocoding failed
     */
    private function geocodeWithOpenCage($address) {
        // Check if we have an API key
        if (empty($this->apiKeys['opencage'])) {
            error_log("OpenCage API key not found");
            return null;
        }

        try {
            // Build the API URL
            $url = 'https://api.opencagedata.com/geocode/v1/json?' . http_build_query([
                'q' => $address,
                'key' => $this->apiKeys['opencage'],
                'limit' => 1,
                'no_annotations' => 1
            ]);

            // Make the request
            $response = $this->makeHttpRequest($url);

            if ($response && isset($response['results']) && !empty($response['results'])) {
                $result = $response['results'][0];

                return [
                    'latitude' => $result['geometry']['lat'],
                    'longitude' => $result['geometry']['lng'],
                    'formatted_address' => $result['formatted'],
                    'components' => $result['components'] ?? [],
                    'confidence' => $result['confidence'] ?? 0,
                    'provider' => 'opencage'
                ];
            }
        } catch (Exception $e) {
            error_log("Error geocoding with OpenCage: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Geocode an address using Nominatim
     *
     * @param string $address The address to geocode
     * @return array|null Array with lat/lng or null if geocoding failed
     */
    private function geocodeWithNominatim($address) {
        try {
            // Build the API URL
            $url = 'https://nominatim.openstreetmap.org/search?' . http_build_query([
                'q' => $address,
                'format' => 'json',
                'limit' => 1,
                'addressdetails' => 1
            ]);

            error_log("Nominatim geocoding URL: $url");

            // Add a custom user agent as required by Nominatim's usage policy
            $options = [
                'http' => [
                    'header' => 'User-Agent: TrackingSite/1.0 (https://tracklogistics.com)'
                ]
            ];

            // Make the request
            $response = $this->makeHttpRequest($url, $options);

            if ($response === null) {
                error_log("Nominatim geocoding response is null");
                return null;
            }

            if (empty($response)) {
                error_log("Nominatim geocoding response is empty");
                return null;
            }

            error_log("Nominatim geocoding response: " . print_r($response, true));

            // Check if we have a valid result
            if (!isset($response[0]) || !isset($response[0]['lat']) || !isset($response[0]['lon'])) {
                error_log("Nominatim geocoding response missing required fields");
                return null;
            }

            $result = $response[0];

            return [
                'latitude' => (float)$result['lat'],
                'longitude' => (float)$result['lon'],
                'formatted_address' => $result['display_name'],
                'components' => $result['address'] ?? [],
                'provider' => 'nominatim'
            ];
        } catch (Exception $e) {
            error_log("Error geocoding with Nominatim: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Reverse geocode coordinates using OpenCage Geocoder
     *
     * @param float $latitude Latitude
     * @param float $longitude Longitude
     * @return array|null Array with address components or null if reverse geocoding failed
     */
    private function reverseGeocodeWithOpenCage($latitude, $longitude) {
        // Check if we have an API key
        if (empty($this->apiKeys['opencage'])) {
            error_log("OpenCage API key not found");
            return null;
        }

        try {
            // Build the API URL
            $url = 'https://api.opencagedata.com/geocode/v1/json?' . http_build_query([
                'q' => "$latitude,$longitude",
                'key' => $this->apiKeys['opencage'],
                'limit' => 1,
                'no_annotations' => 1
            ]);

            // Make the request
            $response = $this->makeHttpRequest($url);

            if ($response && isset($response['results']) && !empty($response['results'])) {
                $result = $response['results'][0];

                return [
                    'formatted_address' => $result['formatted'],
                    'components' => $result['components'] ?? [],
                    'provider' => 'opencage'
                ];
            }
        } catch (Exception $e) {
            error_log("Error reverse geocoding with OpenCage: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Reverse geocode coordinates using Nominatim
     *
     * @param float $latitude Latitude
     * @param float $longitude Longitude
     * @return array|null Array with address components or null if reverse geocoding failed
     */
    private function reverseGeocodeWithNominatim($latitude, $longitude) {
        try {
            // Build the API URL
            $url = 'https://nominatim.openstreetmap.org/reverse?' . http_build_query([
                'lat' => $latitude,
                'lon' => $longitude,
                'format' => 'json',
                'addressdetails' => 1,
                'zoom' => 18 // Higher zoom level for more detailed results
            ]);

            error_log("Nominatim reverse geocoding URL: $url");

            // Add a custom user agent as required by Nominatim's usage policy
            $options = [
                'http' => [
                    'header' => 'User-Agent: TrackingSite/1.0 (https://tracklogistics.com)'
                ]
            ];

            // Make the request
            $response = $this->makeHttpRequest($url, $options);

            if ($response === null) {
                error_log("Nominatim reverse geocoding response is null");
                return null;
            }

            error_log("Nominatim reverse geocoding response: " . print_r($response, true));

            // Check if we have a valid result
            if (!isset($response['display_name'])) {
                error_log("Nominatim reverse geocoding response missing display_name");
                return null;
            }

            // Extract address components
            $components = $response['address'] ?? [];

            // Create a more structured result
            return [
                'formatted_address' => $response['display_name'],
                'components' => $components,
                'city' => $components['city'] ?? $components['town'] ?? $components['village'] ?? null,
                'state' => $components['state'] ?? $components['province'] ?? null,
                'country' => $components['country'] ?? null,
                'postal_code' => $components['postcode'] ?? null,
                'provider' => 'nominatim'
            ];
        } catch (Exception $e) {
            error_log("Error reverse geocoding with Nominatim: " . $e->getMessage());
        }

        return null;
    }

    /**
     * Make an HTTP request and return the JSON response
     *
     * @param string $url The URL to request
     * @param array $options Additional options for the request
     * @return array|null The JSON response as an array or null on failure
     */
    private function makeHttpRequest($url, $options = []) {
        try {
            error_log("Making HTTP request to: $url");

            // Always use cURL for HTTP requests
            if (!function_exists('curl_init')) {
                error_log("cURL is not available. Cannot make HTTP request.");
                return null;
            }

            // Initialize cURL session
            $ch = curl_init();

            // Set the URL
            curl_setopt($ch, CURLOPT_URL, $url);

            // Set default cURL options
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true); // Return the response instead of outputting it
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Increase timeout to 30 seconds
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Disable SSL verification for local development
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true); // Follow redirects
            curl_setopt($ch, CURLOPT_MAXREDIRS, 5); // Maximum number of redirects to follow
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // Connection timeout in seconds

            // Set user agent - Nominatim requires a unique user agent
            $userAgent = 'TrackingSite/1.0 (https://tracklogistics.com)';
            if (!empty($options['http']['header'])) {
                // Extract user agent from header if provided
                if (preg_match('/User-Agent: ([^\r\n]+)/i', $options['http']['header'], $matches)) {
                    $userAgent = $matches[1];
                }
            }
            curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
            error_log("Using User-Agent: $userAgent");

            // Add a small delay to avoid hitting rate limits
            usleep(250000); // 250ms delay

            // Execute the request
            $response = curl_exec($ch);

            // Check for errors
            if ($response === false) {
                $errorMsg = curl_error($ch);
                $errorCode = curl_errno($ch);
                error_log("cURL error: $errorMsg (Code: $errorCode)");
                curl_close($ch);
                return null;
            }

            // Get HTTP status code
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            error_log("cURL HTTP status code: $httpCode");

            // Close cURL
            curl_close($ch);

            // Check for HTTP errors
            if ($httpCode >= 400) {
                error_log("HTTP error: $httpCode, Response: $response");
                return null;
            }

            error_log("Received response length: " . strlen($response));
            error_log("Response preview: " . substr($response, 0, 100) . (strlen($response) > 100 ? '...' : ''));

            // Decode the JSON response
            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                error_log("JSON decode error: " . json_last_error_msg());
                error_log("Raw response: $response");
                return null;
            }

            return $data;
        } catch (Exception $e) {
            error_log("HTTP request error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get a cached geocoding result
     *
     * @param string $type The query type ('geocode' or 'reverse')
     * @param string $query The query string
     * @return array|null The cached result or null if not found or expired
     * @throws Exception If there's an error accessing the cache
     */
    private function getFromCache($type, $query) {
        try {
            // Check if the cache table exists
            $tableCheck = $this->db->query("SHOW TABLES LIKE 'geocoding_cache'");
            if (!$tableCheck || $tableCheck->rowCount() == 0) {
                // Table doesn't exist yet
                return null;
            }

            // Calculate the expiration timestamp
            $expirationTime = date('Y-m-d H:i:s', time() - $this->cacheDuration);

            // Query the cache
            $stmt = $this->db->prepare("
                SELECT result
                FROM geocoding_cache
                WHERE query_type = ?
                AND query_string = ?
                AND created_at > ?
            ");

            if (!$stmt) {
                throw new Exception("Failed to prepare cache query");
            }

            $success = $stmt->execute([$type, $query, $expirationTime]);
            if (!$success) {
                throw new Exception("Failed to execute cache query");
            }

            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result) {
                $decoded = json_decode($result['result'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new Exception("Failed to decode cached result: " . json_last_error_msg());
                }
                return $decoded;
            }
        } catch (PDOException $e) {
            error_log("Database error getting from geocoding cache: " . $e->getMessage());
            throw new Exception("Cache access error: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error getting from geocoding cache: " . $e->getMessage());
            throw $e; // Re-throw the exception for the caller to handle
        }

        return null;
    }

    /**
     * Save a geocoding result to the cache
     *
     * @param string $type The query type ('geocode' or 'reverse')
     * @param string $query The query string
     * @param array $result The result to cache
     * @return bool Whether the operation was successful
     * @throws Exception If there's an error saving to the cache
     */
    private function saveToCache($type, $query, $result) {
        try {
            // Check if the cache table exists
            $tableCheck = $this->db->query("SHOW TABLES LIKE 'geocoding_cache'");
            if (!$tableCheck || $tableCheck->rowCount() == 0) {
                // Try to create the table
                $this->ensureCacheTableExists();

                // Check again if the table exists
                $tableCheck = $this->db->query("SHOW TABLES LIKE 'geocoding_cache'");
                if (!$tableCheck || $tableCheck->rowCount() == 0) {
                    throw new Exception("Cache table does not exist and could not be created");
                }
            }

            // Encode the result as JSON
            $jsonResult = json_encode($result);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Failed to encode result as JSON: " . json_last_error_msg());
            }

            // Check if this query is already in the cache
            $stmt = $this->db->prepare("
                SELECT id
                FROM geocoding_cache
                WHERE query_type = ?
                AND query_string = ?
            ");

            if (!$stmt) {
                throw new Exception("Failed to prepare cache query");
            }

            $success = $stmt->execute([$type, $query]);
            if (!$success) {
                throw new Exception("Failed to execute cache query");
            }

            $existing = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($existing) {
                // Update the existing cache entry
                $stmt = $this->db->prepare("
                    UPDATE geocoding_cache
                    SET result = ?, created_at = NOW()
                    WHERE id = ?
                ");

                if (!$stmt) {
                    throw new Exception("Failed to prepare cache update query");
                }

                $success = $stmt->execute([$jsonResult, $existing['id']]);
                if (!$success) {
                    throw new Exception("Failed to execute cache update query");
                }

                return true;
            } else {
                // Insert a new cache entry
                $stmt = $this->db->prepare("
                    INSERT INTO geocoding_cache (query_type, query_string, result)
                    VALUES (?, ?, ?)
                ");

                if (!$stmt) {
                    throw new Exception("Failed to prepare cache insert query");
                }

                $success = $stmt->execute([$type, $query, $jsonResult]);
                if (!$success) {
                    throw new Exception("Failed to execute cache insert query");
                }

                return true;
            }
        } catch (PDOException $e) {
            error_log("Database error saving to geocoding cache: " . $e->getMessage());
            throw new Exception("Cache save error: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("Error saving to geocoding cache: " . $e->getMessage());
            throw $e; // Re-throw the exception for the caller to handle
        }
    }

    /**
     * Clear the geocoding cache
     *
     * @param string $type Optional query type to clear ('geocode', 'reverse', or null for all)
     * @return bool Whether the operation was successful
     */
    public function clearCache($type = null) {
        try {
            // Check if the cache table exists
            $tableCheck = $this->db->query("SHOW TABLES LIKE 'geocoding_cache'");
            if (!$tableCheck || $tableCheck->rowCount() == 0) {
                // Table doesn't exist, nothing to clear
                return true;
            }

            if ($type) {
                $stmt = $this->db->prepare("DELETE FROM geocoding_cache WHERE query_type = ?");
                if (!$stmt) {
                    error_log("Failed to prepare cache clear query");
                    return false;
                }

                $success = $stmt->execute([$type]);
                if (!$success) {
                    error_log("Failed to execute cache clear query");
                    return false;
                }

                return true;
            } else {
                $stmt = $this->db->prepare("DELETE FROM geocoding_cache");
                if (!$stmt) {
                    error_log("Failed to prepare cache clear all query");
                    return false;
                }

                $success = $stmt->execute();
                if (!$success) {
                    error_log("Failed to execute cache clear all query");
                    return false;
                }

                return true;
            }
        } catch (PDOException $e) {
            error_log("Database error clearing geocoding cache: " . $e->getMessage());
            return false;
        } catch (Exception $e) {
            error_log("Error clearing geocoding cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Format a location string from components
     *
     * @param array $components Address components
     * @return string Formatted location string
     */
    public function formatLocation($components) {
        // Validate input
        if (!is_array($components)) {
            error_log("Invalid components provided to formatLocation: not an array");
            return '';
        }

        try {
            $parts = [];

            // Add city/town/village
            if (!empty($components['city'])) {
                $parts[] = $components['city'];
            } elseif (!empty($components['town'])) {
                $parts[] = $components['town'];
            } elseif (!empty($components['village'])) {
                $parts[] = $components['village'];
            }

            // Add state/province
            if (!empty($components['state'])) {
                $parts[] = $components['state'];
            } elseif (!empty($components['province'])) {
                $parts[] = $components['province'];
            } elseif (!empty($components['county'])) {
                $parts[] = $components['county'];
            }

            // Add country
            if (!empty($components['country'])) {
                $parts[] = $components['country'];
            }

            // If no parts were found, try to use the formatted address directly
            if (empty($parts) && !empty($components['formatted_address'])) {
                return $components['formatted_address'];
            }

            return implode(', ', $parts);
        } catch (Exception $e) {
            error_log("Error formatting location: " . $e->getMessage());
            return '';
        }
    }

    /**
     * Get the distance between two points in kilometers
     *
     * @param float $lat1 Latitude of the first point
     * @param float $lon1 Longitude of the first point
     * @param float $lat2 Latitude of the second point
     * @param float $lon2 Longitude of the second point
     * @return float Distance in kilometers, or 0 if invalid coordinates
     */
    public function getDistance($lat1, $lon1, $lat2, $lon2) {
        // Validate inputs
        if (!is_numeric($lat1) || !is_numeric($lon1) || !is_numeric($lat2) || !is_numeric($lon2)) {
            error_log("Invalid coordinates provided to getDistance");
            return 0;
        }

        // Validate coordinate ranges
        if ($lat1 < -90 || $lat1 > 90 || $lat2 < -90 || $lat2 > 90 ||
            $lon1 < -180 || $lon1 > 180 || $lon2 < -180 || $lon2 > 180) {
            error_log("Coordinates out of range in getDistance");
            return 0;
        }

        try {
            // Convert degrees to radians
            $lat1 = deg2rad((float)$lat1);
            $lon1 = deg2rad((float)$lon1);
            $lat2 = deg2rad((float)$lat2);
            $lon2 = deg2rad((float)$lon2);

            // Haversine formula
            $dlat = $lat2 - $lat1;
            $dlon = $lon2 - $lon1;
            $a = sin($dlat/2) * sin($dlat/2) + cos($lat1) * cos($lat2) * sin($dlon/2) * sin($dlon/2);
            $c = 2 * atan2(sqrt($a), sqrt(1-$a));
            $distance = 6371 * $c; // Earth's radius in km

            return $distance;
        } catch (Exception $e) {
            error_log("Error calculating distance: " . $e->getMessage());
            return 0;
        }
    }
}
