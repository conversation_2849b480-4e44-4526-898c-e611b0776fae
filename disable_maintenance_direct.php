<?php
// Include configuration and database connection
require_once 'includes/config.php';
require_once 'includes/db.php';

try {
    // Update the maintenance_mode setting to '0' (disabled)
    $updateStmt = $conn->prepare("UPDATE system_settings SET setting_value = '0', updated_at = NOW() WHERE setting_key = 'maintenance_mode'");
    $result = $updateStmt->execute();
    
    if($result) {
        echo "<h2>Success!</h2>";
        echo "<p>Maintenance mode has been disabled.</p>";
        echo "<p><a href='index.php'>Return to homepage</a></p>";
    } else {
        echo "<h2>Error</h2>";
        echo "<p>Failed to disable maintenance mode.</p>";
    }
} catch (PDOException $e) {
    echo "<h2>Database Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
