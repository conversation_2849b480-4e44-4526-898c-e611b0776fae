<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

echo "<h1>Force Update Shipment Status</h1>";

try {
    // Get current status counts
    $stmt = $conn->query("SELECT status, COUNT(*) as count FROM shipments GROUP BY status");
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Status Counts:</h2>";
    echo "<ul>";
    foreach ($results as $row) {
        echo "<li><strong>" . htmlspecialchars($row['status']) . ":</strong> " . $row['count'] . "</li>";
    }
    echo "</ul>";
    
    // Update some pending shipments to in_transit
    $updateStmt = $conn->prepare("UPDATE shipments SET status = 'in_transit' WHERE status = 'pending' LIMIT 5");
    $updateStmt->execute();
    $updatedCount = $updateStmt->rowCount();
    
    echo "<p>Updated $updatedCount pending shipments to in_transit status.</p>";
    
    // Get updated status counts
    $stmt = $conn->query("SELECT status, COUNT(*) as count FROM shipments GROUP BY status");
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Updated Status Counts:</h2>";
    echo "<ul>";
    foreach ($results as $row) {
        echo "<li><strong>" . htmlspecialchars($row['status']) . ":</strong> " . $row['count'] . "</li>";
    }
    echo "</ul>";
    
    // Test the dashboard query
    $statsQuery = $conn->query("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as `delayed`,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM shipments");
    $stats = $statsQuery->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Dashboard Query Results:</h2>";
    echo "<ul>";
    foreach ($stats as $key => $value) {
        echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . $value . "</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='admin/index.php'>Go to Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
