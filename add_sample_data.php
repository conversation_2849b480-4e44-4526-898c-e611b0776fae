<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Sample Data - TransLogix</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="page-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: var(--glass-bg);
            border-radius: 16px;
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            text-align: center;
        }

        h2 {
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        p {
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .icon {
            font-size: 48px;
            color: var(--primary-color);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon"><i class="fas fa-database"></i></div>

<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Check if sample data already exists
$db->query("SELECT COUNT(*) as count FROM shipments");
$result = $db->single();

if ($result['count'] > 0) {
    echo "<h2>Sample Data Already Exists</h2>";
    echo "<p>There are already {$result['count']} shipments in the database.</p>";
    echo "<a href='admin/index.php' class='btn'>Go to Admin Dashboard</a>";
    echo "</div></body></html>";
    exit;
}

// Sample data
$statuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
$origins = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Miami', 'Seattle'];
$destinations = ['London', 'Paris', 'Tokyo', 'Sydney', 'Berlin', 'Toronto', 'Dubai'];
$customers = ['John Smith', 'Jane Doe', 'Robert Johnson', 'Emily Davis', 'Michael Brown', 'Sarah Wilson'];

// Generate 50 sample shipments
$count = 0;
for ($i = 0; $i < 50; $i++) {
    $status = $statuses[array_rand($statuses)];
    $origin = $origins[array_rand($origins)];
    $destination = $destinations[array_rand($destinations)];
    $customer = $customers[array_rand($customers)];

    // Generate tracking number
    $tracking_number = 'TL' . str_pad(mt_rand(1000, 9999), 4, '0', STR_PAD_LEFT);

    // Generate dates
    $created_at = date('Y-m-d H:i:s', strtotime('-' . mt_rand(1, 60) . ' days'));
    $delivered_at = null;

    if ($status == 'delivered') {
        $delivered_at = date('Y-m-d H:i:s', strtotime($created_at . ' +' . mt_rand(2, 10) . ' days'));
    }

    // Insert shipment
    $db->query("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, delivered_at)
                VALUES (:tracking_number, :customer_name, :origin, :destination, :status, :created_at, :delivered_at)");

    $db->bind(':tracking_number', $tracking_number);
    $db->bind(':customer_name', $customer);
    $db->bind(':origin', $origin);
    $db->bind(':destination', $destination);
    $db->bind(':status', $status);
    $db->bind(':created_at', $created_at);
    $db->bind(':delivered_at', $delivered_at);

    if ($db->execute()) {
        $count++;
    }
}

echo "<h2>Sample Data Added</h2>";
echo "<p>Successfully added $count sample shipments to the database.</p>";
echo "<a href='admin/index.php' class='btn'>Go to Admin Dashboard</a>";
echo "</div></body></html>";
?>
