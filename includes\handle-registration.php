<?php
require_once 'config.php';
require_once 'db.php';
require_once 'functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    setErrorNotification('Invalid request method');
    redirect('../register.php');
    exit;
}

// Get form data
$username = isset($_POST['username']) ? sanitize($_POST['username']) : '';
$email = isset($_POST['email']) ? sanitize($_POST['email']) : '';
$password = isset($_POST['password']) ? $_POST['password'] : '';
$confirmPassword = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';

// Validate input
if (empty($username) || empty($email) || empty($password) || empty($confirmPassword)) {
    setErrorNotification('All fields are required');
    redirect('../register.php');
    exit;
}

// Validate email format
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    setErrorNotification('Invalid email format');
    redirect('../register.php');
    exit;
}

// Check if passwords match
if ($password !== $confirmPassword) {
    setErrorNotification('Passwords do not match');
    redirect('../register.php');
    exit;
}

// Check password length
if (strlen($password) < 8) {
    setErrorNotification('Password must be at least 8 characters long');
    redirect('../register.php');
    exit;
}

try {
    global $conn;
    
    // Check if username already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$username]);
    if ($stmt->rowCount() > 0) {
        setErrorNotification('Username already exists');
        redirect('../register.php');
        exit;
    }
    
    // Check if email already exists
    $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->rowCount() > 0) {
        setErrorNotification('Email already exists');
        redirect('../register.php');
        exit;
    }
    
    // Hash password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Insert new user
    $stmt = $conn->prepare("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, 'customer')");
    $result = $stmt->execute([$username, $email, $hashedPassword]);
    
    if ($result) {
        // Get the new user's ID
        $userId = $conn->lastInsertId();
        
        // Set session variables to log the user in
        $_SESSION['user_id'] = $userId;
        $_SESSION['username'] = $username;
        $_SESSION['user_role'] = 'customer';
        
        // Set success message
        setSuccessNotification('Registration successful! Welcome to our platform.');
        
        // Redirect to user dashboard
        redirect('../user/index.php');
    } else {
        setErrorNotification('Registration failed. Please try again.');
        redirect('../register.php');
    }
} catch (PDOException $e) {
    error_log("Registration error: " . $e->getMessage());
    setErrorNotification('An error occurred during registration. Please try again later.');
    redirect('../register.php');
}
?>
