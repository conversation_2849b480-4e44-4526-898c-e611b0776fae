<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Set page title
$pageTitle = 'Track Your Shipment';

// Add tracking CSS
$additionalCss = [
    SITE_URL . '/assets/css/tracking.css',
    SITE_URL . '/assets/css/enhanced-tracking.css',
    SITE_URL . '/assets/css/tracking-hero.css',
    SITE_URL . '/assets/css/status-styles.css',
    SITE_URL . '/assets/css/intercontinental-tracking.css' // Add new CSS for intercontinental tracking
];

// Process tracking form
$shipment = null;
$trackingUpdates = null;
$showResults = false;
$mapCenter = null;

if(isset($_GET['tracking_number']) && !empty($_GET['tracking_number'])) {
    $trackingNumber = sanitize($_GET['tracking_number']);
    $shipment = getShipmentByTrackingNumber($trackingNumber);

    if($shipment) {
        $trackingUpdates = getTrackingUpdates($shipment['id']);
        $showResults = true;

        // If we don't have tracking updates but have origin and destination, create basic updates
        if (empty($trackingUpdates) && !empty($shipment['origin']) && !empty($shipment['destination'])) {
            // Create basic origin update
            $originUpdate = [
                'id' => 'origin',
                'shipment_id' => $shipment['id'],
                'status' => 'Shipment picked up',
                'location' => $shipment['origin'],
                'timestamp' => $shipment['created_at'] ?? date('Y-m-d H:i:s'),
                'latitude' => null,
                'longitude' => null,
                'notes' => 'Shipment has been picked up from origin'
            ];

            // Create basic destination update
            $destinationUpdate = [
                'id' => 'destination',
                'shipment_id' => $shipment['id'],
                'status' => $shipment['status'] === 'delivered' ? 'Delivered' : 'In transit',
                'location' => $shipment['destination'],
                'timestamp' => $shipment['estimated_delivery'] ?? date('Y-m-d H:i:s', strtotime('+3 days')),
                'latitude' => null,
                'longitude' => null,
                'notes' => $shipment['status'] === 'delivered' ? 'Shipment has been delivered to destination' : 'Shipment is en route to destination'
            ];

            // Use geocoding to get real coordinates
            require_once '../includes/geocoding.php';
            $geocoder = new GeocodingHelper();

            // Geocode origin
            $originGeocode = $geocoder->geocode($shipment['origin']);
            if ($originGeocode) {
                $originUpdate['latitude'] = $originGeocode['latitude'];
                $originUpdate['longitude'] = $originGeocode['longitude'];
            } else {
                // Fallback to default coordinates if geocoding fails
                $originUpdate['latitude'] = '40.7128';  // New York
                $originUpdate['longitude'] = '-74.0060';
            }

            // Geocode destination
            $destinationGeocode = $geocoder->geocode($shipment['destination']);
            if ($destinationGeocode) {
                $destinationUpdate['latitude'] = $destinationGeocode['latitude'];
                $destinationUpdate['longitude'] = $destinationGeocode['longitude'];
            } else {
                // Fallback to default coordinates if geocoding fails
                $destinationUpdate['latitude'] = '34.0522';  // Los Angeles
                $destinationUpdate['longitude'] = '-118.2437';
            }

            $trackingUpdates = [$originUpdate, $destinationUpdate];

            // Save these updates to the database for future use
            if (getSetting('auto_save_geocoded_updates', '1') === '1') {
                addTrackingUpdate(
                    $shipment['id'],
                    $originUpdate['location'],
                    $originUpdate['status'],
                    $originUpdate['latitude'],
                    $originUpdate['longitude'],
                    $originUpdate['notes']
                );

                // Only add destination update if it's different from origin
                if ($shipment['origin'] != $shipment['destination']) {
                    addTrackingUpdate(
                        $shipment['id'],
                        $destinationUpdate['location'],
                        $destinationUpdate['status'],
                        $destinationUpdate['latitude'],
                        $destinationUpdate['longitude'],
                        $destinationUpdate['notes']
                    );
                }
            }
        }

        // Get map center coordinates from the latest update with coordinates
        if (!empty($trackingUpdates)) {
            foreach ($trackingUpdates as $update) {
                if (!empty($update['latitude']) && !empty($update['longitude'])) {
                    $mapCenter = [
                        'lat' => $update['latitude'],
                        'lng' => $update['longitude'],
                        'location' => $update['location'],
                        'status' => $update['status']
                    ];
                    break;
                }
            }
        }
    } else {
        setErrorNotification('No shipment found with tracking number: ' . $trackingNumber, 8000);
    }
}

// Include header
include_once '../includes/header.php';
?>

<!-- Hero Section -->
<section class="hero gradient-hero textured">
    <div class="container">
        <div class="hero-content">
            <h1>Track Your Shipment</h1>
            <p>Enter your tracking number to get real-time updates on your shipment</p>
            <div class="cta-buttons">
                <a href="#tracking-form" class="btn primary-btn glass-btn"><i class="fas fa-search"></i> Track Now</a>
                <a href="<?php echo SITE_URL; ?>/contact.php" class="btn secondary-btn glass-btn"><i class="fas fa-headset"></i> Need Help?</a>
            </div>
        </div>
    </div>
</section>

<!-- Tracking Form Section -->
<section id="tracking-form" class="tracking-form-section">
    <div class="container">
        <div class="tracking-box">
            <form class="tracking-form" method="GET" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                <div class="form-group">
                    <label for="tracking-number">Tracking Number</label>
                    <input type="text" id="tracking-number" name="tracking_number" placeholder="Enter your tracking number" value="<?php echo isset($_GET['tracking_number']) ? htmlspecialchars($_GET['tracking_number']) : ''; ?>" required>
                </div>
                <button type="submit" class="btn primary-btn"><i class="fas fa-search"></i> Track</button>
            </form>
            <div class="tracking-examples">
                <p>Example tracking numbers:
                    <?php
                    // Get some sample tracking numbers from the database
                    try {
                        $sampleQuery = $conn->query("SELECT tracking_number FROM shipments WHERE status != 'delivered' LIMIT 3");
                        $samples = $sampleQuery->fetchAll(PDO::FETCH_COLUMN);

                        if (!empty($samples)) {
                            foreach ($samples as $index => $sample) {
                                echo '<a href="?tracking_number=' . htmlspecialchars($sample) . '">' . htmlspecialchars($sample) . '</a>';
                                if ($index < count($samples) - 1) {
                                    echo ', ';
                                }
                            }
                        }
                    } catch (PDOException $e) {
                        // Silently fail and use default examples
                        echo '<a href="?tracking_number=TL1234567">TL1234567</a>, <a href="?tracking_number=TL7654321">TL7654321</a>';
                    }
                    ?>
                </p>
            </div>
        </div>
    </div>
</section>

<?php if($showResults && $shipment): ?>
<!-- Tracking Results Section -->
<section class="tracking-results textured-section">
    <div class="container">
        <div class="shipment-details">
            <h2>Shipment Details</h2>

            <div class="details-grid">
                <div class="detail-card">
                    <h3>Tracking Number</h3>
                    <p><?php echo $shipment['tracking_number']; ?></p>
                </div>

                <div class="detail-card">
                    <h3>Status</h3>
                    <p class="status-badge <?php echo getStatusClass($shipment['status']); ?>"><?php echo ucfirst($shipment['status']); ?></p>
                </div>

                <?php if (!empty($shipment['shipment_name'])): ?>
                <div class="detail-card">
                    <h3>Shipment Name</h3>
                    <p><?php echo $shipment['shipment_name']; ?></p>
                </div>
                <?php endif; ?>

                <div class="detail-card">
                    <h3>Origin</h3>
                    <p><?php echo $shipment['origin']; ?></p>
                </div>

                <div class="detail-card">
                    <h3>Destination</h3>
                    <p><?php echo $shipment['destination']; ?></p>
                </div>

                <div class="detail-card">
                    <h3>Customer</h3>
                    <p><?php echo $shipment['customer_name']; ?></p>
                </div>

                <div class="detail-card">
                    <h3>Estimated Delivery</h3>
                    <p><?php echo $shipment['estimated_delivery'] ? date('F j, Y', strtotime($shipment['estimated_delivery'])) : 'Not available'; ?></p>
                </div>

                <?php if (!empty($shipment['shipment_description'])): ?>
                <div class="detail-card full-width">
                    <h3>Shipment Description</h3>
                    <p><?php echo $shipment['shipment_description']; ?></p>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tracking Map -->
        <div class="tracking-map-container">
            <h2>Shipment Location</h2>
            <div class="map-section-grid">
                <div id="tracking-map" style="height: 500px; width: 100%; position: relative; z-index: 5; background-color: #f8f9fa;"
                     data-tracking-updates='<?php echo json_encode($trackingUpdates); ?>'
                     data-shipment='<?php echo json_encode($shipment); ?>'
                     data-routing-preference='<?php echo getSetting("intercontinental_routing_preference", "auto"); ?>'
                     data-enable-intercontinental='<?php echo getSetting("enable_intercontinental_routing", "0"); ?>'></div>
                <div class="current-location-info">
                    <div class="location-card">
                        <h3>Current Status</h3>
                        <div class="status-badge <?php echo getStatusClass($shipment['status']); ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $shipment['status'])); ?>
                        </div>

                        <?php if ($mapCenter): ?>
                        <div class="location-details">
                            <div class="location-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div>
                                    <h4>Current Location</h4>
                                    <p><?php echo htmlspecialchars($mapCenter['location']); ?></p>
                                </div>
                            </div>

                            <?php if (!empty($trackingUpdates[0]['timestamp'])): ?>
                            <div class="location-item">
                                <i class="fas fa-clock"></i>
                                <div>
                                    <h4>Last Updated</h4>
                                    <p><?php echo formatDate($trackingUpdates[0]['timestamp']); ?></p>
                                </div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($shipment['estimated_delivery'])): ?>
                            <div class="location-item">
                                <i class="fas fa-calendar-alt"></i>
                                <div>
                                    <h4>Estimated Delivery</h4>
                                    <p><?php echo date('F j, Y', strtotime($shipment['estimated_delivery'])); ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>

                        <!-- Shipment Progress -->
                        <div class="shipment-progress">
                            <h4>Shipment Progress</h4>
                            <div class="progress-bar-container">
                                <div id="shipment-progress-bar" class="progress-bar" style="width: 0%"></div>
                            </div>
                            <div class="progress-labels">
                                <span>Origin</span>
                                <span id="progress-percentage">0%</span>
                                <span>Destination</span>
                            </div>
                            <div class="share-tracking">
                                <button id="share-tracking-btn" class="btn share-btn" title="Share tracking link">
                                    <i class="fas fa-share-alt"></i> Share Tracking
                                </button>
                            </div>
                        </div>

                        <!-- Delivery Countdown -->
                        <?php if (!empty($shipment['estimated_delivery']) && strtotime($shipment['estimated_delivery']) > time()): ?>
                        <div class="delivery-countdown">
                            <h4><i class="fas fa-hourglass-half"></i> Estimated Delivery In</h4>
                            <div id="delivery-countdown">Loading...</div>
                        </div>
                        <?php endif; ?>

                        <?php else: ?>
                        <div class="no-location-data">
                            <p><i class="fas fa-info-circle"></i> Location data is not available for this shipment yet.</p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Shipment Details -->
        <div class="shipment-details-section">
            <div class="shipment-details-grid">
                <!-- Package Information -->
                <div class="shipment-details-card">
                    <h2><i class="fas fa-box"></i> Package Information</h2>
                    <div class="details-content">
                        <div class="package-picture">
                            <?php if (!empty($shipment['package_picture'])): ?>
                                <img src="<?php echo SITE_URL; ?>/uploads/packages/<?php echo $shipment['package_picture']; ?>" alt="Package Picture">
                            <?php else: ?>
                                <img src="<?php echo SITE_URL; ?>/assets/images/defaults/package-placeholder.svg" alt="Default Package" class="default-package-image">
                            <?php endif; ?>
                        </div>

                        <div class="details-grid">
                            <?php if (!empty($shipment['package_name'])): ?>
                            <div class="detail-item">
                                <div class="detail-label"><i class="fas fa-box-open"></i> Package Name</div>
                                <div class="detail-value"><?php echo $shipment['package_name']; ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($shipment['package_description'])): ?>
                            <div class="detail-item full-width">
                                <div class="detail-label"><i class="fas fa-info-circle"></i> Description</div>
                                <div class="detail-value"><?php echo $shipment['package_description']; ?></div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($shipment['package_weight'])): ?>
                            <div class="detail-item">
                                <div class="detail-label"><i class="fas fa-weight"></i> Weight</div>
                                <div class="detail-value"><?php echo $shipment['package_weight']; ?> kg</div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($shipment['package_dimensions'])): ?>
                            <div class="detail-item">
                                <div class="detail-label"><i class="fas fa-ruler-combined"></i> Dimensions</div>
                                <div class="detail-value"><?php echo $shipment['package_dimensions']; ?> cm</div>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($shipment['shipping_service'])): ?>
                            <div class="detail-item">
                                <div class="detail-label"><i class="fas fa-shipping-fast"></i> Service</div>
                                <div class="detail-value"><?php echo $shipment['shipping_service']; ?></div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Sender Information -->
                <div class="shipment-details-card">
                    <h2><i class="fas fa-user-edit"></i> Sender Information</h2>
                    <div class="details-content">
                        <?php if (empty($shipment['shopper_name']) && empty($shipment['shopper_email']) && empty($shipment['shopper_phone']) && empty($shipment['shopper_address'])): ?>
                            <div class="empty-state">
                                <p>No sender information available</p>
                            </div>
                        <?php else: ?>
                            <div class="details-grid">
                                <?php if (!empty($shipment['shopper_name'])): ?>
                                <div class="detail-item">
                                    <div class="detail-label"><i class="fas fa-user"></i> Name</div>
                                    <div class="detail-value"><?php echo $shipment['shopper_name']; ?></div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($shipment['shopper_address'])): ?>
                                <div class="detail-item">
                                    <div class="detail-label"><i class="fas fa-map-marker-alt"></i> Address</div>
                                    <div class="detail-value"><?php echo $shipment['shopper_address']; ?></div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($shipment['shopper_email']) || !empty($shipment['shopper_phone'])): ?>
                                <div class="detail-item contact-info">
                                    <div class="detail-label"><i class="fas fa-address-card"></i> Contact</div>
                                    <div class="detail-value">
                                        <?php if (!empty($shipment['shopper_email'])): ?>
                                            <div><i class="fas fa-envelope"></i> <?php echo $shipment['shopper_email']; ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($shipment['shopper_phone'])): ?>
                                            <div><i class="fas fa-phone"></i> <?php echo $shipment['shopper_phone']; ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Receiver Information -->
                <div class="shipment-details-card">
                    <h2><i class="fas fa-user-check"></i> Receiver Information</h2>
                    <div class="details-content">
                        <?php if (empty($shipment['receiver_name']) && empty($shipment['receiver_email']) && empty($shipment['receiver_phone']) && empty($shipment['receiver_address'])): ?>
                            <div class="empty-state">
                                <p>No receiver information available</p>
                            </div>
                        <?php else: ?>
                            <div class="details-grid">
                                <?php if (!empty($shipment['receiver_name'])): ?>
                                <div class="detail-item">
                                    <div class="detail-label"><i class="fas fa-user"></i> Name</div>
                                    <div class="detail-value"><?php echo $shipment['receiver_name']; ?></div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($shipment['receiver_address'])): ?>
                                <div class="detail-item">
                                    <div class="detail-label"><i class="fas fa-map-marker-alt"></i> Address</div>
                                    <div class="detail-value"><?php echo $shipment['receiver_address']; ?></div>
                                </div>
                                <?php endif; ?>

                                <?php if (!empty($shipment['receiver_email']) || !empty($shipment['receiver_phone'])): ?>
                                <div class="detail-item contact-info">
                                    <div class="detail-label"><i class="fas fa-address-card"></i> Contact</div>
                                    <div class="detail-value">
                                        <?php if (!empty($shipment['receiver_email'])): ?>
                                            <div><i class="fas fa-envelope"></i> <?php echo $shipment['receiver_email']; ?></div>
                                        <?php endif; ?>
                                        <?php if (!empty($shipment['receiver_phone'])): ?>
                                            <div><i class="fas fa-phone"></i> <?php echo $shipment['receiver_phone']; ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tracking Timeline -->
        <div class="tracking-timeline">
            <h2><i class="fas fa-history"></i> Tracking Updates</h2>

            <?php if(empty($trackingUpdates)): ?>
                <p class="no-updates">No tracking updates available yet.</p>
            <?php else: ?>
                <div class="timeline">
                    <?php foreach($trackingUpdates as $update): ?>
                        <div class="timeline-item">
                            <div class="timeline-marker"></div>
                            <div class="timeline-content">
                                <h3><?php echo $update['status']; ?></h3>
                                <p class="timeline-location"><?php echo $update['location']; ?></p>
                                <p class="timeline-time"><?php echo formatDate($update['timestamp']); ?></p>
                                <?php if(!empty($update['notes'])): ?>
                                    <p class="timeline-notes"><?php echo $update['notes']; ?></p>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Add mobile-specific styles for tracking form -->
<style>
/* Full-width detail cards */
.detail-card.full-width {
    grid-column: 1 / -1;
    margin-top: 10px;
}

.detail-item.full-width {
    grid-column: 1 / -1;
    margin-top: 10px;
}

.detail-item.full-width .detail-value {
    margin-top: 5px;
    line-height: 1.5;
}

@media (max-width: 576px) {
    .tracking-form .form-group {
        position: relative;
        margin-bottom: 30px;
        padding-top: 20px;
    }

    .tracking-form label {
        position: absolute;
        top: 0;
        left: 0;
        margin-bottom: 0;
        font-size: 0.85rem;
        font-weight: bold;
        z-index: 10;
    }

    .tracking-form input {
        padding-top: 20px;
    }
}
</style>

<!-- Add login debugging script -->
<script>
// Add event listener to login form if it exists
document.addEventListener('DOMContentLoaded', function() {
    // Check if login form exists
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        console.log('Login form found on tracking page');

        // Add submit event listener for debugging
        loginForm.addEventListener('submit', function(e) {
            console.log('Login form submitted on tracking page');
            // Don't prevent default here, let the main handler in footer.php handle it
        });
    } else {
        console.log('Login form not found on tracking page');
    }
});
</script>

<!-- Initialize tracking map -->
<script>
    // Store tracking updates for map
    const trackingUpdates = <?php echo json_encode($trackingUpdates); ?>;
    // Store shipment data for countdown
    const shipmentData = <?php echo json_encode($shipment); ?>;
</script>
<?php
    // Add Leaflet CSS and JS, and tracking map JS
    // Add Leaflet CSS directly
    echo "<link rel='stylesheet' href='https://unpkg.com/leaflet@1.9.4/dist/leaflet.css'>";

    // Add Leaflet Routing Machine CSS
    echo "<link rel='stylesheet' href='https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css'>";

    // Add Leaflet JS directly
    echo "<script src='https://unpkg.com/leaflet@1.9.4/dist/leaflet.js'></script>";

    // Add Leaflet Routing Machine
    echo "<script src='https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js'></script>";

    // We're not using the OSRM plugin anymore, using our custom implementation instead

    // Add Polyline library (global version)
    echo "<script src='https://unpkg.com/@mapbox/polyline@1.1.1/src/polyline.js'></script>";

    // Commented out to avoid conflicts with our new implementation
    // echo "<script src='" . SITE_URL . "/assets/js/leaflet-simple-routing.js'></script>";

    // Add Leaflet Control Geocoder (for address lookup)
    echo "<script src='https://unpkg.com/leaflet-control-geocoder@2.4.0/dist/Control.Geocoder.js'></script>";

    // Add Leaflet Polyline Decorator for route arrows
    echo "<script src='https://unpkg.com/leaflet-polylinedecorator@1.6.0/dist/leaflet.polylineDecorator.js'></script>";

    // Add Leaflet Ant Path plugin
    echo "<script src='https://cdn.jsdelivr.net/npm/leaflet-ant-path@1.3.0/dist/leaflet-ant-path.min.js'></script>";

    // Add Polyline Encoder/Decoder (local copy)
    echo "<script src='" . SITE_URL . "/assets/js/polyline.encoded.min.js'></script>";

    // Route Generator is now handled by leaflet-simple-routing.js

    // Add Notifications JS
    echo "<script src='" . SITE_URL . "/assets/js/notifications.js'></script>";

    // Add Tracking Share JS
    echo "<script src='" . SITE_URL . "/assets/js/tracking-share.js'></script>";

    // Add Map Path Fix JS
    echo "<script src='" . SITE_URL . "/assets/js/map-path-fix.js'></script>";

    // Add Custom AntPath JS
    echo "<script src='" . SITE_URL . "/assets/js/custom-ant-path.js'></script>";

    // Add custom polyline decoder function
    echo "<script>
        // Add polyline decoder if not available
        if (!L.Polyline.fromEncoded) {
            L.Polyline.fromEncoded = function(encoded, options) {
                return new L.Polyline(polyline.decode(encoded), options);
            };
        }
    </script>";

    // Debug information
    echo "<!-- Debug: SITE_URL = " . SITE_URL . " -->";

    // Check if intercontinental routing is enabled
    $enableIntercontinentalRouting = getSetting('enable_intercontinental_routing', '0');

    // Load the appropriate script based on the setting
    if ($enableIntercontinentalRouting === '1') {
        // First load the intercontinental routing helper
        echo "<script src='" . SITE_URL . "/assets/js/intercontinental-routing.js'></script>";

        // Then load the intercontinental tracking map script
        echo "<script src='" . SITE_URL . "/assets/js/intercontinental-tracking-map.js'></script>";
    } else {
        // Load the standard tracking map script
        echo "<script src='" . SITE_URL . "/assets/js/simple-tracking-map.js'></script>";
    }

    // Keep these empty so the header/footer don't try to include them again
    $additionalCss = [];
    $additionalJs = [];
?>
<?php endif; ?>

<?php include_once '../includes/footer.php'; ?>
