<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('../index.php');
    exit;
}

// Set page title
$pageTitle = 'Tracking Settings';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Start transaction
        $conn->beginTransaction();

        // Get form data
        $enableIntercontinentalRouting = isset($_POST['enable_intercontinental_routing']) ? '1' : '0';
        $routingPreference = isset($_POST['intercontinental_routing_preference']) ? sanitize($_POST['intercontinental_routing_preference']) : 'auto';

        // Update settings
        $updateStmt = $conn->prepare("UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?");

        // Update intercontinental routing setting
        $updateStmt->execute([$enableIntercontinentalRouting, 'enable_intercontinental_routing']);

        // Update routing preference setting
        $updateStmt->execute([$routingPreference, 'intercontinental_routing_preference']);

        // Commit transaction
        $conn->commit();

        setSuccessNotification('Tracking settings updated successfully');

    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        setErrorNotification('Error updating settings: ' . $e->getMessage());
    }
}

// Get current settings
$enableIntercontinentalRouting = getSetting('enable_intercontinental_routing', '0');
$routingPreference = getSetting('intercontinental_routing_preference', 'auto');

// Include header
include_once '../includes/header.php';
?>

<!-- Admin Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">
            <h1>Tracking Settings</h1>
            <p>Configure advanced tracking and routing settings</p>
        </div>
    </div>
</section>

<!-- Settings Section -->
<section class="settings-section">
    <div class="container">
        <div class="settings-card">
            <div class="settings-header">
                <h2>Intercontinental Routing Settings</h2>
                <p>Configure how shipments are routed between continents</p>
            </div>

            <form method="POST" action="tracking-settings.php" class="settings-form">
                <div class="form-group">
                    <label for="enable_intercontinental_routing">Enable Intercontinental Routing</label>
                    <div class="toggle-switch">
                        <input
                            type="checkbox"
                            id="enable_intercontinental_routing"
                            name="enable_intercontinental_routing"
                            value="1"
                            <?php echo $enableIntercontinentalRouting == '1' ? 'checked' : ''; ?>
                        >
                        <label for="enable_intercontinental_routing" class="toggle-label"></label>
                    </div>
                    <p class="form-text">When enabled, intercontinental shipments will be routed through the nearest airports or seaports</p>
                </div>

                <div class="form-group">
                    <label for="intercontinental_routing_preference">Routing Preference</label>
                    <select id="intercontinental_routing_preference" name="intercontinental_routing_preference" class="form-control">
                        <option value="auto" <?php echo $routingPreference == 'auto' ? 'selected' : ''; ?>>Auto-detect (Based on distance)</option>
                        <option value="air" <?php echo $routingPreference == 'air' ? 'selected' : ''; ?>>Prefer Airports</option>
                        <option value="sea" <?php echo $routingPreference == 'sea' ? 'selected' : ''; ?>>Prefer Seaports</option>
                    </select>
                    <p class="form-text">Choose how to route intercontinental shipments</p>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn primary-btn">Save Settings</button>
                    <a href="settings.php" class="btn outline-btn">Back to Settings</a>
                </div>
            </form>
        </div>
    </div>
</section>

<?php
// Add CSS for settings page
echo "<style>";
readfile(__DIR__ . '/../assets/css/admin-mobile.css');
readfile(__DIR__ . '/../assets/css/sidebar-fix.css'); // Add sidebar fix CSS
echo "</style>";

echo "<script>";
readfile(__DIR__ . '/../assets/js/admin-mobile.js');
echo "</script>";

// Include footer
include_once '../includes/footer.php';
?>
