/* Admin Styles */

/* Admin Hero Section */
.admin-hero {
    background: linear-gradient(rgba(92, 43, 226, 0.8), rgba(79, 37, 194, 0.8)), url('https://images.unsplash.com/photo-1586528116311-ad8dd3c8310d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
}

.admin-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    justify-content: center;
}

/* Button Styles */
.purple-btn {
    background-color: #8e44ad;
    color: white;
}

.purple-btn:hover {
    background-color: #7d3c98;
}

.orange-btn {
    background-color: #e67e22;
    color: white;
}

.orange-btn:hover {
    background-color: #d35400;
}

/* Dashboard Overview */
.dashboard-overview {
    padding: 40px 0;
    background-color: var(--bg-color);
    position: relative;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    margin-top: 20px;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    display: none;
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.sidebar-toggle:hover {
    transform: scale(1.1);
}

.sidebar-toggle.active i {
    transform: rotate(90deg);
}

.dashboard-main {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.dashboard-sidebar {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-top: 20px;
}

.stats-container {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.stats-header, .metrics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.stats-header h2, .metrics-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.stats-section, .metrics-section {
    margin-bottom: 30px;
}

.stats-section:last-child, .metrics-section:last-child {
    margin-bottom: 0;
}

.stats-subheader, .metrics-subheader {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.stats-summary, .metrics-summary {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px dashed var(--border-color);
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 500;
    color: var(--text-color);
}

.summary-value {
    font-weight: 700;
    color: var(--primary-color);
}

.summary-value.positive {
    color: #28a745;
}

.summary-value.negative {
    color: #dc3545;
}

.change-indicator {
    display: block;
    font-size: 0.8rem;
    font-weight: normal;
    margin-top: 2px;
}

.change-indicator.positive {
    color: #28a745;
}

.change-indicator.negative {
    color: #dc3545;
}

.stat-percentage {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 2px;
}

.stats-header .header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.stats-header .view-all {
    font-size: 0.9rem;
}

.refresh-stats {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.refresh-stats:hover {
    transform: rotate(180deg);
}

.stats-container.loading .refresh-stats {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stat-card {
    display: flex;
    align-items: center;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(92, 43, 226, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-right: 20px;
}

.stat-icon.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.stat-icon.transit {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.stat-icon.delivered {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.stat-icon.delayed {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.stat-icon.cancelled {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stat-info h3 {
    font-size: 1rem;
    margin-bottom: 5px;
    color: var(--text-color);
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Recent Shipments Section */
.recent-shipments {
    padding: 0;
    background-color: transparent;
}

.recent-shipments .table-container {
    max-height: 400px;
    overflow-y: auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.view-all {
    color: var(--secondary-color);
    font-weight: 600;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.table-container {
    overflow-x: auto;
    background-color: var(--glass-bg);
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    text-align: left;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--primary-color);
    font-weight: 600;
}

.data-table td {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-color);
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tbody tr {
    transition: background-color 0.3s ease;
}

.data-table tbody tr:hover {
    background-color: rgba(92, 43, 226, 0.05);
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.status-transit {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.status-delivered {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-delayed {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.status-cancelled {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.view-btn {
    background-color: var(--primary-color);
}

.edit-btn {
    background-color: var(--secondary-color);
}

.update-btn {
    background-color: #28a745;
}

.delete-btn {
    background-color: #dc3545;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.no-data {
    text-align: center;
    padding: 30px;
    color: var(--text-secondary);
    font-style: italic;
}

/* Quick Actions Section */
.quick-actions {
    padding: 0;
    background-color: transparent;
}

.actions-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
    margin-top: 20px;
}

.action-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 20px;
    display: flex;
    align-items: center;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: all 0.3s ease;
    color: var(--text-color);
}

.action-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.2);
}

.maintenance-card {
    background-color: #f8f9fa;
    border: 1px dashed #6c757d;
}

.maintenance-card .action-icon {
    background-color: #e67e22;
    color: white;
}

.maintenance-card:hover {
    background-color: #f1f3f5;
}

.dark-theme .maintenance-card {
    background-color: rgba(40, 40, 40, 0.7);
    border: 1px dashed rgba(255, 255, 255, 0.2);
}

/* Maintenance Mode Toggle Card */
.danger-card {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.danger-card .action-icon {
    background-color: #dc3545;
    color: white;
}

.danger-card:hover {
    background-color: rgba(220, 53, 69, 0.15);
    border: 1px solid rgba(220, 53, 69, 0.5);
}

.danger-card:hover .action-icon {
    background-color: #c82333;
}

.dark-theme .danger-card {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.4);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(92, 43, 226, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 15px;
    transition: all 0.3s ease;
}

.action-card:hover .action-icon {
    background-color: var(--primary-color);
    color: white;
}

.action-info {
    flex: 1;
}

.action-card h3 {
    margin-bottom: 5px;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.action-card p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

/* Admin Login */
.admin-login {
    padding: 60px 0;
    background-color: var(--bg-color);
}

.login-container {
    max-width: 500px;
    margin: 0 auto;
}

.login-form-container {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

/* Tracking Page Styles */
.tracking-form-section {
    padding: 60px 0;
    background-color: var(--bg-color);
}

.tracking-results {
    padding: 60px 0;
    background-color: var(--bg-secondary);
}

.shipment-details {
    margin-bottom: 50px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.detail-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.detail-card h3 {
    font-size: 0.9rem;
    margin-bottom: 10px;
    color: var(--text-secondary);
}

.detail-card p {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

.tracking-map-container {
    margin-bottom: 50px;
}

#tracking-map {
    height: 400px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    position: relative;
}

/* Map Provider Control */
.map-provider-control {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    background-color: var(--glass-bg);
    border-radius: 4px;
    padding: 5px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.map-provider-control select {
    background-color: transparent;
    border: none;
    color: var(--text-color);
    font-size: 0.9rem;
    padding: 5px;
    cursor: pointer;
    outline: none;
}

.map-provider-control select option {
    background-color: var(--bg-color);
    color: var(--text-color);
}

/* Dark Theme Map */
.dark-theme-map .leaflet-tile {
    filter: brightness(0.6) invert(1) contrast(3) hue-rotate(200deg) saturate(0.3) brightness(0.7);
}

.dark-theme-map .leaflet-container {
    background: #303030;
}

.dark-theme-map .leaflet-popup-content-wrapper,
.dark-theme-map .leaflet-popup-tip {
    background-color: var(--bg-secondary);
    color: var(--text-color);
    border: var(--glass-border);
}

.no-map-data {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    font-style: italic;
}

.map-info-window {
    padding: 5px;
    max-width: 250px;
}

.map-info-window h3 {
    color: var(--primary-color);
    margin-bottom: 5px;
}

.map-info-window p {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

/* Tracking Timeline */
.tracking-timeline {
    margin-bottom: 50px;
}

.timeline {
    position: relative;
    padding-left: 30px;
    margin-top: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 10px;
    width: 2px;
    background-color: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    top: 0;
    left: -30px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 3px solid var(--bg-secondary);
}

.timeline-content {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 20px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.timeline-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.timeline-location {
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.timeline-notes {
    background-color: rgba(92, 43, 226, 0.05);
    padding: 10px;
    border-radius: 8px;
    font-style: italic;
}

.no-updates {
    text-align: center;
    padding: 30px;
    background-color: var(--glass-bg);
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-secondary);
    font-style: italic;
}

/* Message Styles */
.message {
    padding: 15px;
    margin: 20px 0;
    border-radius: 8px;
    text-align: center;
}

.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.error {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.2);
}

.info {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
    border: 1px solid rgba(0, 123, 255, 0.2);
}

.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
    border: 1px solid rgba(255, 193, 7, 0.2);
}

/* Tooltip */
.tooltip {
    position: fixed;
    background-color: var(--primary-color);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.9rem;
    z-index: 1001;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid var(--primary-color);
}

/* Dark Theme Overrides */
.dark-theme .stat-card,
.dark-theme .action-card,
.dark-theme .detail-card,
.dark-theme .timeline-content,
.dark-theme .table-container,
.dark-theme .login-form-container,
.dark-theme .no-updates {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .data-table th,
.dark-theme .data-table td {
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .data-table tbody tr:hover {
    background-color: rgba(92, 43, 226, 0.1);
}

.dark-theme .timeline::before {
    background-color: var(--primary-color);
}

.dark-theme .timeline-marker {
    border-color: var(--bg-secondary);
}

.dark-theme .timeline-notes {
    background-color: rgba(92, 43, 226, 0.1);
}

.dark-theme .no-map-data {
    background-color: #1e1e1e;
}

.dark-theme .chart-container,
.dark-theme .performance-metrics,
.dark-theme .activity-feed {
    background-color: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .metric-item {
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .activity-item:not(:last-child)::after {
    background-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .sidebar-toggle {
    background-color: var(--primary-color);
    color: white;
}

.dark-theme .tooltip {
    background-color: var(--primary-color);
}

.dark-theme .tooltip::after {
    border-top-color: var(--primary-color);
}

/* Responsive Styles */
/* Dashboard Charts */
.chart-container {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.chart-wrapper {
    position: relative;
    height: 300px;
}

/* Performance Metrics */
.performance-metrics {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.metric-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    display: flex;
    align-items: center;
}

.metric-icon {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: rgba(92, 43, 226, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    margin-right: 12px;
}

.metric-name {
    font-weight: 500;
}

.metric-value {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
}

.progress-container {
    height: 6px;
    background-color: rgba(92, 43, 226, 0.1);
    border-radius: 3px;
    margin-top: 8px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 3px;
}

/* Routes List */
.routes-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.route-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.route-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.route-rank {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 15px;
}

.route-details {
    flex: 1;
}

.route-name {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color);
}

.route-stats {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.no-routes {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
    font-style: italic;
}

/* Activity Feed */
.activity-feed {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.activity-list {
    margin-top: 20px;
}

.activity-item {
    display: flex;
    margin-bottom: 20px;
    position: relative;
}

.activity-item:last-child {
    margin-bottom: 0;
}

.activity-item:not(:last-child)::after {
    content: '';
    position: absolute;
    top: 30px;
    left: 15px;
    bottom: -20px;
    width: 2px;
    background-color: var(--border-color);
}

.activity-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(92, 43, 226, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    margin-right: 15px;
    z-index: 1;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.activity-time {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.no-activity {
    text-align: center;
    padding: 20px;
    color: var(--text-secondary);
    font-style: italic;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 992px) {
    .admin-actions {
        flex-direction: column;
        align-items: center;
    }

    .admin-actions .btn {
        width: 100%;
        max-width: 300px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    /* Show sidebar toggle on tablet and below */
    .sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Hide sidebar by default on mobile */
    .dashboard-sidebar {
        display: none;
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        width: 300px;
        background-color: var(--bg-color);
        z-index: 999;
        overflow-y: auto;
        padding: 20px;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
        transform: translateX(100%);
    }

    /* Show sidebar when active */
    .dashboard-sidebar.active {
        display: block;
        transform: translateX(0);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }

    #tracking-map {
        height: 300px;
    }

    .action-card {
        flex-direction: column;
        text-align: center;
    }

    .action-icon {
        margin-right: 0;
        margin-bottom: 15px;
    }
}

@media (max-width: 480px) {
    .actions {
        flex-wrap: wrap;
        justify-content: center;
    }

    .dashboard-sidebar {
        width: 100%;
    }
}
