// Custom Routing Implementation for Leaflet Routing Machine
// This file provides custom routing functionality for different transportation modes

// Enable debug mode for troubleshooting
const DEBUG_MODE = false;

// Helper function to log debug messages
function debugLog(...args) {
    if (DEBUG_MODE) {
        console.log('[Routing Debug]', ...args);
    }
}

// Custom router implementation for different transportation modes
function createCustomRouter(options) {
    debugLog('Creating custom router with options:', options);

    // Create a custom router object that extends L.Class to ensure proper context
    const CustomRouter = L.Class.extend({
        initialize: function(options) {
            this.options = options || {};
        },

        // The route method is called by Leaflet Routing Machine
        route: function(waypoints, callback, context) {
            // Mode is always 'driving' internally for API profile, but fallback is 'rail'
            const mode = 'driving'; // Keep for API profile consistency
            debugLog('Custom router creating route (API: driving, Fallback: rail)');
            debugLog('Received waypoints:', waypoints); // Log input waypoints

            // Ensure we have valid waypoints
            if (!waypoints || waypoints.length < 2) {
                callback.call(context || this, null, []);
                return;
            }

            // Process waypoints to ensure they have valid latLng properties
            const processedWaypoints = waypoints.map(wp => {
                if (wp.latLng) return wp;
                return { latLng: wp };
            });

            // Store reference to this for use in callbacks
            const self = this;
            debugLog('Processed waypoints:', processedWaypoints); // Log processed waypoints

            // Define the fallback function (Rail Route)
            const createFallbackRoute = () => {
                debugLog('Creating fallback rail route.');
                const fallbackCoordinates = this._createRailRoute(processedWaypoints);
                // Use 'rail' for mode in fallback route object creation
                this._createAndReturnRoute(fallbackCoordinates, processedWaypoints, 'rail', callback, context);
            };

            // Try to get actual road route from OSRM API
            debugLog('Attempting to fetch route from external API...');
            this._fetchOSRMRoute(processedWaypoints).then(function(routeCoords) {
                debugLog('External API fetch succeeded.');
                // Use OSRM route if available
                if (routeCoords && routeCoords.length > 0) {
                    debugLog('Using coordinates from external API.');
                    self._createAndReturnRoute(routeCoords, processedWaypoints, mode, callback, context);
                } else {
                    // API success but no route data, use rail fallback
                    debugLog('External API returned success but no route coordinates found. Falling back to rail route.');
                    createFallbackRoute();
                }
            }).catch(function(error) {
                // Use debugLog and provide more context
                debugLog('Error fetching route from external API:', error.message);
                // Fall back to rail route on API error
                debugLog('Using rail fallback coordinates (API error).');
                createFallbackRoute();
            });

        },

        // Helper method to create and return a route object
        _createAndReturnRoute: function(coordinates, waypoints, mode, callback, context) {
            // Calculate total distance
            let totalDistance = 0;
            for (let i = 0; i < coordinates.length - 1; i++) {
                totalDistance += coordinates[i].distanceTo(coordinates[i + 1]);
            }

            // Create route object that matches Leaflet Routing Machine's expected format
            const routeTime = this._calculateTime(totalDistance, mode);

            // Define the single instruction object
            const singleInstruction = {
                type: 'Straight', // Instruction type
                // Adjust text based on whether it's the API route or fallback
                text: (mode === 'driving' ? 'Follow the road route' : 'Follow the rail route'),
                distance: totalDistance,
                time: routeTime,
                index: 0,
                direction: 'forward'
            };

            const route = {
                // Basic properties
                // Adjust name based on whether it's the API route or fallback
                name: (mode === 'driving' ? 'Road Route' : 'Rail Route (Fallback)'),
                coordinates: coordinates,
                waypoints: waypoints,
                inputWaypoints: waypoints,

                // Summary information
                summary: {
                    totalDistance: totalDistance,
                    totalTime: routeTime
                },

                // Route legs (segments between waypoints)
                legs: [{
                    start: waypoints[0].latLng,
                    end: waypoints[waypoints.length - 1].latLng,
                    distance: totalDistance,
                    time: routeTime,
                    summary: {
                        totalDistance: totalDistance,
                        totalTime: routeTime
                    },
                    // Populate steps array instead of instructions array directly on leg
                    steps: [
                        // Use the predefined instruction object
                        singleInstruction
                    ]
                }],

                // Add top-level instructions array as potentially required by LRM
                instructions: [singleInstruction],

                // Geometry for rendering
                geometry: coordinates,

                // Additional properties required by Leaflet Routing Machine
                weight: 5,
                color: this._getRouteColor(mode),
                opacity: 0.8
            };

            // Return the route
            callback.call(context || this, null, [route]);
        },

        // Calculate estimated time (simplified)
        _calculateTime: function(distance, mode) { // Keep mode param for potential future use
            const distanceKm = distance / 1000;
            // Use average speed: road ~40km/h (1.5 min/km), rail ~60km/h (1 min/km)
            const minutesPerKm = (mode === 'rail') ? 1 : 1.5;
            return distanceKm * 60 * minutesPerKm;
        },

        // Get appropriate color (simplified)
        _getRouteColor: function(mode) {
            // Dark gray for rail (fallback), Purple for road (API success)
            return (mode === 'rail') ? '#333333' : '#5c2be2';
        },

        // Create rail route (straighter than road)
        _createRailRoute: function(waypoints) {
            const coordinates = [];

            // Process each segment between waypoints
            for (let i = 0; i < waypoints.length - 1; i++) {
                const start = waypoints[i].latLng;
                const end = waypoints[i + 1].latLng;

                // Calculate distance for this segment
                const segmentDistance = start.distanceTo(end) / 1000; // km

                // For longer segments, add intermediate points to simulate rail lines
                const numPoints = Math.max(Math.ceil(segmentDistance / 100), 4); // Fewer points for straighter lines

                // Generate points with very slight randomness to simulate rail lines (straighter than roads)
                for (let j = 0; j <= numPoints; j++) {
                    const t = j / numPoints;

                    // Linear interpolation between start and end
                    let lat = start.lat * (1 - t) + end.lat * t;
                    let lng = start.lng * (1 - t) + end.lng * t;

                    // Add very small random deviation to simulate rail lines (much straighter than roads)
                    if (j > 0 && j < numPoints) {
                        const deviation = 0.005 * Math.sin(t * Math.PI); // Very small deviation
                        lat += (Math.random() * 2 - 1) * deviation;
                        lng += (Math.random() * 2 - 1) * deviation;
                    }

                    const point = L.latLng(lat, lng);
                    if (!point.lat || !point.lng) {
                        debugLog('Invalid coordinate generated:', lat, lng);
                        continue;
                    }
                    coordinates.push(point);
                }
            }

            return coordinates;
        },


        // Fetch route from routing API
        _fetchOSRMRoute: function(waypoints) {
            debugLog('Fetching route for waypoints:', waypoints);
            return new Promise(function(resolve, reject) {
                try {
                    // Try multiple routing services for better results
                    // First try OSRM API
                    const fetchOSRM = function() {
                        // Prepare waypoints for OSRM API
                        const points = waypoints.map(wp => {
                            return wp.latLng.lng + ',' + wp.latLng.lat;
                        }).join(';');

                        // Construct OSRM API URL
                        const url = 'https://router.project-osrm.org/route/v1/driving/' + points + '?overview=full&geometries=geojson';
                        debugLog('Fetching OSRM route from:', url);

                        // Fetch route from OSRM API
                        return fetch(url)
                            .then(response => response.json())
                            .then(data => {
                                if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                                    // Extract coordinates from OSRM response
                                    const routeCoords = data.routes[0].geometry.coordinates.map(coord => {
                                        const latLng = L.latLng(coord[1], coord[0]); // OSRM returns [lng, lat]
                                        if (!latLng.lat || !latLng.lng) {
                                            throw new Error('Invalid coordinate received from API');
                                        }
                                        return latLng;
                                    });
                                    debugLog('OSRM API call successful. Route found with', routeCoords.length, 'points');
                                    return routeCoords;
                                } else {
                                    debugLog('OSRM API call successful but no route found in response (data.code=' + data.code + '). Trying alternative.');
                                    throw new Error('No OSRM route found in response');
                                }
                            });
                    };

                    // Alternative: Try OpenRouteService API
                    const fetchORS = function() {
                        // Only use first and last waypoint for simplicity
                        const start = waypoints[0].latLng;
                        const end = waypoints[waypoints.length - 1].latLng;

                        // Construct ORS API URL (using public API key - limited usage)
                        const url = 'https://api.openrouteservice.org/v2/directions/driving-car?api_key=5b3ce3597851110001cf6248b4c2e5bba7f742a49ba8b5c763979829&start=' +
                                   start.lng + ',' + start.lat + '&end=' + end.lng + ',' + end.lat;
                        debugLog('Fetching ORS route from:', url);

                        return fetch(url)
                            .then(response => response.json())
                            .then(data => {
                                if (data.features && data.features.length > 0) {
                                    // Extract coordinates from ORS response
                                    const routeCoords = data.features[0].geometry.coordinates.map(coord => {
                                        const latLng = L.latLng(coord[1], coord[0]); // ORS returns [lng, lat]
                                        if (!latLng.lat || !latLng.lng) {
                                            throw new Error('Invalid coordinate received from ORS');
                                        }
                                        return latLng;
                                    });
                                    debugLog('ORS API call successful. Route found with', routeCoords.length, 'points');
                                    return routeCoords;
                                } else {
                                    debugLog('ORS API call successful but no route found in response.');
                                    throw new Error('No ORS route found in response');
                                }
                            });
                    };

                    // Try OSRM first, then fall back to ORS if needed
                    fetchOSRM()
                        .then(routeCoords => {
                            resolve(routeCoords);
                        })
                        .catch(error => {
                            debugLog('OSRM API call failed:', error.message, '. Trying ORS as fallback.');
                            // Try ORS as fallback
                            fetchORS()
                                .then(routeCoords => {
                                    resolve(routeCoords);
                                })
                                .catch(error => {
                                    debugLog('ORS API call also failed:', error.message, '. No external route available.');
                                    reject(error); // Reject the main promise
                                });
                        });
                } catch (error) {
                    debugLog('Error in route fetching:', error);
                    reject(error);
                }
            });
        },

    });

    // Return a new instance of the custom router
    return new CustomRouter(options);
}
