<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Set page title
$pageTitle = 'Notification System Demo';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['show_notification'])) {
        $message = sanitize($_POST['message']);
        $type = sanitize($_POST['type']);
        $duration = (int)$_POST['duration'];
        
        // Set the notification
        setNotification($message, $type, $duration);
        
        // Redirect to avoid form resubmission
        redirect('notification-demo.php');
    }
    
    if (isset($_POST['show_multiple'])) {
        // Set multiple notifications
        setSuccessNotification('Operation completed successfully!', 5000);
        setInfoNotification('Your account has been updated.', 7000);
        setWarningNotification('Your subscription will expire in 7 days.', 9000);
        
        // Redirect to avoid form resubmission
        redirect('notification-demo.php');
    }
    
    if (isset($_POST['show_error_reasons'])) {
        // Show error with different reasons
        $reasons = [
            'Database connection failed. Please try again later.',
            'Invalid input. Please check your form and try again.',
            'Permission denied. You do not have access to this resource.',
            'Network error. Please check your internet connection.',
            'Session expired. Please log in again.',
            'File upload failed. The file size exceeds the maximum limit.',
            'Service unavailable. The server is currently under maintenance.'
        ];
        
        $reason = $reasons[array_rand($reasons)];
        setErrorNotification($reason, 10000);
        
        // Redirect to avoid form resubmission
        redirect('notification-demo.php');
    }
}

// Include header
include_once 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>Notification System Demo</h1>
            <p>Test the notification system with different types and options</p>
        </div>
    </div>
</section>

<!-- Demo Section -->
<section class="notification-demo">
    <div class="container">
        <div class="demo-grid">
            <!-- Single Notification Form -->
            <div class="demo-card">
                <h2>Show Single Notification</h2>
                <form method="POST" action="notification-demo.php" class="demo-form">
                    <div class="form-group">
                        <label for="message">Message</label>
                        <input type="text" id="message" name="message" value="This is a test notification" required class="form-control">
                    </div>
                    
                    <div class="form-group">
                        <label for="type">Notification Type</label>
                        <select id="type" name="type" class="form-control">
                            <option value="success">Success</option>
                            <option value="error">Error</option>
                            <option value="warning">Warning</option>
                            <option value="info" selected>Info</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="duration">Duration (ms)</label>
                        <input type="number" id="duration" name="duration" value="5000" min="1000" step="1000" class="form-control">
                    </div>
                    
                    <button type="submit" name="show_notification" class="btn primary-btn">Show Notification</button>
                </form>
            </div>
            
            <!-- Multiple Notifications -->
            <div class="demo-card">
                <h2>Show Multiple Notifications</h2>
                <p>This will display multiple notifications of different types at once.</p>
                <form method="POST" action="notification-demo.php" class="demo-form">
                    <button type="submit" name="show_multiple" class="btn secondary-btn">Show Multiple Notifications</button>
                </form>
                
                <h2 class="mt-4">Show Error with Reasons</h2>
                <p>This will display an error notification with a random reason.</p>
                <form method="POST" action="notification-demo.php" class="demo-form">
                    <button type="submit" name="show_error_reasons" class="btn danger-btn">Show Error with Reason</button>
                </form>
            </div>
            
            <!-- JavaScript Demo -->
            <div class="demo-card">
                <h2>JavaScript API Demo</h2>
                <p>Use the JavaScript API directly to show notifications.</p>
                
                <div class="button-group">
                    <button onclick="showSuccess('Operation completed successfully!')" class="btn success-btn">Success</button>
                    <button onclick="showError('An error occurred while processing your request.')" class="btn danger-btn">Error</button>
                    <button onclick="showWarning('Please review your information before proceeding.')" class="btn warning-btn">Warning</button>
                    <button onclick="showInfo('Your session will expire in 15 minutes.')" class="btn info-btn">Info</button>
                </div>
                
                <div class="button-group mt-3">
                    <button onclick="showNotification('Custom notification with options', 'info', {duration: 10000, position: 'top-center'})" class="btn primary-btn">Custom Options</button>
                    <button onclick="closeAllNotifications()" class="btn secondary-btn">Close All</button>
                </div>
            </div>
            
            <!-- Documentation -->
            <div class="demo-card">
                <h2>Documentation</h2>
                
                <h3>PHP Functions</h3>
                <ul class="doc-list">
                    <li><code>setNotification($message, $type, $duration)</code> - Set a notification</li>
                    <li><code>setSuccessNotification($message, $duration)</code> - Set a success notification</li>
                    <li><code>setErrorNotification($message, $duration)</code> - Set an error notification</li>
                    <li><code>setWarningNotification($message, $duration)</code> - Set a warning notification</li>
                    <li><code>setInfoNotification($message, $duration)</code> - Set an info notification</li>
                </ul>
                
                <h3>JavaScript Functions</h3>
                <ul class="doc-list">
                    <li><code>showNotification(message, type, options)</code> - Show a notification</li>
                    <li><code>showSuccess(message, options)</code> - Show a success notification</li>
                    <li><code>showError(message, options)</code> - Show an error notification</li>
                    <li><code>showWarning(message, options)</code> - Show a warning notification</li>
                    <li><code>showInfo(message, options)</code> - Show an info notification</li>
                    <li><code>closeNotification(id)</code> - Close a specific notification</li>
                    <li><code>closeAllNotifications()</code> - Close all notifications</li>
                </ul>
                
                <h3>Options</h3>
                <ul class="doc-list">
                    <li><code>duration</code> - Duration in milliseconds (default: 5000)</li>
                    <li><code>position</code> - Position (top-right, top-left, bottom-right, bottom-left, top-center, bottom-center)</li>
                    <li><code>autoClose</code> - Whether to auto-close (default: true)</li>
                    <li><code>closeOnClick</code> - Whether to close when clicked (default: true)</li>
                    <li><code>showProgressBar</code> - Whether to show progress bar (default: true)</li>
                    <li><code>pauseOnHover</code> - Whether to pause on hover (default: true)</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Demo Styles -->
<style>
.notification-demo {
    padding: 40px 0;
}

.demo-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
}

.demo-card {
    background-color: var(--bg-secondary);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.demo-card h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: var(--text-color);
}

.demo-card h3 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 1.2rem;
    color: var(--text-color);
}

.demo-form .form-group {
    margin-bottom: 15px;
}

.demo-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.demo-form .form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--input-bg);
    color: var(--text-color);
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.mt-3 {
    margin-top: 15px;
}

.mt-4 {
    margin-top: 20px;
}

.success-btn {
    background-color: #28a745;
    color: white;
}

.success-btn:hover {
    background-color: #218838;
}

.danger-btn {
    background-color: #dc3545;
    color: white;
}

.danger-btn:hover {
    background-color: #c82333;
}

.warning-btn {
    background-color: #ffc107;
    color: #212529;
}

.warning-btn:hover {
    background-color: #e0a800;
}

.info-btn {
    background-color: #17a2b8;
    color: white;
}

.info-btn:hover {
    background-color: #138496;
}

.doc-list {
    padding-left: 20px;
}

.doc-list li {
    margin-bottom: 8px;
}

.doc-list code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 5px;
    border-radius: 3px;
    font-family: monospace;
}

@media (max-width: 992px) {
    .demo-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include_once 'includes/footer.php'; ?>
