<?php
/**
 * Update the status ENUM in the shipments table to include new status values
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Update Database Schema';

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-database"></i> Update Database Schema</h1>
            <div class="admin-actions">
                <a href="check_status_enum_new.php" class="btn secondary-btn"><i class="fas fa-search"></i> Check Database Status</a>
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Update Status ENUM</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Check current ENUM values
                        $db->query("SHOW COLUMNS FROM shipments LIKE 'status'");
                        $statusColumn = $db->single();
                        
                        if ($statusColumn) {
                            $currentEnum = $statusColumn['Type'];
                            echo "<p>Current status ENUM: <code>{$currentEnum}</code></p>";
                            
                            // Extract values from ENUM
                            preg_match("/^enum\(\'(.*)\'\)$/", $currentEnum, $matches);
                            $values = explode("','", $matches[1]);
                            
                            // Check if new values already exist
                            $newValues = ['picked_up', 'arrived_at_facility', 'out_for_delivery'];
                            $missingValues = array_diff($newValues, $values);
                            
                            if (empty($missingValues)) {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> All required status values already exist in the database.</div>";
                            } else {
                                // Add missing values to the ENUM
                                $allValues = array_merge($values, $missingValues);
                                $newEnum = "'" . implode("','", $allValues) . "'";
                                
                                // Update the ENUM
                                $db->query("ALTER TABLE shipments MODIFY COLUMN status ENUM({$newEnum}) NOT NULL");
                                
                                if ($db->execute()) {
                                    echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully updated status ENUM to include new values: " . implode(", ", $missingValues) . "</div>";
                                    
                                    // Check the updated ENUM
                                    $db->query("SHOW COLUMNS FROM shipments LIKE 'status'");
                                    $updatedStatusColumn = $db->single();
                                    echo "<p>Updated status ENUM: <code>{$updatedStatusColumn['Type']}</code></p>";
                                } else {
                                    echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Failed to update status ENUM.</div>";
                                }
                            }
                        } else {
                            echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Could not find status column in shipments table.</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Update Tracking Updates Table</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Check if latitude and longitude columns exist in tracking_updates table
                        $db->query("SHOW COLUMNS FROM tracking_updates LIKE 'latitude'");
                        $latitudeColumn = $db->single();
                        
                        $db->query("SHOW COLUMNS FROM tracking_updates LIKE 'longitude'");
                        $longitudeColumn = $db->single();
                        
                        if (!$latitudeColumn || !$longitudeColumn) {
                            // Add latitude and longitude columns if they don't exist
                            $alterQuery = "ALTER TABLE tracking_updates ";
                            
                            if (!$latitudeColumn) {
                                $alterQuery .= "ADD COLUMN latitude DECIMAL(10, 8) NULL, ";
                            }
                            
                            if (!$longitudeColumn) {
                                $alterQuery .= "ADD COLUMN longitude DECIMAL(11, 8) NULL, ";
                            }
                            
                            // Remove trailing comma and space
                            $alterQuery = rtrim($alterQuery, ", ");
                            
                            $db->query($alterQuery);
                            
                            if ($db->execute()) {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully added latitude and longitude columns to tracking_updates table.</div>";
                            } else {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Failed to add latitude and longitude columns to tracking_updates table.</div>";
                            }
                        } else {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Latitude and longitude columns already exist in tracking_updates table.</div>";
                        }
                        
                        // Check if notes column exists in tracking_updates table
                        $db->query("SHOW COLUMNS FROM tracking_updates LIKE 'notes'");
                        $notesColumn = $db->single();
                        
                        if (!$notesColumn) {
                            // Add notes column if it doesn't exist
                            $db->query("ALTER TABLE tracking_updates ADD COLUMN notes TEXT NULL");
                            
                            if ($db->execute()) {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully added notes column to tracking_updates table.</div>";
                            } else {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Failed to add notes column to tracking_updates table.</div>";
                            }
                        } else {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Notes column already exists in tracking_updates table.</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Create Geocoding Cache Table</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Check if geocoding_cache table exists
                        $db->query("SHOW TABLES LIKE 'geocoding_cache'");
                        $tableExists = $db->rowCount() > 0;
                        
                        if (!$tableExists) {
                            // Create the geocoding_cache table
                            $db->query("CREATE TABLE geocoding_cache (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                query_type ENUM('geocode', 'reverse') NOT NULL,
                                query_string VARCHAR(255) NOT NULL,
                                result TEXT NOT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                INDEX (query_type, query_string(191))
                            )");
                            
                            if ($db->execute()) {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully created geocoding_cache table.</div>";
                            } else {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Failed to create geocoding_cache table.</div>";
                            }
                        } else {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Geocoding cache table already exists.</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Create System Settings Table</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Check if system_settings table exists
                        $db->query("SHOW TABLES LIKE 'system_settings'");
                        $tableExists = $db->rowCount() > 0;
                        
                        if (!$tableExists) {
                            // Create the system_settings table
                            $db->query("CREATE TABLE system_settings (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                setting_key VARCHAR(100) NOT NULL UNIQUE,
                                setting_value TEXT,
                                setting_group VARCHAR(50) DEFAULT 'general',
                                is_public TINYINT(1) DEFAULT 1,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            )");
                            
                            if ($db->execute()) {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully created system_settings table.</div>";
                                
                                // Insert default settings
                                $defaultSettings = [
                                    ['enable_geocoding', '1', 'geocoding', 1],
                                    ['geocoding_provider', 'nominatim', 'geocoding', 1],
                                    ['geocoding_api_key', '', 'geocoding', 0],
                                    ['enable_email_notifications', '1', 'notifications', 1],
                                    ['company_name', 'TrackLogistics', 'general', 1],
                                    ['company_email', '<EMAIL>', 'general', 1],
                                    ['company_phone', '+****************', 'general', 1],
                                    ['company_address', '123 Shipping Lane, Logistics City, LC 12345', 'general', 1],
                                    ['maintenance_mode', '0', 'system', 1]
                                ];
                                
                                $insertCount = 0;
                                foreach ($defaultSettings as $setting) {
                                    $db->query("INSERT INTO system_settings (setting_key, setting_value, setting_group, is_public) VALUES (?, ?, ?, ?)");
                                    if ($db->execute($setting)) {
                                        $insertCount++;
                                    }
                                }
                                
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully inserted {$insertCount} default settings.</div>";
                            } else {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Failed to create system_settings table.</div>";
                            }
                        } else {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> System settings table already exists.</div>";
                            
                            // Check if geocoding settings exist
                            $db->query("SELECT COUNT(*) as count FROM system_settings WHERE setting_group = 'geocoding'");
                            $geocodingSettingsCount = $db->single()['count'];
                            
                            if ($geocodingSettingsCount == 0) {
                                // Insert geocoding settings
                                $geocodingSettings = [
                                    ['enable_geocoding', '1', 'geocoding', 1],
                                    ['geocoding_provider', 'nominatim', 'geocoding', 1],
                                    ['geocoding_api_key', '', 'geocoding', 0]
                                ];
                                
                                $insertCount = 0;
                                foreach ($geocodingSettings as $setting) {
                                    $db->query("INSERT INTO system_settings (setting_key, setting_value, setting_group, is_public) VALUES (?, ?, ?, ?)");
                                    if ($db->execute($setting)) {
                                        $insertCount++;
                                    }
                                }
                                
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully inserted {$insertCount} geocoding settings.</div>";
                            } else {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Geocoding settings already exist.</div>";
                            }
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="admin-actions mt-4">
            <a href="check_status_enum_new.php" class="btn secondary-btn"><i class="fas fa-search"></i> Check Database Status</a>
            <a href="admin/index.php" class="btn primary-btn"><i class="fas fa-arrow-left"></i> Return to Dashboard</a>
        </div>
    </div>
</section>

<style>
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.mt-4 {
    margin-top: 20px;
}
</style>

<?php include_once 'includes/footer.php'; ?>
