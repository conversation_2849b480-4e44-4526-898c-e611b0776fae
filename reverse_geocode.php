<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/geocoding.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if latitude and longitude are provided
if (empty($_GET['lat']) || empty($_GET['lng'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Latitude and longitude are required'
    ]);
    exit;
}

// Get the coordinates from the query string
$latitude = (float)$_GET['lat'];
$longitude = (float)$_GET['lng'];

// Validate coordinates
if ($latitude < -90 || $latitude > 90 || $longitude < -180 || $longitude > 180) {
    echo json_encode([
        'success' => false,
        'error' => 'Invalid coordinates'
    ]);
    exit;
}

// Initialize geocoding helper
$geocoder = new GeocodingHelper();

// Try to reverse geocode the coordinates
$result = $geocoder->reverseGeocode($latitude, $longitude);

if ($result) {
    echo json_encode([
        'success' => true,
        'result' => $result
    ]);
} else {
    echo json_encode([
        'success' => false,
        'error' => 'Could not reverse geocode the coordinates'
    ]);
}
