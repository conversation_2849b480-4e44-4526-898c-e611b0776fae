<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>Adding Visual Route Test Data</h1>";

try {
    // First, clear existing tracking data for a clean test
    $conn->exec("DELETE FROM tracking_updates WHERE shipment_id IN (SELECT id FROM shipments WHERE tracking_number = 'VISUAL123')");
    $conn->exec("DELETE FROM shipments WHERE tracking_number = 'VISUAL123'");
    
    echo "<p>Cleared existing test data.</p>";
    
    // Create a test shipment
    $stmt = $conn->prepare("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, estimated_delivery)
                          VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    $trackingNumber = 'VISUAL123';
    $customerName = 'Visual Route Test';
    $origin = 'Miami, USA';
    $destination = 'Seattle, USA';
    $status = 'in_transit';
    $createdAt = date('Y-m-d H:i:s', strtotime("-10 days"));
    $estimatedDelivery = date('Y-m-d', strtotime("+5 days"));
    
    $stmt->execute([$trackingNumber, $customerName, $origin, $destination, $status, $createdAt, $estimatedDelivery]);
    $shipmentId = $conn->lastInsertId();
    
    echo "<p>Created test shipment with ID: $shipmentId</p>";
    
    // Define tracking points with exact coordinates for a visually distinct route
    $trackingPoints = [
        // Origin - Miami
        [
            'location' => 'Miami International Distribution Center, USA',
            'status' => 'pending',
            'lat' => 25.7617,
            'lng' => -80.1918,
            'notes' => 'Package received at origin facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-10 days'))
        ],
        // First transit point - Atlanta
        [
            'location' => 'Atlanta Sorting Facility, USA',
            'status' => 'in_transit',
            'lat' => 33.7490,
            'lng' => -84.3880,
            'notes' => 'Package in transit through Atlanta',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-8 days'))
        ],
        // Second transit point - Nashville
        [
            'location' => 'Nashville Distribution Center, USA',
            'status' => 'in_transit',
            'lat' => 36.1627,
            'lng' => -86.7816,
            'notes' => 'Package processed at Nashville distribution center',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-7 days'))
        ],
        // Third transit point - St. Louis
        [
            'location' => 'St. Louis Transit Hub, USA',
            'status' => 'in_transit',
            'lat' => 38.6270,
            'lng' => -90.1994,
            'notes' => 'Package in transit through St. Louis',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-6 days'))
        ],
        // Fourth transit point - Kansas City
        [
            'location' => 'Kansas City Sorting Facility, USA',
            'status' => 'in_transit',
            'lat' => 39.0997,
            'lng' => -94.5786,
            'notes' => 'Package processed at Kansas City sorting facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-5 days'))
        ],
        // Fifth transit point - Denver
        [
            'location' => 'Denver Distribution Center, USA',
            'status' => 'in_transit',
            'lat' => 39.7392,
            'lng' => -104.9903,
            'notes' => 'Package processed at Denver distribution center',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-4 days'))
        ],
        // Sixth transit point - Salt Lake City
        [
            'location' => 'Salt Lake City Transit Hub, USA',
            'status' => 'in_transit',
            'lat' => 40.7608,
            'lng' => -111.8910,
            'notes' => 'Package in transit through Salt Lake City',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-3 days'))
        ],
        // Seventh transit point - Boise
        [
            'location' => 'Boise Sorting Facility, USA',
            'status' => 'in_transit',
            'lat' => 43.6150,
            'lng' => -116.2023,
            'notes' => 'Package processed at Boise sorting facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-2 days'))
        ],
        // Eighth transit point - Portland
        [
            'location' => 'Portland Distribution Center, USA',
            'status' => 'in_transit',
            'lat' => 45.5051,
            'lng' => -122.6750,
            'notes' => 'Package in transit to final destination',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ],
        // Current location - Seattle
        [
            'location' => 'Seattle Distribution Center, USA',
            'status' => 'in_transit',
            'lat' => 47.6062,
            'lng' => -122.3321,
            'notes' => 'Package arrived at Seattle distribution center',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-6 hours'))
        ]
    ];
    
    // Insert tracking updates
    $insertStmt = $conn->prepare("
        INSERT INTO tracking_updates 
        (shipment_id, location, status, latitude, longitude, notes, timestamp)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($trackingPoints as $point) {
        $insertStmt->execute([
            $shipmentId,
            $point['location'],
            $point['status'],
            $point['lat'],
            $point['lng'],
            $point['notes'],
            $point['timestamp']
        ]);
        
        echo "<p>Added tracking point: {$point['location']} ({$point['status']})</p>";
    }
    
    echo "<h2>Visual Route Data Added Successfully</h2>";
    echo "<p>Use tracking number <strong>VISUAL123</strong> to test the tracking map with a visually distinct route.</p>";
    echo "<p><a href='tracking/index.php?tracking_number=VISUAL123'>View Tracking Map</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
