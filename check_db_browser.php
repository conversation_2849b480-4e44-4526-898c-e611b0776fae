<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

echo "<h1>Database Connection Check</h1>";

try {
    // Check if the connection is established
    echo "<p>Connection established successfully.</p>";
    
    // Check if tables exist
    $tables = ['users', 'shipments', 'tracking_updates', 'system_settings', 'geocoding_cache'];
    
    echo "<h2>Tables Check</h2>";
    echo "<ul>";
    foreach ($tables as $table) {
        $db->query("SELECT 1 FROM $table LIMIT 1");
        try {
            $db->execute();
            echo "<li>Table '$table' exists.</li>";
        } catch (Exception $e) {
            echo "<li>Table '$table' does not exist or is not accessible: " . $e->getMessage() . "</li>";
        }
    }
    echo "</ul>";
    
    // Check if there are users in the database
    $db->query("SELECT COUNT(*) as count FROM users");
    $result = $db->single();
    echo "<p>Number of users: " . $result['count'] . "</p>";
    
    // Check if there are shipments in the database
    $db->query("SELECT COUNT(*) as count FROM shipments");
    $result = $db->single();
    echo "<p>Number of shipments: " . $result['count'] . "</p>";
    
    // Check if there are tracking updates in the database
    $db->query("SELECT COUNT(*) as count FROM tracking_updates");
    $result = $db->single();
    echo "<p>Number of tracking updates: " . $result['count'] . "</p>";
    
    // Check if there are system settings in the database
    $db->query("SELECT COUNT(*) as count FROM system_settings");
    $result = $db->single();
    echo "<p>Number of system settings: " . $result['count'] . "</p>";
    
    echo "<p>Database check completed successfully.</p>";
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
