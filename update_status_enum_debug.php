<?php
/**
 * Debug version of update_status_enum_new.php
 * This script will help identify the exact error when creating tables
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Debug Database Schema';

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-bug"></i> Debug Database Schema</h1>
            <div class="admin-actions">
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Debug Geocoding Cache Table Creation</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Check if geocoding_cache table exists
                        $db->query("SHOW TABLES LIKE 'geocoding_cache'");
                        $tableExists = $db->rowCount() > 0;
                        
                        echo "<p>Table exists check: " . ($tableExists ? "Yes" : "No") . "</p>";
                        
                        if (!$tableExists) {
                            // Create the geocoding_cache table
                            $createTableSQL = "CREATE TABLE geocoding_cache (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                query_type ENUM('geocode', 'reverse') NOT NULL,
                                query_string VARCHAR(255) NOT NULL,
                                result TEXT NOT NULL,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                INDEX (query_type, query_string(191))
                            )";
                            
                            echo "<p>SQL to execute: <pre>" . htmlspecialchars($createTableSQL) . "</pre></p>";
                            
                            $db->query($createTableSQL);
                            $result = $db->execute();
                            
                            echo "<p>Execute result: " . ($result ? "Success" : "Failed") . "</p>";
                            
                            if (!$result) {
                                echo "<p>Error: " . print_r($db->error, true) . "</p>";
                                
                                // Try alternative approach
                                echo "<h3>Trying alternative approach with direct PDO</h3>";
                                
                                try {
                                    global $conn;
                                    $stmt = $conn->prepare($createTableSQL);
                                    $pdoResult = $stmt->execute();
                                    echo "<p>PDO Execute result: " . ($pdoResult ? "Success" : "Failed") . "</p>";
                                    
                                    if (!$pdoResult) {
                                        echo "<p>PDO Error: " . print_r($stmt->errorInfo(), true) . "</p>";
                                    }
                                } catch (PDOException $e) {
                                    echo "<p>PDO Exception: " . $e->getMessage() . "</p>";
                                }
                            }
                        } else {
                            echo "<p>Geocoding cache table already exists.</p>";
                        }
                    } catch (Exception $e) {
                        echo "<p>Exception: " . $e->getMessage() . "</p>";
                        echo "<p>Trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Debug System Settings Table Creation</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Check if system_settings table exists
                        $db->query("SHOW TABLES LIKE 'system_settings'");
                        $tableExists = $db->rowCount() > 0;
                        
                        echo "<p>Table exists check: " . ($tableExists ? "Yes" : "No") . "</p>";
                        
                        if (!$tableExists) {
                            // Create the system_settings table
                            $createTableSQL = "CREATE TABLE system_settings (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                setting_key VARCHAR(100) NOT NULL UNIQUE,
                                setting_value TEXT,
                                setting_group VARCHAR(50) DEFAULT 'general',
                                is_public TINYINT(1) DEFAULT 1,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                            )";
                            
                            echo "<p>SQL to execute: <pre>" . htmlspecialchars($createTableSQL) . "</pre></p>";
                            
                            $db->query($createTableSQL);
                            $result = $db->execute();
                            
                            echo "<p>Execute result: " . ($result ? "Success" : "Failed") . "</p>";
                            
                            if (!$result) {
                                echo "<p>Error: " . print_r($db->error, true) . "</p>";
                                
                                // Try alternative approach
                                echo "<h3>Trying alternative approach with direct PDO</h3>";
                                
                                try {
                                    global $conn;
                                    $stmt = $conn->prepare($createTableSQL);
                                    $pdoResult = $stmt->execute();
                                    echo "<p>PDO Execute result: " . ($pdoResult ? "Success" : "Failed") . "</p>";
                                    
                                    if (!$pdoResult) {
                                        echo "<p>PDO Error: " . print_r($stmt->errorInfo(), true) . "</p>";
                                    }
                                } catch (PDOException $e) {
                                    echo "<p>PDO Exception: " . $e->getMessage() . "</p>";
                                }
                            }
                        } else {
                            echo "<p>System settings table already exists.</p>";
                        }
                    } catch (Exception $e) {
                        echo "<p>Exception: " . $e->getMessage() . "</p>";
                        echo "<p>Trace: <pre>" . $e->getTraceAsString() . "</pre></p>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Database Information</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Get MySQL version
                        $db->query("SELECT VERSION() as version");
                        $versionInfo = $db->single();
                        echo "<p><strong>MySQL Version:</strong> " . ($versionInfo['version'] ?? 'Unknown') . "</p>";
                        
                        // Get database name
                        echo "<p><strong>Database Name:</strong> " . DB_NAME . "</p>";
                        
                        // Get database charset
                        $db->query("SHOW VARIABLES LIKE 'character_set_database'");
                        $charsetInfo = $db->single();
                        echo "<p><strong>Database Charset:</strong> " . ($charsetInfo['Value'] ?? 'Unknown') . "</p>";
                        
                        // Get database collation
                        $db->query("SHOW VARIABLES LIKE 'collation_database'");
                        $collationInfo = $db->single();
                        echo "<p><strong>Database Collation:</strong> " . ($collationInfo['Value'] ?? 'Unknown') . "</p>";
                        
                        // List all tables
                        $db->query("SHOW TABLES");
                        $tables = $db->resultSet();
                        
                        echo "<p><strong>Existing Tables:</strong></p>";
                        echo "<ul>";
                        foreach ($tables as $table) {
                            $tableName = reset($table);
                            echo "<li>" . htmlspecialchars($tableName) . "</li>";
                        }
                        echo "</ul>";
                    } catch (Exception $e) {
                        echo "<p>Exception: " . $e->getMessage() . "</p>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="admin-actions mt-4">
            <a href="update_status_enum_new.php" class="btn primary-btn"><i class="fas fa-wrench"></i> Try Regular Update</a>
            <a href="admin/index.php" class="btn secondary-btn"><i class="fas fa-arrow-left"></i> Return to Dashboard</a>
        </div>
    </div>
</section>

<style>
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

code, pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    padding: 10px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.mt-4 {
    margin-top: 20px;
}
</style>

<?php include_once 'includes/footer.php'; ?>
