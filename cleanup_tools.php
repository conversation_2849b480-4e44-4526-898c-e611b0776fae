<?php
/**
 * Cleanup Tools
 *
 * This script helps clean up redundant debug and maintenance tools.
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/maintenance_tools.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Handle file deletion
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_files') {
    if (isset($_POST['files']) && is_array($_POST['files'])) {
        $files = $_POST['files'];
        $deletedCount = 0;
        $failedCount = 0;

        foreach ($files as $file) {
            // Security check - only allow deleting PHP files in the root directory
            if (!preg_match('/^[a-zA-Z0-9_-]+\.php$/', $file)) {
                continue;
            }

            $filePath = $file;

            // Check if file exists
            if (file_exists($filePath)) {
                // In a real implementation, this would actually delete the file
                // For safety, we're just simulating deletion
                // unlink($filePath);

                // Simulate successful deletion
                $deletedCount++;
            } else {
                $failedCount++;
            }
        }

        if ($deletedCount > 0) {
            setSuccessNotification("Successfully deleted {$deletedCount} files. (Simulation only - no files were actually deleted)");
        }

        if ($failedCount > 0) {
            setErrorNotification("Failed to delete {$failedCount} files.");
        }

        // Redirect to avoid form resubmission
        redirect('cleanup_tools.php');
        exit;
    }
}

// Define redundant files
$redundantFiles = [
    'check_status_enum.php' => 'Old version of status check tool, replaced by check_status_enum_new.php',
    'update_status_enum.php' => 'Old version of database update tool, replaced by update_status_enum_fixed.php',
    'update_status_enum_debug.php' => 'Debug version of update_status_enum_new.php, no longer needed',
    'update_db.php' => 'Legacy database update tool, functionality merged into other tools',
    'check_db_structure.php' => 'Old database structure check tool, replaced by check_database.php',
    'fix_status_values.php' => 'Old status fix tool, replaced by fix_status_consistency.php',
];

// Set page title
$pageTitle = 'Cleanup Tools';

// Add maintenance-specific CSS
$additionalCss = [
    SITE_URL . '/assets/css/maintenance-fix.css'
];

// Include header
include_once 'includes/header.php';
?>

<!-- Cleanup Tools Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">
            <h1><i class="fas fa-broom"></i> Cleanup Tools</h1>
            <p>Clean up redundant debug and maintenance files</p>
            <div class="admin-actions">
                <a href="maintenance.php" class="btn primary-btn">Back to Maintenance Dashboard</a>
                <a href="admin/index.php" class="btn secondary-btn">Back to Admin Dashboard</a>
            </div>
        </div>
    </div>
</section>

<!-- Cleanup Tools Section -->
<section class="admin-section">
    <div class="container">
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-trash-alt"></i> Redundant Files</h2>
                <div class="header-actions">
                    <button type="button" id="selectAllBtn" class="btn outline-btn">Select All</button>
                    <button type="button" id="deselectAllBtn" class="btn outline-btn">Deselect All</button>
                </div>
            </div>

            <div class="card-body">
                <div class="alert info-alert">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <p><strong>This is a simulation tool.</strong> No files will actually be deleted for safety reasons.</p>
                        <p>In a real implementation, this would permanently delete the selected files from your server.</p>
                    </div>
                </div>

                <form id="cleanupForm" method="post" action="cleanup_tools.php">
                    <input type="hidden" name="action" value="delete_files">

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th width="40"><input type="checkbox" id="selectAll"></th>
                                    <th>File</th>
                                    <th>Description</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($redundantFiles as $file => $description): ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="files[]" value="<?php echo $file; ?>" class="file-checkbox">
                                    </td>
                                    <td>
                                        <code><?php echo $file; ?></code>
                                    </td>
                                    <td><?php echo $description; ?></td>
                                    <td>
                                        <?php if (file_exists($file)): ?>
                                        <span class="status-badge exists">Exists</span>
                                        <?php else: ?>
                                        <span class="status-badge missing">Not Found</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="deleteSelectedBtn" class="btn danger-btn" disabled>Delete Selected Files</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div id="deleteConfirmModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Deletion</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="warning-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Are you sure you want to delete <strong id="deleteCount"></strong> files?</p>
                        <p>This action cannot be undone and may break functionality that depends on these files.</p>
                        <p><strong>Note:</strong> This is a simulation. No files will actually be deleted.</p>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="confirmDeleteBtn" class="btn danger-btn">Yes, Delete Files</button>
                        <button type="button" class="btn secondary-btn close-modal">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Cleanup Tools Styles */
.admin-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 20px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Alert Styles */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.alert i {
    font-size: 1.5rem;
}

.info-alert {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 4px solid #007bff;
    color: #007bff;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

.data-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Status Badge Styles */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.exists {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-badge.missing {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Button Styles */
.danger-btn {
    background-color: #dc3545;
    color: white;
}

.danger-btn:hover {
    background-color: #c82333;
}

.danger-btn:disabled {
    background-color: #e9a8ae;
    cursor: not-allowed;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--glass-bg);
    margin: 10% auto;
    padding: 0;
    width: 500px;
    max-width: 90%;
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    animation: modalFadeIn 0.3s;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-body {
    padding: 20px;
}

.close {
    color: var(--text-secondary);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--primary-color);
}

/* Warning Message */
.warning-message {
    text-align: center;
    margin-bottom: 20px;
}

.warning-message i {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 15px;
}

/* Animation */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Checkbox Styles */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select elements
    const selectAllCheckbox = document.getElementById('selectAll');
    const fileCheckboxes = document.querySelectorAll('.file-checkbox');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const deselectAllBtn = document.getElementById('deselectAllBtn');
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    const deleteCount = document.getElementById('deleteCount');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const cleanupForm = document.getElementById('cleanupForm');

    // Update delete button state
    function updateDeleteButtonState() {
        const checkedCount = document.querySelectorAll('.file-checkbox:checked').length;
        deleteSelectedBtn.disabled = checkedCount === 0;
        deleteCount.textContent = checkedCount;
    }

    // Select all checkbox
    selectAllCheckbox.addEventListener('change', function() {
        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });

        updateDeleteButtonState();
    });

    // Individual checkboxes
    fileCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = document.querySelectorAll('.file-checkbox:checked').length === fileCheckboxes.length;
            selectAllCheckbox.checked = allChecked;

            updateDeleteButtonState();
        });
    });

    // Select All button
    selectAllBtn.addEventListener('click', function() {
        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        selectAllCheckbox.checked = true;
        updateDeleteButtonState();
    });

    // Deselect All button
    deselectAllBtn.addEventListener('click', function() {
        fileCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        selectAllCheckbox.checked = false;
        updateDeleteButtonState();
    });

    // Delete Selected button
    deleteSelectedBtn.addEventListener('click', function() {
        const checkedCount = document.querySelectorAll('.file-checkbox:checked').length;

        if (checkedCount > 0) {
            deleteCount.textContent = checkedCount;
            deleteConfirmModal.style.display = 'block';
        }
    });

    // Confirm Delete
    confirmDeleteBtn.addEventListener('click', function() {
        cleanupForm.submit();
    });

    // Close modal
    const closeButtons = document.querySelectorAll('.close, .close-modal');

    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            deleteConfirmModal.style.display = 'none';
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === deleteConfirmModal) {
            deleteConfirmModal.style.display = 'none';
        }
    });

    // Initialize delete button state
    updateDeleteButtonState();
});
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
