<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    setErrorNotification('You must be logged in as an administrator to run this script.');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Update Users Table';

// Include header
include_once 'includes/header.php';
?>

<div class="container" style="padding-top: 100px; padding-bottom: 50px;">
    <div class="glass-card">
        <h1>Update Users Table</h1>
        
        <?php
        // Run the database update
        try {
            // Check if first_name column exists
            $stmt = $conn->prepare("SELECT COUNT(*) as column_exists
                                  FROM INFORMATION_SCHEMA.COLUMNS
                                  WHERE TABLE_SCHEMA = ?
                                    AND TABLE_NAME = 'users'
                                    AND COLUMN_NAME = 'first_name'");
            $stmt->execute([DB_NAME]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Add the columns if they don't exist
            if ($result['column_exists'] == 0) {
                $conn->exec("ALTER TABLE users ADD COLUMN first_name VARCHAR(100) DEFAULT NULL");
                echo "<p class='success'>✓ Added first_name column to users table.</p>";
            } else {
                echo "<p class='info'>ℹ first_name column already exists.</p>";
            }

            // Check if last_name column exists
            $stmt = $conn->prepare("SELECT COUNT(*) as column_exists
                                  FROM INFORMATION_SCHEMA.COLUMNS
                                  WHERE TABLE_SCHEMA = ?
                                    AND TABLE_NAME = 'users'
                                    AND COLUMN_NAME = 'last_name'");
            $stmt->execute([DB_NAME]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($result['column_exists'] == 0) {
                $conn->exec("ALTER TABLE users ADD COLUMN last_name VARCHAR(100) DEFAULT NULL");
                echo "<p class='success'>✓ Added last_name column to users table.</p>";
            } else {
                echo "<p class='info'>ℹ last_name column already exists.</p>";
            }

            // Update existing users to set default values for first_name and last_name
            $stmt = $conn->prepare("UPDATE users SET 
                first_name = username,
                last_name = ''
                WHERE first_name IS NULL");
            $stmt->execute();
            $updatedRows = $stmt->rowCount();
            echo "<p class='success'>✓ Updated $updatedRows users with default first name values.</p>";

            echo "<div class='success'><h2>Database updated successfully!</h2></div>";
        } catch(PDOException $e) {
            echo "<div class='error'><h2>Database update failed:</h2><p>" . $e->getMessage() . "</p></div>";
        }
        ?>
        
        <div class="update-footer" style="margin-top: 20px;">
            <a href="admin/index.php" class="btn primary-btn">Go to Admin Dashboard</a>
            <a href="user/index.php" class="btn outline-btn">Go to User Dashboard</a>
        </div>
    </div>
</div>

<style>
    .success {
        color: #28a745;
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
    }
    
    .info {
        color: #17a2b8;
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
    }
    
    .error {
        color: #dc3545;
        background-color: rgba(220, 53, 69, 0.1);
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
    }
    
    .update-footer {
        display: flex;
        gap: 10px;
        margin-top: 20px;
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
