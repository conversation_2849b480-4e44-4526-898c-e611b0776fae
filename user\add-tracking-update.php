<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';
require_once '../includes/geocoding.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Check if user has permission to add tracking updates
if (!canAddTrackingUpdate()) {
    setErrorNotification('You do not have permission to add tracking updates');
    header('Location: index.php');
    exit;
}

// Get shipment ID from URL
$shipmentId = isset($_GET['shipment_id']) ? (int)$_GET['shipment_id'] : 0;

if ($shipmentId <= 0) {
    setErrorNotification('Invalid shipment ID');
    header('Location: shipments.php');
    exit;
}

// Get shipment details
try {
    $stmt = $conn->prepare("SELECT * FROM shipments WHERE id = ?");
    $stmt->execute([$shipmentId]);
    $shipment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$shipment) {
        setErrorNotification('Shipment not found');
        header('Location: shipments.php');
        exit;
    }
} catch (PDOException $e) {
    setErrorNotification('Error retrieving shipment: ' . $e->getMessage());
    header('Location: shipments.php');
    exit;
}

// Get existing tracking updates
try {
    $stmt = $conn->prepare("SELECT * FROM tracking_updates WHERE shipment_id = ? ORDER BY timestamp DESC");
    $stmt->execute([$shipmentId]);
    $trackingUpdates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $trackingUpdates = [];
}

// Initialize geocoding helper
$geocoder = new GeocodingHelper();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_update'])) {
    $location = sanitize($_POST['location']);
    $status = sanitize($_POST['status']);
    $notes = sanitize($_POST['notes']);
    $latitude = !empty($_POST['latitude']) ? (float)$_POST['latitude'] : null;
    $longitude = !empty($_POST['longitude']) ? (float)$_POST['longitude'] : null;

    // If coordinates are not provided but geocoding is enabled, try to geocode the location
    if (($latitude === null || $longitude === null) && getSetting('enable_geocoding', '1') === '1') {
        $geocodeResult = $geocoder->geocode($location);
        if ($geocodeResult) {
            $latitude = $geocodeResult['latitude'];
            $longitude = $geocodeResult['longitude'];
        }
    }

    // Validate input
    if (empty($location) || empty($status)) {
        setErrorNotification('Location and status are required fields', 10000);
    } else {
        try {
            // Add tracking update
            $result = addTrackingUpdate($shipmentId, $location, $status, $latitude, $longitude, $notes);

            if ($result) {
                // Update shipment status
                updateShipmentStatus($shipmentId, $status);

                // Update delivered_at date if status is delivered
                if ($status === 'delivered') {
                    $stmt = $conn->prepare("UPDATE shipments SET delivered_at = NOW() WHERE id = ? AND delivered_at IS NULL");
                    $stmt->execute([$shipmentId]);
                }

                // Send email notification if email notifications are enabled
                if (getSetting('enable_email_notifications', '1') === '1') {
                    // Check if we have an email to send to
                    $customerEmail = null;

                    // Try to get email from receiver_email first
                    if (!empty($shipment['receiver_email'])) {
                        $customerEmail = $shipment['receiver_email'];
                    }
                    // If no receiver email, try shopper_email
                    else if (!empty($shipment['shopper_email'])) {
                        $customerEmail = $shipment['shopper_email'];
                    }

                    // If we have an email, send the notification
                    if ($customerEmail && filter_var($customerEmail, FILTER_VALIDATE_EMAIL)) {
                        try {
                            sendShipmentStatusUpdateEmail($shipment, $status, $customerEmail);
                            // Log successful email
                            debugLog("Status update email sent for shipment #{$shipmentId} to {$customerEmail}");
                        } catch (Exception $e) {
                            // Log email error but don't affect the tracking update result
                            debugLog("Error sending status update email for shipment #{$shipmentId}: " . $e->getMessage());
                        }
                    }
                }

                setSuccessNotification('Tracking update added successfully', 10000);
                header('Location: add-tracking-update.php?shipment_id=' . $shipmentId);
                exit;
            } else {
                setErrorNotification('Error adding tracking update. Please try again.', 10000);
            }
        } catch (PDOException $e) {
            setErrorNotification('Database error: ' . $e->getMessage(), 10000);
        }
    }
}

// Set page title
$pageTitle = 'Add Tracking Update';

// Include header
include_once 'includes/header.php';
?>

<div class="container">
    <div class="page-header">
        <h1>Add Tracking Update</h1>
        <p>Add a new tracking update for shipment #<?php echo htmlspecialchars($shipment['tracking_number']); ?></p>
        <div class="header-actions">
            <a href="shipments.php" class="btn outline-btn">Back to Shipments</a>
            <a href="../tracking/index.php?tracking_number=<?php echo htmlspecialchars($shipment['tracking_number']); ?>" class="btn secondary-btn" target="_blank">View Tracking Page</a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="shipment-info glass-card">
                <h3>Shipment Information</h3>
                <div class="info-item">
                    <span class="info-label">Tracking Number:</span>
                    <span class="info-value"><?php echo htmlspecialchars($shipment['tracking_number']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Customer:</span>
                    <span class="info-value"><?php echo htmlspecialchars($shipment['customer_name']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Origin:</span>
                    <span class="info-value"><?php echo htmlspecialchars($shipment['origin']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Destination:</span>
                    <span class="info-value"><?php echo htmlspecialchars($shipment['destination']); ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Current Status:</span>
                    <span class="info-value status-badge status-<?php echo strtolower(str_replace(' ', '-', $shipment['status'])); ?>">
                        <?php echo ucfirst(str_replace('_', ' ', $shipment['status'])); ?>
                    </span>
                </div>
                <div class="info-item">
                    <span class="info-label">Created:</span>
                    <span class="info-value"><?php echo date('M d, Y', strtotime($shipment['created_at'])); ?></span>
                </div>
                <?php if (!empty($shipment['estimated_delivery'])): ?>
                <div class="info-item">
                    <span class="info-label">Estimated Delivery:</span>
                    <span class="info-value"><?php echo date('M d, Y', strtotime($shipment['estimated_delivery'])); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="col-md-8">
            <div class="update-form glass-card">
                <h3>New Tracking Update</h3>
                <form action="add-tracking-update.php?shipment_id=<?php echo $shipmentId; ?>" method="POST">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="location">Location</label>
                            <input type="text" id="location" name="location" required>
                        </div>

                        <div class="form-group">
                            <label for="status">Status</label>
                            <select id="status" name="status" required>
                                <option value="">Select a status</option>
                                <option value="pending">Pending</option>
                                <option value="in_transit">In Transit</option>
                                <option value="delivered">Delivered</option>
                                <option value="delayed">Delayed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="latitude">Latitude (optional)</label>
                            <input type="number" step="any" id="latitude" name="latitude" placeholder="e.g. 40.7128">
                        </div>

                        <div class="form-group">
                            <label for="longitude">Longitude (optional)</label>
                            <input type="number" step="any" id="longitude" name="longitude" placeholder="e.g. -74.0060">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="notes">Notes (optional)</label>
                        <textarea id="notes" name="notes" rows="3" placeholder="Add any additional information about this update"></textarea>
                    </div>

                    <div class="form-actions">
                        <button type="submit" name="add_update" class="btn primary-btn">Add Update</button>
                    </div>
                </form>
            </div>

            <div class="tracking-history glass-card">
                <h3>Tracking History</h3>
                <?php if (empty($trackingUpdates)): ?>
                    <div class="empty-state">
                        <p>No tracking updates found for this shipment.</p>
                    </div>
                <?php else: ?>
                    <div class="tracking-timeline">
                        <?php foreach ($trackingUpdates as $update): ?>
                            <div class="timeline-item">
                                <div class="timeline-marker <?php echo getStatusClass($update['status']); ?>"></div>
                                <div class="timeline-content">
                                    <div class="timeline-header">
                                        <div class="timeline-title"><?php echo htmlspecialchars($update['location']); ?></div>
                                        <div class="timeline-time"><?php echo date('M d, Y, g:i a', strtotime($update['timestamp'])); ?></div>
                                    </div>
                                    <div class="timeline-status">
                                        <span class="status-badge <?php echo getStatusClass($update['status']); ?>">
                                            <?php echo ucfirst(str_replace('_', ' ', $update['status'])); ?>
                                        </span>
                                    </div>
                                    <?php if (!empty($update['notes'])): ?>
                                        <div class="timeline-notes"><?php echo htmlspecialchars($update['notes']); ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const locationInput = document.getElementById('location');
        const latitudeInput = document.getElementById('latitude');
        const longitudeInput = document.getElementById('longitude');
        const locationContainer = locationInput.parentNode;

        // Create a container for the map
        const mapContainer = document.createElement('div');
        mapContainer.id = 'location-map';
        mapContainer.style.height = '300px';
        mapContainer.style.marginTop = '10px';
        mapContainer.style.display = 'none';
        mapContainer.style.borderRadius = '8px';
        mapContainer.style.overflow = 'hidden';

        // Add map container after the location input
        locationContainer.appendChild(mapContainer);

        // Create map toggle button
        const toggleMapBtn = document.createElement('button');
        toggleMapBtn.type = 'button';
        toggleMapBtn.className = 'btn outline-btn';
        toggleMapBtn.innerHTML = '<i class="fas fa-map"></i> Toggle Map';
        toggleMapBtn.style.marginLeft = '10px';
        toggleMapBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (mapContainer.style.display === 'none') {
                mapContainer.style.display = 'block';
                initMap();
            } else {
                mapContainer.style.display = 'none';
            }
        });

        // Add toggle button after location input
        locationInput.parentNode.appendChild(toggleMapBtn);

        // Create geocode button
        const geocodeBtn = document.createElement('button');
        geocodeBtn.type = 'button';
        geocodeBtn.className = 'btn outline-btn';
        geocodeBtn.innerHTML = '<i class="fas fa-search-location"></i> Geocode';
        geocodeBtn.style.marginLeft = '10px';
        geocodeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (locationInput.value.trim() !== '') {
                // Show loading indicator
                geocodeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Geocoding...';
                geocodeBtn.disabled = true;

                // Make a request to our server-side geocoding endpoint
                fetch('../geocode.php?address=' + encodeURIComponent(locationInput.value))
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result) {
                            latitudeInput.value = data.result.latitude;
                            longitudeInput.value = data.result.longitude;

                            // If map is visible, update it
                            if (mapContainer.style.display !== 'none' && window.locationMap) {
                                window.locationMarker.setLatLng([data.result.latitude, data.result.longitude]);
                                window.locationMap.setView([data.result.latitude, data.result.longitude], 13);
                            }
                        } else {
                            alert('Could not geocode the location. Please try a different address or enter coordinates manually.');
                        }

                        // Reset button
                        geocodeBtn.innerHTML = '<i class="fas fa-search-location"></i> Geocode';
                        geocodeBtn.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error geocoding:', error);
                        alert('Error geocoding the location. Please try again or enter coordinates manually.');

                        // Reset button
                        geocodeBtn.innerHTML = '<i class="fas fa-search-location"></i> Geocode';
                        geocodeBtn.disabled = false;
                    });
            } else {
                alert('Please enter a location to geocode.');
            }
        });

        // Add geocode button after location input
        locationInput.parentNode.appendChild(geocodeBtn);

        // Try to get current location if available
        if (navigator.geolocation) {
            const getLocationBtn = document.createElement('button');
            getLocationBtn.type = 'button';
            getLocationBtn.className = 'btn outline-btn get-location-btn';
            getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Get Current Location';
            getLocationBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Show loading indicator
                getLocationBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Getting Location...';
                getLocationBtn.disabled = true;

                navigator.geolocation.getCurrentPosition(function(position) {
                    latitudeInput.value = position.coords.latitude;
                    longitudeInput.value = position.coords.longitude;

                    // If map is visible, update it
                    if (mapContainer.style.display !== 'none' && window.locationMap) {
                        window.locationMarker.setLatLng([position.coords.latitude, position.coords.longitude]);
                        window.locationMap.setView([position.coords.latitude, position.coords.longitude], 13);
                    }

                    // Try to get address from coordinates using our server-side reverse geocoding
                    fetch(`../reverse_geocode.php?lat=${position.coords.latitude}&lng=${position.coords.longitude}`)
                        .then(response => response.json())
                        .then(data => {
                            if (data.success && data.result && data.result.formatted_address) {
                                locationInput.value = data.result.formatted_address;
                            }

                            // Reset button
                            getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Get Current Location';
                            getLocationBtn.disabled = false;
                        })
                        .catch(error => {
                            console.error('Error getting location name:', error);

                            // Reset button
                            getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Get Current Location';
                            getLocationBtn.disabled = false;
                        });
                }, function(error) {
                    console.error('Error getting location:', error);
                    alert('Unable to get your location. Please enter it manually.');

                    // Reset button
                    getLocationBtn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Get Current Location';
                    getLocationBtn.disabled = false;
                }, {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0
                });
            });

            // Add button after longitude input
            longitudeInput.parentNode.appendChild(getLocationBtn);
        }

        // Initialize map when visible
        function initMap() {
            if (!window.locationMap) {
                // Load Leaflet CSS if not already loaded
                if (!document.querySelector('link[href*="leaflet.css"]')) {
                    const leafletCss = document.createElement('link');
                    leafletCss.rel = 'stylesheet';
                    leafletCss.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
                    document.head.appendChild(leafletCss);
                }

                // Load Leaflet JS if not already loaded
                if (typeof L === 'undefined') {
                    const leafletScript = document.createElement('script');
                    leafletScript.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
                    leafletScript.onload = createMap;
                    document.head.appendChild(leafletScript);
                } else {
                    createMap();
                }
            } else {
                window.locationMap.invalidateSize();
            }
        }

        // Create the map
        function createMap() {
            // Get initial coordinates
            let initialLat = 40.7128;
            let initialLng = -74.0060;
            let initialZoom = 2;

            // Use coordinates from inputs if available
            if (latitudeInput.value && longitudeInput.value) {
                initialLat = parseFloat(latitudeInput.value);
                initialLng = parseFloat(longitudeInput.value);
                initialZoom = 13;
            }

            // Create map
            window.locationMap = L.map('location-map').setView([initialLat, initialLng], initialZoom);

            // Add tile layer
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(window.locationMap);

            // Add marker
            window.locationMarker = L.marker([initialLat, initialLng], {
                draggable: true
            }).addTo(window.locationMap);

            // Update coordinates when marker is dragged
            window.locationMarker.on('dragend', function(e) {
                const position = e.target.getLatLng();
                latitudeInput.value = position.lat.toFixed(6);
                longitudeInput.value = position.lng.toFixed(6);

                // Try to get address from coordinates
                fetch(`../reverse_geocode.php?lat=${position.lat}&lng=${position.lng}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result && data.result.formatted_address) {
                            locationInput.value = data.result.formatted_address;
                        }
                    })
                    .catch(error => {
                        console.error('Error getting location name:', error);
                    });
            });

            // Update marker when clicking on map
            window.locationMap.on('click', function(e) {
                window.locationMarker.setLatLng(e.latlng);
                latitudeInput.value = e.latlng.lat.toFixed(6);
                longitudeInput.value = e.latlng.lng.toFixed(6);

                // Try to get address from coordinates
                fetch(`../reverse_geocode.php?lat=${e.latlng.lat}&lng=${e.latlng.lng}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.result && data.result.formatted_address) {
                            locationInput.value = data.result.formatted_address;
                        }
                    })
                    .catch(error => {
                        console.error('Error getting location name:', error);
                    });
            });
        }

        // Update map when coordinates are changed manually
        latitudeInput.addEventListener('change', updateMapFromCoordinates);
        longitudeInput.addEventListener('change', updateMapFromCoordinates);

        function updateMapFromCoordinates() {
            if (latitudeInput.value && longitudeInput.value && window.locationMap) {
                const lat = parseFloat(latitudeInput.value);
                const lng = parseFloat(longitudeInput.value);

                if (!isNaN(lat) && !isNaN(lng)) {
                    window.locationMarker.setLatLng([lat, lng]);
                    window.locationMap.setView([lat, lng], 13);
                }
            }
        }
    });
</script>

<style>
    .page-header {
        margin-bottom: 30px;
        padding-top: 30px;
    }

    .page-header h1 {
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .header-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .row {
        display: flex;
        flex-wrap: wrap;
        margin: 0 -15px;
    }

    .col-md-4 {
        flex: 0 0 33.333333%;
        max-width: 33.333333%;
        padding: 0 15px;
    }

    .col-md-8 {
        flex: 0 0 66.666667%;
        max-width: 66.666667%;
        padding: 0 15px;
    }

    .glass-card {
        background-color: var(--glass-bg);
        border-radius: 16px;
        padding: 25px;
        margin-bottom: 30px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
    }

    .shipment-info h3,
    .update-form h3,
    .tracking-history h3 {
        color: var(--primary-color);
        margin-bottom: 20px;
    }

    .info-item {
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
    }

    .info-label {
        font-weight: 500;
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-bottom: 5px;
    }

    .info-value {
        font-weight: 500;
    }

    .status-badge {
        display: inline-block;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: capitalize;
    }

    .status-pending {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ff9800;
    }

    .status-in-transit {
        background-color: rgba(33, 150, 243, 0.2);
        color: #2196f3;
    }

    .status-delivered {
        background-color: rgba(76, 175, 80, 0.2);
        color: #4caf50;
    }

    .status-delayed {
        background-color: rgba(244, 67, 54, 0.2);
        color: #f44336;
    }

    .status-cancelled {
        background-color: rgba(158, 158, 158, 0.2);
        color: #9e9e9e;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }

    .form-actions {
        margin-top: 20px;
    }

    .tracking-timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 25px;
    }

    .timeline-item:last-child {
        margin-bottom: 0;
    }

    .timeline-marker {
        position: absolute;
        left: -30px;
        top: 0;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: var(--primary-color);
        z-index: 1;
    }

    .timeline-item:not(:last-child)::before {
        content: '';
        position: absolute;
        left: -23px;
        top: 16px;
        width: 2px;
        height: calc(100% + 9px);
        background-color: rgba(0, 0, 0, 0.1);
    }

    .timeline-content {
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 15px;
        border: 1px solid rgba(0, 0, 0, 0.05);
    }

    .timeline-header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
    }

    .timeline-title {
        font-weight: 500;
    }

    .timeline-time {
        font-size: 0.9rem;
        color: var(--text-secondary);
    }

    .timeline-status {
        margin-bottom: 10px;
    }

    .timeline-notes {
        font-size: 0.9rem;
        color: var(--text-secondary);
        white-space: pre-line;
    }

    .empty-state {
        text-align: center;
        padding: 20px;
        color: var(--text-secondary);
    }

    .get-location-btn {
        margin-top: 10px;
        width: 100%;
    }

    /* Dark theme adjustments */
    .dark-theme .glass-card {
        background-color: rgba(30, 30, 40, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-theme .form-group input,
    .dark-theme .form-group select,
    .dark-theme .form-group textarea {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .dark-theme .timeline-content {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.05);
    }

    .dark-theme .timeline-item:not(:last-child)::before {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .col-md-4, .col-md-8 {
            flex: 0 0 100%;
            max-width: 100%;
        }

        .timeline-header {
            flex-direction: column;
        }

        .timeline-time {
            margin-top: 5px;
        }
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
