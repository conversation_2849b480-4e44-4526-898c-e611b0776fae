<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Update - TransLogix</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="page-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .update-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: var(--glass-bg);
            border-radius: 16px;
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        .update-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .update-header h1 {
            color: var(--primary-color);
        }

        .update-content {
            margin-bottom: 30px;
        }

        .update-footer {
            text-align: center;
        }

        .success {
            color: #28a745;
            padding: 10px;
            background-color: rgba(40, 167, 69, 0.1);
            border-radius: 8px;
            margin: 10px 0;
        }

        .error {
            color: #dc3545;
            padding: 10px;
            background-color: rgba(220, 53, 69, 0.1);
            border-radius: 8px;
            margin: 10px 0;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="update-container">
        <div class="update-header">
            <h1><i class="fas fa-database"></i> Database Update</h1>
            <p>Updating the database structure for TransLogix Tracking System</p>
        </div>

        <div class="update-content">
<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Run the database update
try {
    // Check if delivered_at column exists
    $stmt = $conn->prepare("SELECT COUNT(*) as column_exists
                          FROM INFORMATION_SCHEMA.COLUMNS
                          WHERE TABLE_SCHEMA = ?
                            AND TABLE_NAME = 'shipments'
                            AND COLUMN_NAME = 'delivered_at'");
    $stmt->execute([DB_NAME]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    // Add the column if it doesn't exist
    if ($result['column_exists'] == 0) {
        $conn->exec("ALTER TABLE shipments ADD COLUMN delivered_at DATETIME DEFAULT NULL");
        echo "<p>Added delivered_at column to shipments table.</p>";
    } else {
        echo "<p>delivered_at column already exists.</p>";
    }

    // Update delivered_at for delivered shipments
    $stmt = $conn->prepare("UPDATE shipments SET delivered_at = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 10) DAY) WHERE status = 'delivered' AND delivered_at IS NULL");
    $stmt->execute();
    $updatedRows = $stmt->rowCount();
    echo "<p>Updated delivered_at for $updatedRows shipments.</p>";

    echo "<div class='success'><h2>Database updated successfully!</h2></div>";
} catch(PDOException $e) {
    echo "<div class='error'><h2>Database update failed:</h2><p>" . $e->getMessage() . "</p></div>";
}
?>
        </div>

        <div class="update-footer">
            <a href="admin/index.php" class="btn">Go to Admin Dashboard</a>
        </div>
    </div>
</body>
</html>
