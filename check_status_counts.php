<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

echo "<h1>Shipment Status Counts</h1>";

try {
    // Get counts of each status
    $stmt = $conn->query("SELECT status, COUNT(*) as count FROM shipments GROUP BY status");
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Status Counts:</h2>";
    echo "<ul>";
    foreach ($results as $row) {
        echo "<li><strong>" . htmlspecialchars($row['status']) . ":</strong> " . $row['count'] . "</li>";
    }
    echo "</ul>";
    
    // Check for any 'in-transit' values (with hyphen)
    $inTransitCheck = $conn->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'in-transit'");
    $inTransitResult = $inTransitCheck->fetch(PDO::FETCH_ASSOC);
    
    if ($inTransitResult['count'] > 0) {
        echo "<p>Found " . $inTransitResult['count'] . " shipments with 'in-transit' status (with hyphen).</p>";
    } else {
        echo "<p>No shipments found with 'in-transit' status (with hyphen).</p>";
    }
    
    // Check for any 'in_transit' values (with underscore)
    $inTransitCheck = $conn->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'in_transit'");
    $inTransitResult = $inTransitCheck->fetch(PDO::FETCH_ASSOC);
    
    if ($inTransitResult['count'] > 0) {
        echo "<p>Found " . $inTransitResult['count'] . " shipments with 'in_transit' status (with underscore).</p>";
    } else {
        echo "<p>No shipments found with 'in_transit' status (with underscore).</p>";
    }
    
    // Check the ENUM definition
    $stmt = $conn->query("SHOW COLUMNS FROM shipments WHERE Field = 'status'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($column) {
        echo "<h2>Current ENUM Definition:</h2>";
        echo "<p>" . htmlspecialchars($column['Type']) . "</p>";
    }
    
    // Check the query used in the dashboard
    echo "<h2>Testing Dashboard Query:</h2>";
    $statsQuery = $conn->query("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as `delayed`,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM shipments");
    $stats = $statsQuery->fetch(PDO::FETCH_ASSOC);
    
    echo "<ul>";
    foreach ($stats as $key => $value) {
        echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . $value . "</li>";
    }
    echo "</ul>";
    
    // Check if there are any shipments with invalid status values
    $validStatuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
    $validStatusList = "'" . implode("', '", $validStatuses) . "'";
    
    $invalidStmt = $conn->query("SELECT id, tracking_number, status FROM shipments WHERE status NOT IN ($validStatusList)");
    $invalidShipments = $invalidStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invalidShipments) > 0) {
        echo "<h2>Shipments with Invalid Status Values:</h2>";
        echo "<ul>";
        foreach ($invalidShipments as $shipment) {
            echo "<li>Shipment #{$shipment['id']} ({$shipment['tracking_number']}): '{$shipment['status']}'</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No shipments found with invalid status values.</p>";
    }
    
    echo "<p><a href='admin/index.php'>Return to Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
