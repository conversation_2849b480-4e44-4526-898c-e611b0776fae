<?php
/**
 * Test Nominatim API
 * 
 * This script tests if we can make HTTP requests to the Nominatim API
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Test Nominatim API';

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-map-marker-alt"></i> Test Nominatim API</h1>
            <div class="admin-actions">
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Test Geocoding</h2>
                </div>
                <div class="card-body">
                    <h3>Using cURL</h3>
                    <?php
                    try {
                        // Check if cURL is available
                        if (!function_exists('curl_init')) {
                            echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> cURL is not available</div>";
                        } else {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> cURL is available</div>";
                            
                            // Test URL
                            $url = 'https://nominatim.openstreetmap.org/search?q=London&format=json&limit=1';
                            echo "<p><strong>Test URL:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
                            
                            // Initialize cURL
                            $ch = curl_init($url);
                            
                            // Set cURL options
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_USERAGENT, 'TrackingSite/1.0');
                            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                            
                            // Execute the request
                            echo "<p><strong>Making request...</strong></p>";
                            $response = curl_exec($ch);
                            
                            // Check for errors
                            if ($response === false) {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> cURL error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")</div>";
                            } else {
                                // Get HTTP status code
                                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                echo "<p><strong>HTTP Status Code:</strong> {$httpCode}</p>";
                                
                                if ($httpCode >= 400) {
                                    echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> HTTP error: {$httpCode}</div>";
                                } else {
                                    echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Request successful!</div>";
                                    echo "<p><strong>Response:</strong></p>";
                                    echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . (strlen($response) > 1000 ? '...' : '') . "</pre>";
                                    
                                    // Try to decode the JSON
                                    $data = json_decode($response, true);
                                    if (json_last_error() !== JSON_ERROR_NONE) {
                                        echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> JSON decode error: " . json_last_error_msg() . "</div>";
                                    } else {
                                        echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> JSON decode successful!</div>";
                                        echo "<p><strong>Decoded data:</strong></p>";
                                        echo "<pre>" . print_r($data, true) . "</pre>";
                                    }
                                }
                            }
                            
                            // Close cURL
                            curl_close($ch);
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Exception: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Test Reverse Geocoding</h2>
                </div>
                <div class="card-body">
                    <h3>Using cURL</h3>
                    <?php
                    try {
                        // Check if cURL is available
                        if (!function_exists('curl_init')) {
                            echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> cURL is not available</div>";
                        } else {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> cURL is available</div>";
                            
                            // Test URL
                            $url = 'https://nominatim.openstreetmap.org/reverse?lat=51.5074&lon=-0.1278&format=json';
                            echo "<p><strong>Test URL:</strong> <a href='{$url}' target='_blank'>{$url}</a></p>";
                            
                            // Initialize cURL
                            $ch = curl_init($url);
                            
                            // Set cURL options
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_USERAGENT, 'TrackingSite/1.0');
                            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
                            
                            // Execute the request
                            echo "<p><strong>Making request...</strong></p>";
                            $response = curl_exec($ch);
                            
                            // Check for errors
                            if ($response === false) {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> cURL error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")</div>";
                            } else {
                                // Get HTTP status code
                                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                                echo "<p><strong>HTTP Status Code:</strong> {$httpCode}</p>";
                                
                                if ($httpCode >= 400) {
                                    echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> HTTP error: {$httpCode}</div>";
                                } else {
                                    echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Request successful!</div>";
                                    echo "<p><strong>Response:</strong></p>";
                                    echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . (strlen($response) > 1000 ? '...' : '') . "</pre>";
                                    
                                    // Try to decode the JSON
                                    $data = json_decode($response, true);
                                    if (json_last_error() !== JSON_ERROR_NONE) {
                                        echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> JSON decode error: " . json_last_error_msg() . "</div>";
                                    } else {
                                        echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> JSON decode successful!</div>";
                                        echo "<p><strong>Decoded data:</strong></p>";
                                        echo "<pre>" . print_r($data, true) . "</pre>";
                                    }
                                }
                            }
                            
                            // Close cURL
                            curl_close($ch);
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Exception: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="admin-actions mt-4">
            <a href="test_geocoding.php" class="btn primary-btn"><i class="fas fa-map-marker-alt"></i> Go to Geocoding Test</a>
            <a href="admin/index.php" class="btn secondary-btn"><i class="fas fa-arrow-left"></i> Return to Dashboard</a>
        </div>
    </div>
</section>

<style>
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left: 4px solid #17a2b8;
    color: #17a2b8;
}

code, pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    padding: 10px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
}

.mt-4 {
    margin-top: 20px;
}
</style>

<?php include_once 'includes/footer.php'; ?>
