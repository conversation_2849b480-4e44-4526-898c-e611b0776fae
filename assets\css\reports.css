/* Reports Page Styles */

/* Report Controls Section */
.report-controls {
    padding: 40px 0;
    background-color: var(--bg-color);
}

.report-filters {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.filter-form .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    align-items: flex-end;
}

.filter-form .form-group {
    flex: 1;
    min-width: 200px;
}

.filter-form .submit-group {
    flex: 0 0 auto;
}

.filter-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.filter-form .form-control {
    width: 100%;
    padding: 12px 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.filter-form .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(92, 43, 226, 0.2);
    outline: none;
}

.filter-form select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%235c2be2' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 15px) center;
    padding-right: 40px;
}

/* Report Visualization Section */
.report-visualization {
    padding: 40px 0;
    background-color: var(--bg-secondary);
}

.report-container {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.report-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.report-date-range {
    color: var(--text-secondary);
    font-style: italic;
}

.chart-container {
    margin-bottom: 30px;
}

.chart-wrapper {
    position: relative;
    height: 400px;
    margin-bottom: 20px;
}

.chart-type-selector {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.chart-type-btn {
    background-color: var(--glass-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.chart-type-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.chart-type-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Data Summary */
.data-summary {
    margin-top: 40px;
}

.data-summary h3 {
    margin-bottom: 20px;
    color: var(--text-color);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.summary-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(92, 43, 226, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 15px;
}

.summary-icon.delivered {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.summary-icon.delayed {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.summary-info h4 {
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: var(--text-secondary);
}

.summary-number {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Report Data Table Section */
.report-data-table {
    padding: 40px 0 60px;
    background-color: var(--bg-color);
}

/* Dark Theme Overrides */
.dark-theme .filter-form .form-control {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .filter-form .form-control:focus {
    border-color: var(--primary-color);
}

.dark-theme .summary-card {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .chart-type-btn {
    background-color: rgba(30, 30, 30, 0.7);
    border-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .chart-type-btn:hover {
    border-color: var(--primary-color);
}

.dark-theme .chart-type-btn.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .filter-form .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .filter-form .form-group {
        width: 100%;
    }
    
    .report-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .chart-wrapper {
        height: 300px;
    }
    
    .summary-grid {
        grid-template-columns: 1fr;
    }
}
