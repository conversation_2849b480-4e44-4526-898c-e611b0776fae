<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Get the database connection
global $conn;

// Get the table structure
$stmt = $conn->query("DESCRIBE shipments");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Output the table structure
echo "<h1>Shipments Table Structure</h1>";
echo "<table border='1'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";

foreach ($columns as $column) {
    echo "<tr>";
    echo "<td>" . $column['Field'] . "</td>";
    echo "<td>" . $column['Type'] . "</td>";
    echo "<td>" . $column['Null'] . "</td>";
    echo "<td>" . $column['Key'] . "</td>";
    echo "<td>" . (isset($column['Default']) ? $column['Default'] : 'NULL') . "</td>";
    echo "<td>" . $column['Extra'] . "</td>";
    echo "</tr>";
}

echo "</table>";
?>
