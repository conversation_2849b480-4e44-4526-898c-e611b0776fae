<?php
/**
 * Module Loader
 * 
 * This file contains functions for loading dashboard modules
 */

/**
 * Load a module file
 * 
 * @param string $module_name The name of the module to load (without .php extension)
 * @param array $data Optional data to pass to the module
 * @return void
 */
function load_module($module_name, $data = []) {
    $module_path = __DIR__ . '/' . $module_name . '.php';
    
    if (file_exists($module_path)) {
        // Extract data to make variables available to the module
        if (!empty($data)) {
            extract($data);
        }
        
        // Include the module file
        include $module_path;
    } else {
        echo "<!-- Module not found: $module_name -->";
    }
}

/**
 * Load all sidebar modules
 * 
 * @param array $data Data to pass to all sidebar modules
 * @return void
 */
function load_sidebar_modules($data = []) {
    $sidebar_modules = [
        'quick_actions',
        'performance_metrics',
        'activity_feed'
    ];
    
    foreach ($sidebar_modules as $module) {
        load_module($module, $data);
    }
}

/**
 * Load all main content modules
 * 
 * @param array $data Data to pass to all main content modules
 * @return void
 */
function load_main_content_modules($data = []) {
    $main_modules = [
        'shipment_overview',
        'charts',
        'recent_shipments'
    ];
    
    foreach ($main_modules as $module) {
        load_module($module, $data);
    }
}
