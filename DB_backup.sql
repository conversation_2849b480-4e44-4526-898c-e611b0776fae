-- Create database
CREATE DATABASE IF NOT EXISTS tracking_cms;
USE tracking_cms;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100),
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'staff', 'customer') NOT NULL DEFAULT 'customer',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shipments table
CREATE TABLE IF NOT EXISTS shipments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_number VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(100) NOT NULL,
    origin VARCHAR(100) NOT NULL,
    destination VARCHAR(100) NOT NULL,
    status ENUM('pending', 'in_transit', 'delivered', 'delayed', 'cancelled') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estimated_delivery DATE,
    delivered_at DATETIME DEFAULT NULL,
    package_picture VARCHAR(255) DEFAULT NULL,
    shopper_name VARCHAR(100) DEFAULT NULL,
    shopper_email VARCHAR(100) DEFAULT NULL,
    shopper_phone VARCHAR(50) DEFAULT NULL,
    shopper_address TEXT DEFAULT NULL,
    receiver_name VARCHAR(100) DEFAULT NULL,
    receiver_email VARCHAR(100) DEFAULT NULL,
    receiver_phone VARCHAR(50) DEFAULT NULL,
    receiver_address TEXT DEFAULT NULL,
    package_weight DECIMAL(10,2) DEFAULT NULL,
    package_dimensions VARCHAR(50) DEFAULT NULL,
    shipping_service VARCHAR(100) DEFAULT NULL,
    shipping_cost DECIMAL(10,2) DEFAULT NULL
);

-- Tracking updates table
CREATE TABLE IF NOT EXISTS tracking_updates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    shipment_id INT NOT NULL,
    location VARCHAR(100) NOT NULL,
    status VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE
);

-- Remember Me Tokens table
CREATE TABLE IF NOT EXISTS remember_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    selector VARCHAR(32) NOT NULL,
    hashed_validator VARCHAR(255) NOT NULL,
    expires DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$8zf0SXIIhUEMsNXhQ3.wqOkwF3IOl7qRyPRnxRmqUGhKD1eiIgYYm', 'admin');

-- Insert sample shipments
INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, estimated_delivery) VALUES
('TL12345678', 'John Smith', 'New York, USA', 'London, UK', 'in_transit', '2023-12-15'),
('TL87654321', 'Jane Doe', 'Shanghai, China', 'Sydney, Australia', 'pending', '2023-12-20'),
('TL11223344', 'Robert Johnson', 'Tokyo, Japan', 'Los Angeles, USA', 'delivered', '2023-11-30'),
('TL55667788', 'Maria Garcia', 'Berlin, Germany', 'Paris, France', 'delayed', '2023-12-18'),
('TL99001122', 'David Lee', 'Dubai, UAE', 'Mumbai, India', 'in_transit', '2023-12-10');

-- Insert sample tracking updates
INSERT INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes) VALUES
(1, 'New York Shipping Port', 'Shipment received', 40.7128, -74.0060, 'Package received at origin port'),
(1, 'Atlantic Ocean', 'In transit', 41.8781, -67.3667, 'Vessel in transit'),
(1, 'Atlantic Ocean', 'In transit', 45.9456, -55.8882, 'Vessel approaching European waters'),
(2, 'Shanghai Distribution Center', 'Processing', 31.2304, 121.4737, 'Package being processed for international shipping'),
(3, 'Tokyo Shipping Port', 'Shipment received', 35.6762, 139.6503, 'Package received at origin port'),
(3, 'Pacific Ocean', 'In transit', 38.9072, 165.3453, 'Vessel in transit'),
(3, 'Los Angeles Port', 'Arrived at destination', 34.0522, -118.2437, 'Package arrived at destination port'),
(3, 'Los Angeles Distribution Center', 'Out for delivery', 34.0522, -118.2437, 'Package out for final delivery'),
(3, 'Los Angeles', 'Delivered', 34.0522, -118.2437, 'Package delivered successfully'),
(4, 'Berlin Logistics Hub', 'Shipment received', 52.5200, 13.4050, 'Package received at origin hub'),
(4, 'Frankfurt', 'In transit', 50.1109, 8.6821, 'Package in transit'),
(4, 'Frankfurt', 'Delayed', 50.1109, 8.6821, 'Package delayed due to weather conditions'),
(5, 'Dubai Logistics Center', 'Shipment received', 25.2048, 55.2708, 'Package received at origin center'),
(5, 'Dubai International Airport', 'In transit', 25.2532, 55.3657, 'Package in transit via air freight');
