<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Check current status values
try {
    $statusQuery = $conn->query("SELECT DISTINCT status FROM shipments");
    $statuses = $statusQuery->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Current status values in the database:<br>";
    echo "<pre>";
    print_r($statuses);
    echo "</pre>";
    
    // Fix in-transit status (replace hyphen with underscore)
    $updateInTransit = $conn->prepare("UPDATE shipments SET status = 'in_transit' WHERE status = 'in-transit'");
    $updateInTransit->execute();
    $inTransitCount = $updateInTransit->rowCount();
    
    if ($inTransitCount > 0) {
        echo "Updated $inTransitCount shipments from 'in-transit' to 'in_transit'<br>";
    } else {
        echo "No 'in-transit' statuses found to update<br>";
    }
    
    // Fix empty status values
    $updateEmptyStatus = $conn->prepare("UPDATE shipments SET status = 'pending' WHERE status = '' OR status IS NULL");
    $updateEmptyStatus->execute();
    $emptyStatusCount = $updateEmptyStatus->rowCount();
    
    if ($emptyStatusCount > 0) {
        echo "Updated $emptyStatusCount shipments with empty status to 'pending'<br>";
    } else {
        echo "No empty status values found to update<br>";
    }
    
    // Check status values after updates
    $statusQuery = $conn->query("SELECT DISTINCT status FROM shipments");
    $updatedStatuses = $statusQuery->fetchAll(PDO::FETCH_COLUMN);
    
    echo "Status values after updates:<br>";
    echo "<pre>";
    print_r($updatedStatuses);
    echo "</pre>";
    
    // Check if we have data for each status
    $statusCountQuery = $conn->query("SELECT status, COUNT(*) as count FROM shipments GROUP BY status");
    $statusCounts = $statusCountQuery->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Shipment counts by status:<br>";
    echo "<pre>";
    print_r($statusCounts);
    echo "</pre>";
    
    // Make sure we have at least one shipment for each status
    $requiredStatuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
    $existingStatuses = array_column($statusCounts, 'status');
    
    foreach ($requiredStatuses as $status) {
        if (!in_array($status, $existingStatuses)) {
            echo "No shipments found with status '$status'. Adding one now...<br>";
            
            // Add a shipment with this status
            $insertShipment = $conn->prepare("INSERT INTO shipments 
                (tracking_number, customer_name, origin, destination, status, created_at) 
                VALUES (?, ?, ?, ?, ?, ?)");
                
            $trackingNumber = 'TL' . mt_rand(1000000, 9999999);
            $customerName = 'Test Customer';
            $origin = 'New York, USA';
            $destination = 'Miami, USA';
            $createdAt = date('Y-m-d H:i:s');
            
            $insertShipment->execute([
                $trackingNumber,
                $customerName,
                $origin,
                $destination,
                $status,
                $createdAt
            ]);
            
            echo "Added shipment with status '$status'<br>";
        }
    }
    
    echo "<br>Status fixes complete. <a href='admin/index.php'>Go to Admin Dashboard</a>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
