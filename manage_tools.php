<?php
/**
 * Manage Maintenance Tools
 *
 * This page allows administrators to manage maintenance tools.
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/maintenance_tools.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Handle tool management actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        if ($action === 'toggle_tool' && isset($_POST['tool_id'])) {
            $toolId = $_POST['tool_id'];
            $enabled = isset($_POST['enabled']) ? (bool)$_POST['enabled'] : false;

            // In a real implementation, this would update a database setting
            // For now, we'll just show a message
            $tool = getMaintenanceToolById($toolId);
            if ($tool) {
                $status = $enabled ? 'enabled' : 'disabled';
                setSuccessNotification("Tool '{$tool['name']}' has been {$status}. Note: This is just a simulation, tool status will reset when the page is reloaded.");
            }
        } elseif ($action === 'delete_tool' && isset($_POST['tool_id'])) {
            $toolId = $_POST['tool_id'];
            $tool = getMaintenanceToolById($toolId);

            if ($tool && file_exists($tool['file'])) {
                // In a real implementation, this would actually delete the file
                // For now, we'll just show a message
                setSuccessNotification("Tool file '{$tool['file']}' would be deleted. This is just a simulation.");
            } else {
                setErrorNotification("Tool file not found.");
            }
        } elseif ($action === 'delete_selected' && isset($_POST['selected_tools'])) {
            $selectedTools = $_POST['selected_tools'];
            $count = count($selectedTools);

            // In a real implementation, this would actually delete the files
            // For now, we'll just show a message
            setSuccessNotification("{$count} tool files would be deleted. This is just a simulation.");
        }
    }

    // Redirect to avoid form resubmission
    redirect('manage_tools.php');
    exit;
}

// Set page title
$pageTitle = 'Manage Maintenance Tools';

// Add maintenance-specific CSS
$additionalCss = [
    SITE_URL . '/assets/css/maintenance-fix.css'
];

// Include header
include_once 'includes/header.php';
?>

<!-- Manage Tools Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">
            <h1><i class="fas fa-cogs"></i> Manage Maintenance Tools</h1>
            <p>Enable, disable, or delete maintenance and debugging tools</p>
            <div class="admin-actions">
                <a href="maintenance.php" class="btn primary-btn">Back to Maintenance Dashboard</a>
                <a href="admin/index.php" class="btn secondary-btn">Back to Admin Dashboard</a>
            </div>
        </div>
    </div>
</section>

<!-- Manage Tools Section -->
<section class="admin-section">
    <div class="container">
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-tools"></i> Maintenance Tools</h2>
                <div class="header-actions">
                    <button type="button" id="selectAllBtn" class="btn outline-btn">Select All</button>
                    <button type="button" id="deselectAllBtn" class="btn outline-btn">Deselect All</button>
                    <button type="button" id="deleteSelectedBtn" class="btn danger-btn" disabled>Delete Selected</button>
                </div>
            </div>

            <div class="card-body">
                <form id="bulkActionForm" method="post" action="manage_tools.php">
                    <input type="hidden" name="action" value="delete_selected">

                    <div class="table-responsive">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th width="40"><input type="checkbox" id="selectAll"></th>
                                    <th>Name</th>
                                    <th>Description</th>
                                    <th>Category</th>
                                    <th>File</th>
                                    <th>Status</th>
                                    <th width="120">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                // Get all tools including disabled ones
                                $allTools = getMaintenanceTools(false);

                                foreach ($allTools as $tool):
                                ?>
                                <tr>
                                    <td>
                                        <input type="checkbox" name="selected_tools[]" value="<?php echo $tool['id']; ?>" class="tool-checkbox">
                                    </td>
                                    <td>
                                        <div class="tool-name">
                                            <i class="<?php echo $tool['icon']; ?>"></i>
                                            <?php echo $tool['name']; ?>
                                        </div>
                                    </td>
                                    <td><?php echo $tool['description']; ?></td>
                                    <td>
                                        <span class="category-badge <?php echo $tool['category']; ?>">
                                            <?php echo ucfirst($tool['category']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <code><?php echo $tool['file']; ?></code>
                                        <?php if (!file_exists($tool['file'])): ?>
                                        <span class="missing-badge">Missing</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="status-badge <?php echo $tool['enabled'] ? 'enabled' : 'disabled'; ?>">
                                            <?php echo $tool['enabled'] ? 'Enabled' : 'Disabled'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <button type="button" class="action-btn toggle-btn tooltip" data-tool-id="<?php echo $tool['id']; ?>" data-enabled="<?php echo $tool['enabled'] ? '1' : '0'; ?>" title="<?php echo $tool['enabled'] ? 'Disable Tool' : 'Enable Tool'; ?>">
                                                <i class="fas <?php echo $tool['enabled'] ? 'fa-toggle-on' : 'fa-toggle-off'; ?>"></i>
                                                <span class="tooltip-text"><?php echo $tool['enabled'] ? 'Disable Tool' : 'Enable Tool'; ?></span>
                                            </button>
                                            <a href="<?php echo $tool['file']; ?>" class="action-btn view-btn tooltip" title="Run Tool">
                                                <i class="fas fa-play"></i>
                                                <span class="tooltip-text">Run Tool</span>
                                            </a>
                                            <button type="button" class="action-btn delete-btn tooltip" data-tool-id="<?php echo $tool['id']; ?>" data-tool-name="<?php echo $tool['name']; ?>" title="Delete Tool">
                                                <i class="fas fa-trash"></i>
                                                <span class="tooltip-text">Delete Tool</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        </div>

        <!-- Tool Management Forms -->
        <form id="toggleToolForm" method="post" action="manage_tools.php" style="display: none;">
            <input type="hidden" name="action" value="toggle_tool">
            <input type="hidden" name="tool_id" id="toggleToolId">
            <input type="hidden" name="enabled" id="toggleToolEnabled">
        </form>

        <form id="deleteToolForm" method="post" action="manage_tools.php" style="display: none;">
            <input type="hidden" name="action" value="delete_tool">
            <input type="hidden" name="tool_id" id="deleteToolId">
        </form>

        <!-- Delete Confirmation Modal -->
        <div id="deleteConfirmModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Deletion</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="warning-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Are you sure you want to delete the file for <strong id="deleteToolName"></strong>?</p>
                        <p>This action cannot be undone and may break functionality that depends on this file.</p>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="confirmDeleteBtn" class="btn danger-btn">Yes, Delete File</button>
                        <button type="button" class="btn secondary-btn close-modal">Cancel</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Delete Confirmation Modal -->
        <div id="bulkDeleteConfirmModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Bulk Deletion</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="warning-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Are you sure you want to delete <strong id="deleteCount"></strong> tool files?</p>
                        <p>This action cannot be undone and may break functionality that depends on these files.</p>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="confirmBulkDeleteBtn" class="btn danger-btn">Yes, Delete Files</button>
                        <button type="button" class="btn secondary-btn close-modal">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Manage Tools Styles */
.admin-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-body {
    padding: 20px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.data-table th {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

.data-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.tool-name {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.tool-name i {
    color: var(--primary-color);
}

/* Badge Styles */
.category-badge,
.status-badge,
.missing-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.category-badge.database {
    background-color: rgba(0, 123, 255, 0.1);
    color: #007bff;
}

.category-badge.geocoding {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.category-badge.system {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.category-badge.debug {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.status-badge.enabled {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.status-badge.disabled {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.missing-badge {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    margin-left: 5px;
}

/* Action Buttons */
.actions {
    display: flex;
    gap: 5px;
}

.action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toggle-btn {
    background-color: #6c757d;
}

.view-btn {
    background-color: var(--primary-color);
}

.delete-btn {
    background-color: #dc3545;
}

.action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--glass-bg);
    margin: 10% auto;
    padding: 0;
    width: 500px;
    max-width: 90%;
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    animation: modalFadeIn 0.3s;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-body {
    padding: 20px;
}

.close {
    color: var(--text-secondary);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--primary-color);
}

/* Warning Message */
.warning-message {
    text-align: center;
    margin-bottom: 20px;
}

.warning-message i {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 15px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Tooltip Styles */
.tooltip {
    position: relative;
}

.tooltip .tooltip-text {
    visibility: hidden;
    width: 120px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
    pointer-events: none;
}

.tooltip .tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Animation */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Button Styles */
.danger-btn {
    background-color: #dc3545;
    color: white;
}

.danger-btn:hover {
    background-color: #c82333;
}

.danger-btn:disabled {
    background-color: #e9a8ae;
    cursor: not-allowed;
}

/* Checkbox Styles */
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle Tool Status
    const toggleButtons = document.querySelectorAll('.toggle-btn');
    const toggleToolForm = document.getElementById('toggleToolForm');
    const toggleToolId = document.getElementById('toggleToolId');
    const toggleToolEnabled = document.getElementById('toggleToolEnabled');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const toolId = this.getAttribute('data-tool-id');
            const currentlyEnabled = this.getAttribute('data-enabled') === '1';

            toggleToolId.value = toolId;
            toggleToolEnabled.value = currentlyEnabled ? '0' : '1';

            toggleToolForm.submit();
        });
    });

    // Delete Tool
    const deleteButtons = document.querySelectorAll('.delete-btn');
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    const deleteToolName = document.getElementById('deleteToolName');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const deleteToolForm = document.getElementById('deleteToolForm');
    const deleteToolId = document.getElementById('deleteToolId');

    deleteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const toolId = this.getAttribute('data-tool-id');
            const toolName = this.getAttribute('data-tool-name');

            deleteToolId.value = toolId;
            deleteToolName.textContent = toolName;

            deleteConfirmModal.style.display = 'block';
        });
    });

    confirmDeleteBtn.addEventListener('click', function() {
        deleteToolForm.submit();
    });

    // Bulk Actions
    const selectAllCheckbox = document.getElementById('selectAll');
    const toolCheckboxes = document.querySelectorAll('.tool-checkbox');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');
    const selectAllBtn = document.getElementById('selectAllBtn');
    const deselectAllBtn = document.getElementById('deselectAllBtn');
    const bulkDeleteConfirmModal = document.getElementById('bulkDeleteConfirmModal');
    const deleteCount = document.getElementById('deleteCount');
    const confirmBulkDeleteBtn = document.getElementById('confirmBulkDeleteBtn');
    const bulkActionForm = document.getElementById('bulkActionForm');

    // Update delete button state
    function updateDeleteButtonState() {
        const checkedCount = document.querySelectorAll('.tool-checkbox:checked').length;
        deleteSelectedBtn.disabled = checkedCount === 0;
        deleteCount.textContent = checkedCount;
    }

    // Select all checkbox
    selectAllCheckbox.addEventListener('change', function() {
        toolCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });

        updateDeleteButtonState();
    });

    // Individual checkboxes
    toolCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = document.querySelectorAll('.tool-checkbox:checked').length === toolCheckboxes.length;
            selectAllCheckbox.checked = allChecked;

            updateDeleteButtonState();
        });
    });

    // Select All button
    selectAllBtn.addEventListener('click', function() {
        toolCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        selectAllCheckbox.checked = true;
        updateDeleteButtonState();
    });

    // Deselect All button
    deselectAllBtn.addEventListener('click', function() {
        toolCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        selectAllCheckbox.checked = false;
        updateDeleteButtonState();
    });

    // Delete Selected button
    deleteSelectedBtn.addEventListener('click', function() {
        const checkedCount = document.querySelectorAll('.tool-checkbox:checked').length;

        if (checkedCount > 0) {
            deleteCount.textContent = checkedCount;
            bulkDeleteConfirmModal.style.display = 'block';
        }
    });

    // Confirm Bulk Delete
    confirmBulkDeleteBtn.addEventListener('click', function() {
        bulkActionForm.submit();
    });

    // Close modals
    const closeButtons = document.querySelectorAll('.close, .close-modal');

    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            deleteConfirmModal.style.display = 'none';
            bulkDeleteConfirmModal.style.display = 'none';
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === deleteConfirmModal) {
            deleteConfirmModal.style.display = 'none';
        }
        if (event.target === bulkDeleteConfirmModal) {
            bulkDeleteConfirmModal.style.display = 'none';
        }
    });

    // Initialize delete button state
    updateDeleteButtonState();
});
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
