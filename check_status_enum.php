<?php
/**
 * Check Status ENUM Values
 *
 * This script checks the status ENUM values in the shipments table.
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Check Database Status';

// Include header
include_once 'includes/header.php';
?>

// Check status ENUM values
echo "<h2>Status ENUM Values</h2>";
try {
    $db->query("SHOW COLUMNS FROM shipments LIKE 'status'");
    $statusColumn = $db->single();

    if ($statusColumn) {
        $currentEnum = $statusColumn['Type'];
        echo "<p>Current status ENUM: <code>{$currentEnum}</code></p>";

        // Extract values from ENUM
        preg_match("/^enum\(\'(.*)\'\)$/", $currentEnum, $matches);
        if (isset($matches[1])) {
            $values = explode("','", $matches[1]);

            echo "<h3>Available Status Values:</h3>";
            echo "<ul>";
            foreach ($values as $value) {
                echo "<li>{$value}</li>";
            }
            echo "</ul>";

            // Check if new values exist
            $newValues = ['picked_up', 'arrived_at_facility', 'out_for_delivery'];
            $missingValues = array_diff($newValues, $values);

            if (empty($missingValues)) {
                echo "<p class='success'>All required status values exist in the database.</p>";
            } else {
                echo "<p class='error'>Missing status values: " . implode(", ", $missingValues) . "</p>";
                echo "<p>Please run the <a href='update_status_enum.php'>Update Database Schema</a> script to add these values.</p>";
            }
        } else {
            echo "<p class='error'>Could not parse ENUM values.</p>";
        }
    } else {
        echo "<p class='error'>Could not find status column in shipments table.</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}

// Test adding a shipment with each status
echo "<h2>Test Adding Shipments with Different Statuses</h2>";
try {
    $db->query("SHOW COLUMNS FROM shipments LIKE 'status'");
    $statusColumn = $db->single();

    if ($statusColumn) {
        $currentEnum = $statusColumn['Type'];

        // Extract values from ENUM
        preg_match("/^enum\(\'(.*)\'\)$/", $currentEnum, $matches);
        if (isset($matches[1])) {
            $values = explode("','", $matches[1]);

            echo "<table>
                    <tr>
                        <th>Status</th>
                        <th>Test Result</th>
                    </tr>";

            foreach ($values as $status) {
                echo "<tr>";
                echo "<td>{$status}</td>";

                try {
                    // Generate a unique tracking number
                    $trackingNumber = 'TEST' . time() . rand(1000, 9999);

                    // Try to insert a test shipment with this status
                    $db->query("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, estimated_delivery)
                               VALUES (:tracking, 'Test Customer', 'Test Origin', 'Test Destination', :status, DATE_ADD(NOW(), INTERVAL 7 DAY))");
                    $db->bind(':tracking', $trackingNumber);
                    $db->bind(':status', $status);
                    $result = $db->execute();

                    if ($result) {
                        echo "<td class='success'>Success</td>";

                        // Delete the test shipment
                        $db->query("DELETE FROM shipments WHERE tracking_number = :tracking");
                        $db->bind(':tracking', $trackingNumber);
                        $db->execute();
                    } else {
                        echo "<td class='error'>Failed</td>";
                    }
                } catch (Exception $e) {
                    echo "<td class='error'>Error: " . $e->getMessage() . "</td>";
                }

                echo "</tr>";
            }

            echo "</table>";
        }
    }
} catch (Exception $e) {
    echo "<p class='error'>Error testing shipment addition: " . $e->getMessage() . "</p>";
}

// Check if geocoding_cache table exists
echo "<h2>Geocoding Cache Table</h2>";
try {
    $db->query("SHOW TABLES LIKE 'geocoding_cache'");
    $tableExists = $db->rowCount() > 0;

    if ($tableExists) {
        echo "<p class='success'>Geocoding cache table exists.</p>";
    } else {
        echo "<p class='error'>Geocoding cache table does not exist. Please run the <a href='update_status_enum.php'>Update Database Schema</a> script to create this table.</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>Error checking geocoding_cache table: " . $e->getMessage() . "</p>";
}

echo "</div></body></html>";
?>
