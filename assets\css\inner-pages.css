/* Styles for Service, Industry, and Contact Pages */

/* Service Page Styles */
.service-detail {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.service-detail:nth-child(even) {
    background-color: var(--bg-secondary);
}

.service-content {
    display: flex;
    gap: 40px;
    align-items: center;
}

.service-content.reverse {
    flex-direction: row-reverse;
}

.service-text {
    flex: 1;
}

.service-image {
    flex: 1;
}

.service-image img {
    width: 100%;
    border-radius: 16px;
    box-shadow: var(--box-shadow);
}

.service-text h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.service-text h3 {
    margin: 30px 0 15px;
    color: var(--secondary-color);
}

.service-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.service-list li {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
}

.service-list li i {
    color: var(--primary-color);
    margin-right: 10px;
    margin-top: 5px;
}

/* Industry Page Styles */
.industry-detail {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.industry-detail:nth-child(even) {
    background-color: var(--bg-secondary);
}

.industry-content {
    display: flex;
    gap: 40px;
    align-items: center;
}

.industry-content.reverse {
    flex-direction: row-reverse;
}

.industry-text {
    flex: 1;
}

.industry-image {
    flex: 1;
}

.industry-image img {
    width: 100%;
    border-radius: 16px;
    box-shadow: var(--box-shadow);
}

.industry-text h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.industry-text h3 {
    margin: 30px 0 15px;
    color: var(--secondary-color);
}

.industry-list {
    list-style: none;
    padding: 0;
    margin: 20px 0;
}

.industry-list li {
    margin-bottom: 12px;
    display: flex;
    align-items: flex-start;
}

.industry-list li i {
    color: var(--primary-color);
    margin-right: 10px;
    margin-top: 5px;
}

/* Contact Page Styles */
.contact-info {
    padding: 60px 0;
    background-color: var(--bg-color);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.contact-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3);
}

.contact-icon {
    width: 70px;
    height: 70px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 1.8rem;
}

.contact-card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.contact-form-section {
    padding: 60px 0;
    background-color: var(--bg-secondary);
}

.form-container {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 40px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    max-width: 800px;
    margin: 0 auto;
}

.form-container h2 {
    margin-bottom: 30px;
    text-align: center;
    color: var(--primary-color);
}

.form-success, .form-error {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.form-success {
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.form-error {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

.form-success i, .form-error i {
    font-size: 1.5rem;
    margin-right: 10px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(92, 43, 226, 0.1);
}

.form-note {
    margin-top: 15px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.global-offices {
    padding: 60px 0;
    background-color: var(--bg-color);
}

.offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.office-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    height: 100%;
}

.office-card h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    border-bottom: 2px solid var(--secondary-color);
    padding-bottom: 10px;
}

.office-list {
    list-style: none;
    padding: 0;
}

.office-list li {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.office-list li:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.office-list li strong {
    display: block;
    margin-bottom: 5px;
    color: var(--secondary-color);
}

.map-section {
    padding: 60px 0;
    background-color: var(--bg-secondary);
}

#contact-map {
    height: 400px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .service-content, .industry-content {
        flex-direction: column;
    }
    
    .service-content.reverse, .industry-content.reverse {
        flex-direction: column;
    }
    
    .form-row {
        flex-direction: column;
        gap: 0;
    }
}

@media (max-width: 768px) {
    .service-detail, .industry-detail, .contact-info, .contact-form-section, .global-offices, .map-section {
        padding: 40px 0;
    }
    
    .form-container {
        padding: 20px;
    }
}

/* Dark Theme Adjustments */
.dark-theme .form-group input, .dark-theme .form-group textarea {
    background-color: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.dark-theme .office-list li {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .form-success {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.dark-theme .form-error {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid rgba(220, 53, 69, 0.3);
}
