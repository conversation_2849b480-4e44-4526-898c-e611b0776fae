-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    email VARCHAR(100),
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'staff', 'customer') NOT NULL DEFAULT 'customer',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shipments table
CREATE TABLE IF NOT EXISTS shipments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tracking_number VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(100) NOT NULL,
    origin VARCHAR(100) NOT NULL,
    destination VARCHAR(100) NOT NULL,
    status ENUM('pending', 'in_transit', 'delivered', 'delayed', 'cancelled') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    estimated_delivery DATE,
    delivered_at DATETIME DEFAULT NULL,
    package_picture VARCHAR(255) DEFAULT NULL,
    shipment_name VARCHAR(100) DEFAULT NULL,
    shipment_description TEXT DEFAULT NULL,
    shopper_name VARCHAR(100) DEFAULT NULL,
    shopper_email VARCHAR(100) DEFAULT NULL,
    shopper_phone VARCHAR(50) DEFAULT NULL,
    shopper_address TEXT DEFAULT NULL,
    receiver_name VARCHAR(100) DEFAULT NULL,
    receiver_email VARCHAR(100) DEFAULT NULL,
    receiver_phone VARCHAR(50) DEFAULT NULL,
    receiver_address TEXT DEFAULT NULL,
    package_name VARCHAR(100) DEFAULT NULL,
    package_description TEXT DEFAULT NULL,
    package_weight DECIMAL(10,2) DEFAULT NULL,
    package_dimensions VARCHAR(50) DEFAULT NULL,
    shipping_service VARCHAR(100) DEFAULT NULL,
    shipping_cost DECIMAL(10,2) DEFAULT NULL
);

-- Tracking updates table
CREATE TABLE IF NOT EXISTS tracking_updates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    shipment_id INT NOT NULL,
    location VARCHAR(100) NOT NULL,
    status VARCHAR(100) NOT NULL,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE
);

-- Remember Me Tokens table
CREATE TABLE IF NOT EXISTS remember_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    selector VARCHAR(32) NOT NULL,
    hashed_validator VARCHAR(255) NOT NULL,
    expires DATETIME NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (username, email, password, role) VALUES
('admin', '<EMAIL>', '$2y$10$8zf0SXIIhUEMsNXhQ3.wqOkwF3IOl7qRyPRnxRmqUGhKD1eiIgYYm', 'admin');

-- Insert sample shipments (only if table is empty)
INSERT IGNORE INTO shipments (tracking_number, customer_name, origin, destination, status, estimated_delivery) VALUES
('TL12345678', 'John Smith', 'New York, USA', 'London, UK', 'in_transit', '2023-12-15'),
('TL87654321', 'Jane Doe', 'Shanghai, China', 'Sydney, Australia', 'pending', '2023-12-20'),
('TL11223344', 'Robert Johnson', 'Tokyo, Japan', 'Los Angeles, USA', 'delivered', '2023-11-30'),
('TL55667788', 'Maria Garcia', 'Berlin, Germany', 'Paris, France', 'delayed', '2023-12-18'),
('TL99001122', 'David Lee', 'Dubai, UAE', 'Mumbai, India', 'in_transit', '2023-12-10');

-- Insert sample tracking updates (only if table is empty)
-- Shipment 1: New York to London
INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 1, 'New York Shipping Port', 'Shipment received', 40.7128, -74.0060, 'Package received at origin port', DATE_SUB(NOW(), INTERVAL 10 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 1 AND location = 'New York Shipping Port');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 1, 'Atlantic Ocean', 'In transit', 41.8781, -67.3667, 'Vessel in transit', DATE_SUB(NOW(), INTERVAL 7 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 1 AND location = 'Atlantic Ocean');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 1, 'English Channel', 'In transit', 50.1280, -1.8683, 'Approaching destination port', DATE_SUB(NOW(), INTERVAL 3 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 1 AND location = 'English Channel');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 1, 'London Heathrow Cargo Terminal', 'Arrived at facility', 51.4700, -0.4543, 'Package arrived at destination facility', DATE_SUB(NOW(), INTERVAL 1 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 1 AND location = 'London Heathrow Cargo Terminal');

-- Shipment 2: Shanghai to Sydney
INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 2, 'Shanghai Distribution Center', 'Processing', 31.2304, 121.4737, 'Package being processed for international shipping', DATE_SUB(NOW(), INTERVAL 5 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 2 AND location = 'Shanghai Distribution Center');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 2, 'South China Sea', 'In transit', 16.0544, 119.6963, 'Vessel in transit', DATE_SUB(NOW(), INTERVAL 3 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 2 AND location = 'South China Sea');

-- Shipment 3: Tokyo to Los Angeles (delivered)
INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 3, 'Tokyo International Airport', 'Shipment received', 35.5494, 139.7798, 'Package received for shipping', DATE_SUB(NOW(), INTERVAL 15 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 3 AND location = 'Tokyo International Airport');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 3, 'Pacific Ocean', 'In transit', 35.0000, -160.0000, 'In transit to destination', DATE_SUB(NOW(), INTERVAL 12 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 3 AND location = 'Pacific Ocean');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 3, 'Los Angeles Port', 'Arrived at facility', 33.7395, -118.2610, 'Package arrived at destination port', DATE_SUB(NOW(), INTERVAL 8 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 3 AND location = 'Los Angeles Port');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 3, 'Los Angeles Distribution Center', 'Out for delivery', 34.0522, -118.2437, 'Package out for delivery', DATE_SUB(NOW(), INTERVAL 5 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 3 AND location = 'Los Angeles Distribution Center');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 3, 'Los Angeles, USA', 'Delivered', 34.0522, -118.2437, 'Package delivered successfully', DATE_SUB(NOW(), INTERVAL 4 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 3 AND location = 'Los Angeles, USA');

-- Shipment 4: Berlin to Paris (delayed)
INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 4, 'Berlin Central Station', 'Shipment received', 52.5250, 13.3690, 'Package received for shipping', DATE_SUB(NOW(), INTERVAL 7 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 4 AND location = 'Berlin Central Station');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 4, 'Frankfurt, Germany', 'In transit', 50.1109, 8.6821, 'Package in transit', DATE_SUB(NOW(), INTERVAL 5 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 4 AND location = 'Frankfurt, Germany');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 4, 'Strasbourg, France', 'Delayed', 48.5734, 7.7521, 'Package delayed due to customs inspection', DATE_SUB(NOW(), INTERVAL 2 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 4 AND location = 'Strasbourg, France');

-- Shipment 5: Dubai to Mumbai
INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 5, 'Dubai International Airport', 'Shipment received', 25.2532, 55.3657, 'Package received for shipping', DATE_SUB(NOW(), INTERVAL 6 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 5 AND location = 'Dubai International Airport');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 5, 'Arabian Sea', 'In transit', 20.0000, 65.0000, 'Package in transit', DATE_SUB(NOW(), INTERVAL 3 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 5 AND location = 'Arabian Sea');

INSERT IGNORE INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes, timestamp)
SELECT 5, 'Mumbai Airport', 'Arrived at facility', 19.0896, 72.8656, 'Package arrived at destination facility', DATE_SUB(NOW(), INTERVAL 1 DAY)
WHERE NOT EXISTS (SELECT 1 FROM tracking_updates WHERE shipment_id = 5 AND location = 'Mumbai Airport');

-- Create system settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_description TEXT,
    setting_group VARCHAR(50) DEFAULT 'general',
    setting_type ENUM('text', 'number', 'boolean', 'select', 'textarea') DEFAULT 'text',
    setting_label VARCHAR(100),
    is_public TINYINT(1) DEFAULT 0
);

-- Insert geocoding settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_description, setting_group, setting_type, setting_label, is_public) VALUES
('enable_geocoding', '1', 'Enable automatic geocoding of locations', 'geocoding', 'boolean', 'Enable Geocoding', 1),
('auto_save_geocoded_updates', '1', 'Automatically save geocoded tracking updates to database', 'geocoding', 'boolean', 'Auto-save Geocoded Updates', 0);

-- Create geocoding cache table if it doesn't exist
CREATE TABLE IF NOT EXISTS geocoding_cache (
    id INT AUTO_INCREMENT PRIMARY KEY,
    query_type ENUM('geocode', 'reverse') NOT NULL,
    query_string VARCHAR(255) NOT NULL,
    result TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX (query_type, query_string(191))
);
