<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Set page title
$pageTitle = 'Update Database Schema';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="page-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .update-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            background-color: var(--glass-bg);
            border-radius: 16px;
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
        }

        .update-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .update-header h1 {
            color: var(--primary-color);
        }

        .update-content {
            margin-bottom: 30px;
        }

        .update-footer {
            text-align: center;
        }

        .success {
            color: #28a745;
            padding: 10px;
            background-color: rgba(40, 167, 69, 0.1);
            border-radius: 8px;
            margin: 10px 0;
        }

        .error {
            color: #dc3545;
            padding: 10px;
            background-color: rgba(220, 53, 69, 0.1);
            border-radius: 8px;
            margin: 10px 0;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .log {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        
        .log p {
            margin: 5px 0;
        }
        
        .log .success {
            color: #28a745;
            background: none;
            padding: 0;
        }
        
        .log .error {
            color: #dc3545;
            background: none;
            padding: 0;
        }
        
        .log .info {
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="update-container">
        <div class="update-header">
            <h1><i class="fas fa-database"></i> Update Database Schema</h1>
            <p>Adding new fields for package pictures, shopper info, and receiver info</p>
        </div>

        <div class="update-content">
            <div class="log">
<?php
// Run the database update
try {
    global $conn;
    
    // Check if package_picture column exists in shipments table
    $stmt = $conn->prepare("SELECT COUNT(*) as column_exists
                          FROM INFORMATION_SCHEMA.COLUMNS
                          WHERE TABLE_SCHEMA = ?
                            AND TABLE_NAME = 'shipments'
                            AND COLUMN_NAME = 'package_picture'");
    $stmt->execute([DB_NAME]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['column_exists'] == 0) {
        // Add package_picture column
        $conn->exec("ALTER TABLE shipments ADD COLUMN package_picture VARCHAR(255) DEFAULT NULL");
        echo "<p class='success'>✓ Added package_picture column to shipments table.</p>";
    } else {
        echo "<p class='info'>ℹ package_picture column already exists.</p>";
    }
    
    // Check if shopper_name column exists
    $stmt = $conn->prepare("SELECT COUNT(*) as column_exists
                          FROM INFORMATION_SCHEMA.COLUMNS
                          WHERE TABLE_SCHEMA = ?
                            AND TABLE_NAME = 'shipments'
                            AND COLUMN_NAME = 'shopper_name'");
    $stmt->execute([DB_NAME]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['column_exists'] == 0) {
        // Add shopper info columns
        $conn->exec("ALTER TABLE shipments ADD COLUMN shopper_name VARCHAR(100) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN shopper_email VARCHAR(100) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN shopper_phone VARCHAR(50) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN shopper_address TEXT DEFAULT NULL");
        echo "<p class='success'>✓ Added shopper info columns to shipments table.</p>";
    } else {
        echo "<p class='info'>ℹ Shopper info columns already exist.</p>";
    }
    
    // Check if receiver_name column exists
    $stmt = $conn->prepare("SELECT COUNT(*) as column_exists
                          FROM INFORMATION_SCHEMA.COLUMNS
                          WHERE TABLE_SCHEMA = ?
                            AND TABLE_NAME = 'shipments'
                            AND COLUMN_NAME = 'receiver_name'");
    $stmt->execute([DB_NAME]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['column_exists'] == 0) {
        // Add receiver info columns
        $conn->exec("ALTER TABLE shipments ADD COLUMN receiver_name VARCHAR(100) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN receiver_email VARCHAR(100) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN receiver_phone VARCHAR(50) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN receiver_address TEXT DEFAULT NULL");
        echo "<p class='success'>✓ Added receiver info columns to shipments table.</p>";
    } else {
        echo "<p class='info'>ℹ Receiver info columns already exist.</p>";
    }
    
    // Check if package_weight column exists
    $stmt = $conn->prepare("SELECT COUNT(*) as column_exists
                          FROM INFORMATION_SCHEMA.COLUMNS
                          WHERE TABLE_SCHEMA = ?
                            AND TABLE_NAME = 'shipments'
                            AND COLUMN_NAME = 'package_weight'");
    $stmt->execute([DB_NAME]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['column_exists'] == 0) {
        // Add additional shipment info columns
        $conn->exec("ALTER TABLE shipments ADD COLUMN package_weight DECIMAL(10,2) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN package_dimensions VARCHAR(50) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN shipping_service VARCHAR(100) DEFAULT NULL");
        $conn->exec("ALTER TABLE shipments ADD COLUMN shipping_cost DECIMAL(10,2) DEFAULT NULL");
        echo "<p class='success'>✓ Added additional shipment info columns to shipments table.</p>";
    } else {
        echo "<p class='info'>ℹ Additional shipment info columns already exist.</p>";
    }
    
    // Create uploads directory if it doesn't exist
    $uploadsDir = 'uploads/packages';
    if (!file_exists($uploadsDir)) {
        if (mkdir($uploadsDir, 0755, true)) {
            echo "<p class='success'>✓ Created uploads directory for package pictures.</p>";
        } else {
            echo "<p class='error'>✗ Failed to create uploads directory. Please create it manually: $uploadsDir</p>";
        }
    } else {
        echo "<p class='info'>ℹ Uploads directory already exists.</p>";
    }
    
    // Update existing shipments with sample data
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM shipments WHERE shopper_name IS NULL");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $emptyRecords = $result['count'];
    
    if ($emptyRecords > 0) {
        $stmt = $conn->prepare("UPDATE shipments SET 
            shopper_name = CONCAT('Shopper for ', customer_name),
            shopper_email = CONCAT(LOWER(REPLACE(customer_name, ' ', '.')), '@example.com'),
            shopper_phone = CONCAT('+1', FLOOR(RAND() * 900000000) + 1000000000),
            shopper_address = CONCAT(FLOOR(RAND() * 1000) + 1, ' Main St, ', origin),
            receiver_name = customer_name,
            receiver_email = CONCAT(LOWER(REPLACE(customer_name, ' ', '.')), '@receiver.com'),
            receiver_phone = CONCAT('+1', FLOOR(RAND() * 900000000) + 1000000000),
            receiver_address = CONCAT(FLOOR(RAND() * 1000) + 1, ' Delivery Ave, ', destination),
            package_weight = ROUND(RAND() * 20 + 1, 2),
            package_dimensions = CONCAT(FLOOR(RAND() * 30) + 10, 'x', FLOOR(RAND() * 30) + 10, 'x', FLOOR(RAND() * 30) + 10, ' cm'),
            shipping_service = CASE FLOOR(RAND() * 3)
                WHEN 0 THEN 'Standard Shipping'
                WHEN 1 THEN 'Express Shipping'
                ELSE 'Priority Shipping'
            END,
            shipping_cost = ROUND(RAND() * 100 + 10, 2)
            WHERE shopper_name IS NULL");
        $stmt->execute();
        $updatedRows = $stmt->rowCount();
        echo "<p class='success'>✓ Updated $updatedRows existing shipments with sample data.</p>";
    } else {
        echo "<p class='info'>ℹ No shipments need sample data updates.</p>";
    }
    
    echo "<p class='success'><strong>✓ Database schema update completed successfully!</strong></p>";
} catch(PDOException $e) {
    echo "<p class='error'><strong>✗ Database update failed:</strong> " . $e->getMessage() . "</p>";
}
?>
            </div>
            
            <p>The database schema has been updated to include fields for package pictures, shopper information, receiver information, and additional shipment details. These fields are now available for use in the admin interface and tracking page.</p>
            
            <p><strong>Next Steps:</strong></p>
            <ul>
                <li>Go to the admin dashboard to manage shipments with the new fields</li>
                <li>Upload package pictures and add shopper/receiver information to shipments</li>
                <li>View the enhanced tracking page with the new information</li>
            </ul>
        </div>

        <div class="update-footer">
            <a href="admin/index.php" class="btn">Go to Admin Dashboard</a>
        </div>
    </div>
</body>
</html>
