<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Only start session if one doesn't already exist
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
$step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$error = '';
$success = '';


// Debug file existence
$config_file_exists = file_exists('includes/config.php');
error_log("Config file exists: " . ($config_file_exists ? 'true' : 'false'));
error_log("Current directory: " . getcwd());
error_log("Step: " . $step);

// If configuration file already exists, redirect to index
if($config_file_exists && $step === 1) {
    header('Location: index.php');
    exit;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($step === 1) {
        $db_host = trim($_POST['db_host']);
        $db_name = trim($_POST['db_name']);
        $db_user = trim($_POST['db_user']);
        $db_pass = $_POST['db_pass'];

        // Test database connection without database name first
        try {
            // Connect without database name
            $conn = new PDO("mysql:host=$db_host", $db_user, $db_pass);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Create database if it doesn't exist
            $conn->exec("CREATE DATABASE IF NOT EXISTS `$db_name`");

            // Now connect with the database
            $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
            $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            // Create config file
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
            $host = $_SERVER['HTTP_HOST'];
            $base_path = dirname($_SERVER['SCRIPT_NAME']);
            $base_path = str_replace('/includes', '', $base_path);
            $base_path = str_replace('/admin', '', $base_path);
            $base_path = str_replace('/tracking', '', $base_path);
            $base_path = str_replace('/user', '', $base_path);
            $site_url = $protocol . $host . $base_path;

            $config_content = "<?php
// Only start session if one doesn't already exist
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

define('DB_HOST', '" . addslashes($db_host) . "');
define('DB_USER', '" . addslashes($db_user) . "');
define('DB_PASS', '" . addslashes($db_pass) . "');
define('DB_NAME', '" . addslashes($db_name) . "');

define('SITE_NAME', 'TransLogix Tracking System');

// Calculate base URL dynamically
\$protocol = isset(\$_SERVER['HTTPS']) && \$_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
\$host = \$_SERVER['HTTP_HOST'];
\$base_path = dirname(\$_SERVER['SCRIPT_NAME']);
\$base_path = str_replace('/includes', '', \$base_path);
\$base_path = str_replace('/admin', '', \$base_path);
\$base_path = str_replace('/tracking', '', \$base_path);
\$base_path = str_replace('/user', '', \$base_path);
define('SITE_URL', \$protocol . \$host . \$base_path);

define('ADMIN_EMAIL', '<EMAIL>');

error_reporting(E_ALL);
ini_set('display_errors', 1);

date_default_timezone_set('UTC');
";

            // Make sure the includes directory exists
            if (!is_dir('includes')) {
                mkdir('includes', 0755, true);
                error_log("Created includes directory");
            }

            // Write config file
            if (file_put_contents('includes/config.php', $config_content)) {
                // Import database structure
                if (file_exists('database.sql')) {
                    $sql = file_get_contents('database.sql');
                    $conn->exec($sql);
                    error_log("Imported database structure from database.sql");
                } else {
                    error_log("Warning: database.sql file not found");
                }

                // Create admin user
                $admin_username = 'admin';
                $admin_password = 'admin123';
                $admin_email = '<EMAIL>';
                $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

                // Check if admin user already exists
                $stmt = $conn->prepare("SELECT id FROM users WHERE username = ?");
                $stmt->execute([$admin_username]);
                if (!$stmt->fetch()) {
                    // Create admin user if doesn't exist
                    $stmt = $conn->prepare("INSERT INTO users (username, password, role, email) VALUES (?, ?, 'admin', ?)");
                    $stmt->execute([$admin_username, $hashed_password, $admin_email]);

                    // Log the creation
                    error_log("Admin user created with username: $admin_username");
                }

                // Create uploads directory for package pictures
                $uploadsDir = 'uploads/packages';
                if (!file_exists($uploadsDir)) {
                    mkdir($uploadsDir, 0755, true);
                }

                // Run the status consistency fix script
                if (file_exists('fix_status_consistency.php')) {
                    include_once 'fix_status_consistency.php';
                    error_log("Ran fix_status_consistency.php");
                } else {
                    error_log("Warning: fix_status_consistency.php file not found");
                }

                // Ensure all tracking updates have coordinates
                // This will use the geocoding functionality to add coordinates to any tracking updates that don't have them
                try {
                    // Check if we have any tracking updates without coordinates
                    $checkStmt = $conn->query("SELECT COUNT(*) as count FROM tracking_updates WHERE latitude IS NULL OR longitude IS NULL");
                    $missingCoords = $checkStmt->fetch(PDO::FETCH_ASSOC)['count'];

                    if ($missingCoords > 0) {
                        // Include geocoding helper
                        if (file_exists('includes/geocoding.php')) {
                            require_once 'includes/geocoding.php';
                            $geocoder = new GeocodingHelper();
                            error_log("Loaded geocoding.php");
                        } else {
                            error_log("Warning: includes/geocoding.php file not found");
                            throw new Exception("Geocoding helper file not found");
                        }

                        // Get tracking updates without coordinates
                        $updateStmt = $conn->query("SELECT id, location FROM tracking_updates WHERE (latitude IS NULL OR longitude IS NULL) AND location IS NOT NULL AND location != ''");
                        $updates = $updateStmt->fetchAll(PDO::FETCH_ASSOC);

                        foreach ($updates as $update) {
                            // Try to geocode the location
                            $result = $geocoder->geocode($update['location']);

                            if ($result) {
                                // Update the database with the coordinates
                                $geoStmt = $conn->prepare("UPDATE tracking_updates SET latitude = ?, longitude = ? WHERE id = ?");
                                $geoStmt->execute([$result['latitude'], $result['longitude'], $update['id']]);
                                error_log("Geocoded location '{$update['location']}' to {$result['latitude']}, {$result['longitude']}");
                            }
                        }
                    }
                } catch (Exception $e) {
                    // Log the error but continue with installation
                    error_log("Error geocoding tracking updates: " . $e->getMessage());
                }

                // Update sample shipments with shopper and receiver info
                $updateSql = "UPDATE shipments SET
                    shipment_name = CONCAT('Shipment to ', destination),
                    shipment_description = CONCAT('This is a sample shipment from ', origin, ' to ', destination, ' for ', customer_name),
                    shopper_name = CONCAT('Shopper for ', customer_name),
                    shopper_email = CONCAT(LOWER(REPLACE(customer_name, ' ', '.')), '@example.com'),
                    shopper_phone = CONCAT('+1', FLOOR(RAND() * 900000000) + 1000000000),
                    shopper_address = CONCAT(FLOOR(RAND() * 1000) + 1, ' Main St, ', origin),
                    receiver_name = customer_name,
                    receiver_email = CONCAT(LOWER(REPLACE(customer_name, ' ', '.')), '@receiver.com'),
                    receiver_phone = CONCAT('+1', FLOOR(RAND() * 900000000) + 1000000000),
                    receiver_address = CONCAT(FLOOR(RAND() * 1000) + 1, ' Delivery Ave, ', destination),
                    package_name = CASE FLOOR(RAND() * 4)
                        WHEN 0 THEN 'Standard Package'
                        WHEN 1 THEN 'Gift Package'
                        WHEN 2 THEN 'Electronics Package'
                        ELSE 'Fragile Package'
                    END,
                    package_description = CASE FLOOR(RAND() * 4)
                        WHEN 0 THEN 'Contains standard shipping items'
                        WHEN 1 THEN 'Contains gift items, handle with care'
                        WHEN 2 THEN 'Contains electronic equipment, keep dry'
                        ELSE 'Fragile items inside, handle with extreme care'
                    END,
                    package_weight = ROUND(RAND() * 20 + 1, 2),
                    package_dimensions = CONCAT(FLOOR(RAND() * 30) + 10, 'x', FLOOR(RAND() * 30) + 10, 'x', FLOOR(RAND() * 30) + 10, ' cm'),
                    shipping_service = CASE FLOOR(RAND() * 3)
                        WHEN 0 THEN 'Standard Shipping'
                        WHEN 1 THEN 'Express Shipping'
                        ELSE 'Priority Shipping'
                    END,
                    shipping_cost = ROUND(RAND() * 100 + 10, 2)
                    WHERE shopper_name IS NULL";
                $conn->exec($updateSql);

                $success = "Installation completed successfully!";
                header("Location: install.php?step=2");
                exit;
            } else {
                $error = "Could not write configuration file. Please check permissions.";
            }

        } catch(PDOException $e) {
            $error = "Database connection failed: " . $e->getMessage();
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TransLogix Installation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Your existing CSS styles */
        :root {
            --primary-color: #5c2be2;
            --text-color: #333;
            --bg-color: #fff;
            --glass-bg: rgba(255, 255, 255, 0.9);
            --glass-border: 1px solid rgba(255, 255, 255, 0.2);
        }

        body.dark-theme {
            --text-color: #fff;
            --bg-color: #1a1a1a;
            --glass-bg: rgba(0, 0, 0, 0.8);
            --glass-border: 1px solid rgba(255, 255, 255, 0.1);
        }

        body {
            background-color: var(--bg-color);
            color: var(--text-color);
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .install-wrapper {
            background: var(--glass-bg);
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .btn {
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-error {
            background: #ff5757;
            color: white;
        }

        .alert-success {
            background: #4CAF50;
            color: white;
        }

        /* Success page styles */
        .success-message {
            text-align: center;
            padding: 20px 0;
        }

        .success-message h2 {
            color: #4CAF50;
            margin-bottom: 20px;
        }

        .feature-list, .login-details, .next-steps {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
        }

        .feature-list ul {
            list-style: none;
            padding: 0;
            text-align: left;
        }

        .feature-list li {
            padding: 8px 0;
        }

        .feature-list i {
            color: #4CAF50;
            margin-right: 10px;
        }

        .login-details {
            background: rgba(92, 43, 226, 0.1);
        }

        .warning {
            color: #ff9800;
            font-weight: bold;
        }

        .button-group {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-wrapper">
            <h1>TransLogix Installation</h1>

            <?php if($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>

            <?php if($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>

            <?php if($step === 1): ?>
                <form method="POST" action="install.php?step=1">
                    <h2>Database Configuration</h2>
                    <div class="form-group">
                        <label for="db_host">Database Host</label>
                        <input type="text" id="db_host" name="db_host" value="localhost" required>
                    </div>
                    <div class="form-group">
                        <label for="db_name">Database Name</label>
                        <input type="text" id="db_name" name="db_name" value="tracking_cms" required>
                    </div>
                    <div class="form-group">
                        <label for="db_user">Database Username</label>
                        <input type="text" id="db_user" name="db_user" required>
                    </div>
                    <div class="form-group">
                        <label for="db_pass">Database Password</label>
                        <input type="password" id="db_pass" name="db_pass">
                    </div>
                    <button type="submit" class="btn">Install</button>
                </form>
            <?php elseif($step === 2): ?>
                <div class="success-message">
                    <h2><i class="fas fa-check-circle"></i> Installation Completed Successfully!</h2>
                    <p>The TransLogix Tracking System has been installed and configured with sample data.</p>

                    <div class="feature-list">
                        <h3>Features Installed:</h3>
                        <ul>
                            <li><i class="fas fa-check"></i> Tracking system with geolocation support</li>
                            <li><i class="fas fa-check"></i> Sample shipments with tracking updates</li>
                            <li><i class="fas fa-check"></i> Enhanced shipment details with shipment name, description, package name, and package description</li>
                            <li><i class="fas fa-check"></i> Geocoding functionality for tracking locations</li>
                            <li><i class="fas fa-check"></i> Admin dashboard and user management</li>
                        </ul>
                    </div>

                    <div class="login-details">
                        <h3>Admin Login Details:</h3>
                        <p><strong>Username:</strong> admin</p>
                        <p><strong>Password:</strong> admin123</p>
                        <p class="warning"><i class="fas fa-exclamation-triangle"></i> Please change the default password after logging in!</p>
                    </div>

                    <div class="next-steps">
                        <h3>Next Steps:</h3>
                        <p>You can now:</p>
                        <div class="button-group">
                            <a href="index.php" class="btn">Go to Homepage</a>
                            <a href="admin/index.php" class="btn">Go to Admin Dashboard</a>
                            <a href="tracking/index.php" class="btn">Go to Tracking Page</a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>











