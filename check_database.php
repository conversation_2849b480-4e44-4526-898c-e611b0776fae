<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Check tables in the database
echo "<h2>Database Tables</h2>";
$db->query("SHOW TABLES");
$tables = $db->resultSet();
echo "<pre>";
print_r($tables);
echo "</pre>";

// Check shipments table structure
echo "<h2>Shipments Table Structure</h2>";
$db->query("DESCRIBE shipments");
$shipmentStructure = $db->resultSet();
echo "<pre>";
print_r($shipmentStructure);
echo "</pre>";

// Count shipments
echo "<h2>Shipment Counts</h2>";
$db->query("SELECT COUNT(*) as total FROM shipments");
$totalShipments = $db->single();
echo "Total Shipments: " . ($totalShipments['total'] ?? 0) . "<br>";

// Count shipments by status
echo "<h2>Shipments by Status</h2>";
$db->query("SELECT status, COUNT(*) as count FROM shipments GROUP BY status");
$shipmentsByStatus = $db->resultSet();
echo "<pre>";
print_r($shipmentsByStatus);
echo "</pre>";

// Get sample shipment data
echo "<h2>Sample Shipment Data (5 records)</h2>";
$db->query("SELECT * FROM shipments LIMIT 5");
$sampleShipments = $db->resultSet();
echo "<pre>";
print_r($sampleShipments);
echo "</pre>";

// Check if there are any tracking_updates
echo "<h2>Tracking Updates</h2>";
$db->query("SHOW TABLES LIKE 'tracking_updates'");
$trackingUpdatesTable = $db->resultSet();
if (!empty($trackingUpdatesTable)) {
    $db->query("SELECT COUNT(*) as total FROM tracking_updates");
    $totalUpdates = $db->single();
    echo "Total Tracking Updates: " . ($totalUpdates['total'] ?? 0) . "<br>";
    
    // Get sample tracking update data
    $db->query("SELECT * FROM tracking_updates LIMIT 5");
    $sampleUpdates = $db->resultSet();
    echo "<pre>";
    print_r($sampleUpdates);
    echo "</pre>";
} else {
    echo "Tracking Updates table does not exist<br>";
}

// Check if there are any users
echo "<h2>Users</h2>";
$db->query("SHOW TABLES LIKE 'users'");
$usersTable = $db->resultSet();
if (!empty($usersTable)) {
    $db->query("SELECT COUNT(*) as total FROM users");
    $totalUsers = $db->single();
    echo "Total Users: " . ($totalUsers['total'] ?? 0) . "<br>";
    
    // Get sample user data (without passwords)
    $db->query("SELECT id, username, email, role, created_at FROM users LIMIT 5");
    $sampleUsers = $db->resultSet();
    echo "<pre>";
    print_r($sampleUsers);
    echo "</pre>";
} else {
    echo "Users table does not exist<br>";
}
