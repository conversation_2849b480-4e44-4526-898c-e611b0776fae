<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if shipments table exists
$db->query("SHOW TABLES LIKE 'shipments'");
$shipmentsTable = $db->resultSet();

if (empty($shipmentsTable)) {
    // Create shipments table if it doesn't exist
    $db->query("CREATE TABLE IF NOT EXISTS shipments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tracking_number VARCHAR(20) NOT NULL,
        customer_name VARCHAR(100) NOT NULL,
        origin VARCHAR(100) NOT NULL,
        destination VARCHAR(100) NOT NULL,
        status ENUM('pending', 'in_transit', 'delivered', 'delayed', 'cancelled') NOT NULL DEFAULT 'pending',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME NULL,
        delivered_at DATETIME NULL,
        estimated_delivery DATE NULL,
        notes TEXT NULL
    )");
    $db->execute();
    echo "Shipments table created.<br>";
}

// Check if tracking_updates table exists
$db->query("SHOW TABLES LIKE 'tracking_updates'");
$trackingUpdatesTable = $db->resultSet();

if (empty($trackingUpdatesTable)) {
    // Create tracking_updates table if it doesn't exist
    $db->query("CREATE TABLE IF NOT EXISTS tracking_updates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        shipment_id INT NOT NULL,
        status ENUM('pending', 'in_transit', 'delivered', 'delayed', 'cancelled') NOT NULL,
        location VARCHAR(100) NULL,
        notes TEXT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE
    )");
    $db->execute();
    echo "Tracking updates table created.<br>";
}

// Check if users table exists
$db->query("SHOW TABLES LIKE 'users'");
$usersTable = $db->resultSet();

if (empty($usersTable)) {
    // Create users table if it doesn't exist
    $db->query("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        email VARCHAR(100) NOT NULL UNIQUE,
        role ENUM('admin', 'staff', 'customer') NOT NULL DEFAULT 'customer',
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
    )");
    $db->execute();
    echo "Users table created.<br>";
    
    // Insert admin user
    $password = password_hash('admin123', PASSWORD_DEFAULT);
    $db->query("INSERT INTO users (username, password, email, role) VALUES ('admin', :password, '<EMAIL>', 'admin')");
    $db->bind(':password', $password);
    $db->execute();
    echo "Admin user created.<br>";
}

// Check if we already have shipments
$db->query("SELECT COUNT(*) as count FROM shipments");
$shipmentCount = $db->single();

if ($shipmentCount['count'] < 10) {
    // Insert sample shipments
    $statuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
    $origins = ['New York, USA', 'Los Angeles, USA', 'Chicago, USA', 'London, UK', 'Paris, France', 'Berlin, Germany', 'Tokyo, Japan', 'Sydney, Australia'];
    $destinations = ['Miami, USA', 'Dallas, USA', 'Seattle, USA', 'Manchester, UK', 'Lyon, France', 'Munich, Germany', 'Osaka, Japan', 'Melbourne, Australia'];
    $customers = ['John Smith', 'Jane Doe', 'Robert Johnson', 'Emily Brown', 'Michael Davis', 'Sarah Wilson', 'David Miller', 'Lisa Taylor'];
    
    // Generate shipments for the past year
    for ($i = 0; $i < 50; $i++) {
        $trackingNumber = generateTrackingNumber();
        $customerName = $customers[array_rand($customers)];
        $origin = $origins[array_rand($origins)];
        $destination = $destinations[array_rand($destinations)];
        $status = $statuses[array_rand($statuses)];
        
        // Random date within the past year
        $daysAgo = rand(0, 365);
        $createdAt = date('Y-m-d H:i:s', strtotime("-$daysAgo days"));
        
        // Set delivered_at if status is delivered
        $deliveredAt = null;
        if ($status === 'delivered') {
            $deliveryDays = rand(1, 10);
            $deliveredAt = date('Y-m-d H:i:s', strtotime($createdAt . " +$deliveryDays days"));
        }
        
        // Set estimated delivery date
        $estimatedDelivery = date('Y-m-d', strtotime($createdAt . " +7 days"));
        
        // Insert shipment
        $db->query("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, delivered_at, estimated_delivery) 
                    VALUES (:tracking_number, :customer_name, :origin, :destination, :status, :created_at, :delivered_at, :estimated_delivery)");
        $db->bind(':tracking_number', $trackingNumber);
        $db->bind(':customer_name', $customerName);
        $db->bind(':origin', $origin);
        $db->bind(':destination', $destination);
        $db->bind(':status', $status);
        $db->bind(':created_at', $createdAt);
        $db->bind(':delivered_at', $deliveredAt);
        $db->bind(':estimated_delivery', $estimatedDelivery);
        $db->execute();
        
        $shipmentId = $db->lastInsertId();
        
        // Add tracking updates
        $updateCount = rand(1, 5);
        $currentStatus = 'pending';
        $statusIndex = 0;
        
        for ($j = 0; $j < $updateCount; $j++) {
            // Progress through statuses
            if ($j > 0 && $statusIndex < count($statuses) - 1) {
                $statusIndex++;
            }
            
            if ($status === 'delivered' && $j === $updateCount - 1) {
                $currentStatus = 'delivered';
            } else if ($status === 'delayed' && $j === $updateCount - 1) {
                $currentStatus = 'delayed';
            } else if ($status === 'cancelled' && $j === $updateCount - 1) {
                $currentStatus = 'cancelled';
            } else {
                $currentStatus = $statuses[$statusIndex];
            }
            
            $updateDaysAfter = rand(0, $daysAgo / $updateCount * ($j + 1));
            $updateDate = date('Y-m-d H:i:s', strtotime($createdAt . " +$updateDaysAfter days"));
            
            $location = $j === 0 ? $origin : ($j === $updateCount - 1 ? $destination : $origins[array_rand($origins)]);
            $notes = "Shipment $currentStatus at $location";
            
            $db->query("INSERT INTO tracking_updates (shipment_id, status, location, notes, created_at) 
                        VALUES (:shipment_id, :status, :location, :notes, :created_at)");
            $db->bind(':shipment_id', $shipmentId);
            $db->bind(':status', $currentStatus);
            $db->bind(':location', $location);
            $db->bind(':notes', $notes);
            $db->bind(':created_at', $updateDate);
            $db->execute();
        }
    }
    
    echo "50 sample shipments with tracking updates created.<br>";
}

echo "Sample data insertion complete.<br>";
echo "<a href='admin/index.php'>Go to Admin Dashboard</a>";
