<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Connect to database
    $conn = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Reset admin password to 'admin123'
    $admin_username = 'admin';
    $admin_password = 'admin123';
    $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);

    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE username = ? AND role = 'admin'");
    $result = $stmt->execute([$hashed_password, $admin_username]);

    if ($result) {
        echo "Admin password has been reset successfully to: admin123";
        
        // Show current admin details
        $stmt = $conn->prepare("SELECT username, email, role FROM users WHERE username = ? AND role = 'admin'");
        $stmt->execute([$admin_username]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<pre>";
        print_r($admin);
        echo "</pre>";
    } else {
        echo "Failed to reset admin password";
    }

} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}