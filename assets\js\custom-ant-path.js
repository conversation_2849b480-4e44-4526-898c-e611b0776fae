/**
 * Custom AntPath implementation for the tracking map
 * This ensures the animation works correctly even if the Leaflet AntPath plugin has issues
 */
(function() {
    // Create a custom AntPath function if it doesn't exist
    if (!window.createCustomAntPath) {
        window.createCustomAntPath = function(map, coordinates, options) {
            // First, remove any existing custom paths
            if (window.currentAntPath && window.currentAntPath.remove) {
                window.currentAntPath.remove();
            }

            if (window.currentFallbackPath && window.currentFallbackPath.remove) {
                window.currentFallbackPath.remove();
            }

            // Also remove any paths with the same class
            map.eachLayer(function(layer) {
                if (layer._path && layer._path.classList) {
                    if (options.className && layer._path.classList.contains(options.className)) {
                        map.removeLayer(layer);
                    }
                }
            });
            // Default options
            const defaultOptions = {
                color: '#5c2be2',
                weight: 5,
                opacity: 0.8,
                dashArray: '10, 20',
                pulseColor: '#00d45f',
                delay: 800,
                className: 'custom-ant-path'
            };

            // Merge options
            const pathOptions = Object.assign({}, defaultOptions, options);

            // Create the base path
            const basePath = L.polyline(coordinates, {
                color: pathOptions.color,
                weight: pathOptions.weight,
                opacity: pathOptions.opacity,
                className: pathOptions.className,
                interactive: false
            }).addTo(map);

            // Apply animation styles directly to the path element
            if (basePath._path) {
                basePath._path.style.strokeDasharray = pathOptions.dashArray;
                basePath._path.style.strokeLinecap = 'round';
                basePath._path.style.animation = 'antDash 30s linear infinite';
                basePath._path.classList.add('ant-path-animated');

                // Ensure no fill
                basePath._path.setAttribute('fill', 'none');
                basePath._path.setAttribute('fill-opacity', '0');
            }

            // Create the pulse effect with a second path if pulseColor is provided
            let pulsePath = null;
            if (pathOptions.pulseColor) {
                pulsePath = L.polyline(coordinates, {
                    color: pathOptions.pulseColor,
                    weight: pathOptions.weight,
                    opacity: 0.4,
                    className: pathOptions.className + '-pulse',
                    interactive: false
                }).addTo(map);

                // Apply animation styles to the pulse path
                if (pulsePath._path) {
                    pulsePath._path.style.strokeDasharray = pathOptions.dashArray;
                    pulsePath._path.style.strokeLinecap = 'round';
                    pulsePath._path.style.animation = 'antDash 30s linear infinite reverse';
                    pulsePath._path.classList.add('ant-path-animated');

                    // Ensure no fill
                    pulsePath._path.setAttribute('fill', 'none');
                    pulsePath._path.setAttribute('fill-opacity', '0');
                }
            }

            // Return an object with methods to control the paths
            return {
                basePath: basePath,
                pulsePath: pulsePath,

                // Method to remove the paths from the map
                remove: function() {
                    if (basePath) map.removeLayer(basePath);
                    if (pulsePath) map.removeLayer(pulsePath);
                },

                // Method to update the path coordinates
                setLatLngs: function(newCoordinates) {
                    if (basePath) basePath.setLatLngs(newCoordinates);
                    if (pulsePath) pulsePath.setLatLngs(newCoordinates);
                }
            };
        };
    }
})();
