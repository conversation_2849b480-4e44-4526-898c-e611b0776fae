/* Mobile Optimization Styles
 * This file contains comprehensive mobile-first optimizations
 * to improve the mobile experience across the entire site
 */

/* ===== Base Mobile Optimizations ===== */

/* Improve tap targets for mobile */
@media (max-width: 768px) {
    /* Make all interactive elements have at least 44px touch target */
    button,
    .btn,
    a.btn,
    .action-btn,
    input[type="submit"],
    input[type="button"],
    .nav-link,
    .mobile-menu-toggle,
    .theme-toggle button,
    .sidebar-toggle {
        min-height: 44px;
        min-width: 44px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Increase spacing between interactive elements */
    nav ul li,
    .action-btn,
    .form-group,
    .btn-group .btn {
        margin-bottom: 12px;
    }

    /* Improve form elements for touch */
    input,
    select,
    textarea {
        font-size: 16px; /* Prevents iOS zoom on focus */
        padding: 12px;
        border-radius: 8px;
        margin-bottom: 16px;
    }

    /* Checkbox and radio improvements */
    input[type="checkbox"],
    input[type="radio"] {
        width: 22px;
        height: 22px;
        margin-right: 10px;
    }

    label {
        display: block;
        margin-bottom: 12px;
    }

    /* Specific fix for tracking form labels */
    .tracking-form label {
        margin-bottom: 35px;
        display: block;
        position: relative;
        font-size: 0.85rem;
    }

    /* Add space between label and input */
    .tracking-form input {
        padding-top: 15px;
    }
}

/* ===== Mobile Typography ===== */

@media (max-width: 768px) {
    /* Ensure minimum font sizes for readability */
    body {
        font-size: 16px; /* Prevent iOS zoom on form focus */
        line-height: 1.6;
    }

    /* Mobile-specific heading adjustments */
    h1, .h1 {
        font-size: clamp(1.8rem, 8vw, 2.5rem);
        line-height: 1.2;
        margin-bottom: 16px;
    }

    h2, .h2 {
        font-size: clamp(1.5rem, 6vw, 2rem);
        line-height: 1.3;
        margin-bottom: 14px;
    }

    h3, .h3 {
        font-size: clamp(1.2rem, 5vw, 1.5rem);
        line-height: 1.3;
        margin-bottom: 12px;
    }

    h4, .h4 {
        font-size: clamp(1.1rem, 4vw, 1.3rem);
        line-height: 1.4;
        margin-bottom: 10px;
    }

    h5, .h5 {
        font-size: clamp(1rem, 3vw, 1.2rem);
        line-height: 1.4;
        margin-bottom: 8px;
    }

    h6, .h6 {
        font-size: clamp(0.9rem, 2.5vw, 1.1rem);
        line-height: 1.4;
        margin-bottom: 6px;
    }

    /* Improve paragraph and text readability */
    p {
        font-size: 16px; /* Fixed size for better readability */
        line-height: 1.6;
        margin-bottom: 16px;
    }

    /* List items */
    li {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 8px;
    }

    /* Button text */
    .btn {
        font-size: 16px; /* Consistent button text size */
        padding: 12px 20px;
        min-height: 48px; /* Larger touch target for mobile */
    }

    /* Form elements */
    input, select, textarea {
        font-size: 16px; /* Prevents iOS zoom */
        padding: 12px;
        line-height: 1.4;
    }

    /* Navigation text */
    nav ul li a {
        font-size: 16px;
        padding: 12px 16px;
    }

    /* Card and section text */
    .card, .glass-card {
        font-size: 16px;
    }

    .card h3, .glass-card h3 {
        font-size: clamp(1.2rem, 4vw, 1.4rem);
    }

    /* Table text */
    table, .table {
        font-size: 14px; /* Slightly smaller for data tables */
    }

    .table th, .table td {
        padding: 8px 12px;
        font-size: 14px;
    }

    /* Status badges and labels */
    .badge, .label {
        font-size: 12px;
        padding: 4px 8px;
    }

    /* Stat numbers and metrics */
    .stat-number {
        font-size: clamp(1.5rem, 6vw, 2rem);
    }

    .stat-label {
        font-size: 14px;
    }

    /* Footer text */
    footer {
        font-size: 14px;
    }

    footer h4 {
        font-size: clamp(1rem, 3vw, 1.2rem);
    }
}

/* ===== Mobile Layout Improvements ===== */

@media (max-width: 768px) {
    /* Add more breathing room */
    .container {
        padding-left: 16px;
        padding-right: 16px;
    }

    /* Improve section spacing */
    section {
        padding: 40px 0;
    }

    /* Better card layouts */
    .card,
    .glass-card,
    .service-card,
    .industry-card,
    .stat-card,
    .summary-card {
        padding: 20px;
        margin-bottom: 20px;
        border-radius: 12px;
    }

    /* Ensure all grids stack properly */
    .grid,
    .stats-grid,
    .services-grid,
    .industries-grid,
    .dashboard-grid,
    .metrics-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Improve spacing in flex containers */
    .flex-container,
    .flex-row,
    .admin-actions,
    .form-row {
        flex-direction: column;
        gap: 16px;
    }

    /* Full-width buttons on mobile */
    .btn,
    .action-btn,
    .primary-btn,
    .secondary-btn,
    .outline-btn {
        width: 100%;
        text-align: center;
        justify-content: center;
    }

    /* Button groups should stack */
    .btn-group,
    .action-group,
    .cta-buttons {
        flex-direction: column;
        width: 100%;
        gap: 12px;
    }
}

/* ===== Mobile Navigation Enhancements ===== */

@media (max-width: 768px) {
    /* Remove conflicting mobile menu styles - using bottom nav only */
    .mobile-menu-toggle {
        display: none !important;
    }

    nav#main-nav {
        display: none !important;
    }

    /* Mobile navigation styles removed - using bottom nav only */
}

/* ===== Mobile Table Optimizations ===== */

@media (max-width: 768px) {
    /* Card-based tables for mobile */
    .mobile-card-table {
        display: block;
        width: 100%;
        border: none;
    }

    .mobile-card-table thead {
        display: none; /* Hide table headers */
    }

    .mobile-card-table tbody {
        display: block;
        width: 100%;
    }

    .mobile-card-table tr {
        display: block;
        margin-bottom: 16px;
        border-radius: 8px;
        background: var(--glass-bg);
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
        padding: 16px;
    }

    .mobile-card-table td {
        display: flex;
        padding: 8px 0;
        border: none;
        text-align: left;
        align-items: center;
    }

    .mobile-card-table td::before {
        content: attr(data-label);
        font-weight: 600;
        width: 40%;
        margin-right: 16px;
    }

    /* Actions in mobile tables */
    .mobile-card-table td.actions {
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 8px;
    }

    .mobile-card-table td.actions .action-btn {
        margin: 0;
    }
}

/* ===== Mobile-First Admin Dashboard Design ===== */

@media (max-width: 768px) {
    /* Redesign admin hero for mobile */
    .admin-hero {
        display: block;
        padding: 80px 15px 20px 15px; /* Top padding for fixed header */
        background: none;
        min-height: auto;
    }

    .admin-hero .hero-content {
        text-align: center;
        padding: 0;
    }

    .admin-hero h1 {
        display: none; /* Hide main title */
    }

    .admin-hero p {
        font-size: 1rem;
        margin-bottom: 20px;
        color: var(--text-color);
    }

    /* Mobile admin actions */
    .admin-actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        max-width: 400px;
        margin: 0 auto;
    }

    .admin-actions .btn {
        padding: 12px 8px;
        font-size: 0.85rem;
        text-align: center;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 4px;
        min-height: 60px;
        justify-content: center;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .admin-actions .btn i {
        font-size: 1.2rem;
        margin-bottom: 2px;
    }

    .admin-actions .btn:hover,
    .admin-actions .btn:active {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    /* Create mobile admin header */
    .dashboard-overview::before {
        content: '';
        display: block;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    .dashboard-overview {
        padding-top: 70px; /* Account for fixed header */
    }

    /* Mobile admin top bar */
    .mobile-admin-header {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 60px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        z-index: 1001;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .mobile-admin-title {
        font-size: 1.1rem;
        font-weight: 600;
    }

    .mobile-admin-actions {
        display: flex;
        gap: 10px;
    }

    .mobile-admin-btn {
        background: rgba(255,255,255,0.2);
        border: none;
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.85rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .mobile-admin-btn:hover {
        background: rgba(255,255,255,0.3);
    }

    /* Dashboard container - full width mobile */
    .dashboard-grid {
        display: block;
        padding: 0 15px;
    }

    .dashboard-main {
        width: 100%;
    }

    /* Hide sidebar by default, show via toggle */
    .dashboard-sidebar {
        position: fixed;
        top: 60px;
        right: -100%;
        width: 280px;
        height: calc(100vh - 60px);
        background: white;
        box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        z-index: 999;
        overflow-y: auto;
        transition: right 0.3s ease;
        padding: 20px;
    }

    .dashboard-sidebar.active {
        right: 0;
    }

    /* Sidebar overlay */
    .sidebar-overlay {
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: 998;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .sidebar-overlay.active {
        opacity: 1;
        visibility: visible;
    }

    /* Mobile-optimized stats section */
    .stats-container {
        margin-bottom: 25px;
    }

    .stats-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .stats-header h2 {
        font-size: 1.3rem;
        margin: 0;
    }

    .header-actions {
        display: flex;
        gap: 8px;
    }

    .refresh-stats,
    .view-all {
        padding: 6px 10px;
        font-size: 0.8rem;
        border-radius: 4px;
    }

    /* Stats grid - responsive 2x3 layout */
    .stats-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 12px;
        margin-bottom: 20px;
    }

    /* Compact, touch-friendly stat cards */
    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 16px 12px;
        text-align: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        border: 1px solid rgba(0,0,0,0.05);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    .stat-card:active {
        transform: scale(0.98);
    }

    .stat-card .stat-icon {
        font-size: 1.8rem;
        margin-bottom: 8px;
        opacity: 0.8;
    }

    .stat-card h3 {
        font-size: 0.85rem;
        margin-bottom: 4px;
        color: #666;
        font-weight: 500;
    }

    .stat-card .stat-number {
        font-size: 1.6rem;
        font-weight: 700;
        margin-bottom: 2px;
        color: var(--primary-color);
    }

    .stat-card .stat-percentage {
        font-size: 0.75rem;
        opacity: 0.7;
        color: #888;
    }

    /* Mobile-optimized charts */
    .chart-container {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .chart-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .chart-header h2 {
        font-size: 1.1rem;
        margin: 0;
    }

    .chart-wrapper {
        height: 220px;
        position: relative;
    }

    /* Hide debug info on mobile */
    .debug-info {
        display: none;
    }

    /* Mobile-first table design - card layout */
    .recent-shipments {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .section-title {
        font-size: 1.1rem;
        margin: 0;
    }

    /* Hide table, show card layout */
    .table-container {
        display: none;
    }

    /* Mobile shipment cards */
    .mobile-shipments-list {
        display: block;
    }

    .shipment-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 12px;
        border-left: 4px solid var(--primary-color);
    }

    .shipment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .tracking-number {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.9rem;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .shipment-details {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        font-size: 0.85rem;
        color: #666;
        margin-bottom: 8px;
    }

    .shipment-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }

    .action-btn {
        padding: 6px 8px;
        border-radius: 4px;
        font-size: 0.75rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    /* Sidebar content - mobile optimized */
    .quick-actions {
        margin-bottom: 25px;
    }

    .quick-actions .section-title {
        font-size: 1.1rem;
        margin-bottom: 15px;
        color: var(--primary-color);
    }

    .actions-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .action-card {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        display: flex;
        align-items: center;
        gap: 12px;
        text-decoration: none;
        color: inherit;
        transition: all 0.2s ease;
        border: 1px solid rgba(0,0,0,0.05);
    }

    .action-card:hover {
        background: #e9ecef;
        transform: translateY(-1px);
    }

    .action-card .action-icon {
        width: 36px;
        height: 36px;
        background: var(--primary-color);
        color: white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .action-card h3 {
        font-size: 0.9rem;
        margin: 0 0 2px 0;
        font-weight: 600;
    }

    .action-card p {
        font-size: 0.75rem;
        margin: 0;
        opacity: 0.7;
    }

    /* Performance metrics - mobile cards */
    .performance-metrics {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .metrics-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .metrics-header .section-title {
        font-size: 1.1rem;
        margin: 0;
    }

    .metric-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
    }

    .metric-label {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 6px;
    }

    .metric-icon {
        width: 24px;
        height: 24px;
        background: var(--primary-color);
        color: white;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }

    .metric-name {
        font-size: 0.85rem;
        font-weight: 500;
    }

    .metric-value {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 4px;
    }

    .progress-container {
        height: 6px;
        background: #e9ecef;
        border-radius: 3px;
        overflow: hidden;
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        border-radius: 3px;
        transition: width 0.3s ease;
    }

    /* Activity feed - mobile optimized */
    .activity-feed {
        background: white;
        border-radius: 12px;
        padding: 16px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    }

    .activity-feed .section-title {
        font-size: 1.1rem;
        margin-bottom: 15px;
    }

    .activity-item {
        display: flex;
        gap: 10px;
        padding: 10px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 32px;
        height: 32px;
        background: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        flex-shrink: 0;
    }

    .activity-content {
        flex: 1;
        min-width: 0;
    }

    .activity-title {
        font-size: 0.85rem;
        font-weight: 500;
        margin-bottom: 2px;
        line-height: 1.3;
    }

    .activity-time {
        font-size: 0.75rem;
        color: #888;
    }

    /* ===== Mobile Modal Optimization ===== */

    /* Modal backdrop */
    .modal,
    .popup-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: rgba(0,0,0,0.6) !important;
        z-index: 9999 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
    }

    /* Modal content - full screen approach */
    .modal-content,
    .popup-content {
        background: white !important;
        border-radius: 12px !important;
        width: 100% !important;
        max-width: 100% !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
        position: relative !important;
        margin: 0 !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.3) !important;
    }

    /* Modal header */
    .modal-header {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
        color: white !important;
        padding: 16px 20px !important;
        border-radius: 12px 12px 0 0 !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 1 !important;
    }

    .modal-title {
        font-size: 1.1rem !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .modal-close,
    .close-btn {
        background: rgba(255,255,255,0.2) !important;
        border: none !important;
        color: white !important;
        width: 32px !important;
        height: 32px !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        font-size: 1.2rem !important;
        transition: all 0.2s ease !important;
    }

    .modal-close:hover,
    .close-btn:hover {
        background: rgba(255,255,255,0.3) !important;
    }

    /* Modal body */
    .modal-body {
        padding: 20px !important;
    }

    /* Form elements in modals */
    .modal .form-group {
        margin-bottom: 16px !important;
    }

    .modal .form-group label {
        display: block !important;
        font-size: 0.9rem !important;
        font-weight: 500 !important;
        margin-bottom: 6px !important;
        color: #333 !important;
    }

    .modal input,
    .modal select,
    .modal textarea {
        width: 100% !important;
        padding: 12px !important;
        border: 1px solid #ddd !important;
        border-radius: 8px !important;
        font-size: 16px !important; /* Prevent zoom on iOS */
        background: white !important;
        box-sizing: border-box !important;
    }

    .modal input:focus,
    .modal select:focus,
    .modal textarea:focus {
        outline: none !important;
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 3px rgba(92, 43, 226, 0.1) !important;
    }

    /* Modal footer */
    .modal-footer {
        padding: 16px 20px !important;
        border-top: 1px solid #eee !important;
        display: flex !important;
        gap: 10px !important;
        justify-content: flex-end !important;
        background: #f8f9fa !important;
        border-radius: 0 0 12px 12px !important;
        position: sticky !important;
        bottom: 0 !important;
    }

    .modal-footer .btn {
        flex: 1 !important;
        max-width: 120px !important;
        padding: 12px 16px !important;
        font-size: 0.9rem !important;
        border-radius: 6px !important;
        border: none !important;
        cursor: pointer !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
    }

    /* ===== Mobile Admin Management Pages ===== */

    /* Page headers */
    .page-header {
        background: white;
        padding: 80px 15px 20px 15px; /* Account for fixed mobile header */
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .page-header h1 {
        font-size: 1.5rem;
        margin: 0 0 10px 0;
        color: var(--primary-color);
    }

    /* Data tables - hide on mobile, show cards */
    .table-responsive {
        display: none;
    }

    /* Mobile data cards */
    .mobile-data-cards {
        display: block;
    }

    .data-card {
        background: white;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        border-left: 4px solid var(--primary-color);
    }

    .data-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }

    .data-card-title {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.95rem;
    }

    .data-card-body {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        margin-bottom: 12px;
        font-size: 0.85rem;
    }

    .data-card-actions {
        display: flex;
        gap: 8px;
        justify-content: flex-end;
        border-top: 1px solid #f0f0f0;
        padding-top: 10px;
    }

    .data-card-actions .btn {
        padding: 6px 10px;
        font-size: 0.75rem;
        border-radius: 4px;
        min-width: auto;
    }

    /* Floating action button for add actions */
    .mobile-fab {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 56px;
        height: 56px;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-size: 1.5rem;
        cursor: pointer;
        z-index: 1000;
        transition: all 0.3s ease;
    }

    .mobile-fab:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 16px rgba(0,0,0,0.2);
    }
}

/* ===== Mobile Form Improvements ===== */

@media (max-width: 768px) {
    /* Form layout */
    .form-container {
        padding: 20px;
    }

    /* Floating labels for better UX */
    .form-floating {
        position: relative;
        margin-bottom: 20px;
    }

    .form-floating input,
    .form-floating select,
    .form-floating textarea {
        height: 56px;
        padding: 20px 16px 8px;
    }

    .form-floating textarea {
        height: auto;
        min-height: 100px;
    }

    .form-floating label {
        position: absolute;
        top: 8px;
        left: 16px;
        font-size: 12px;
        color: var(--text-secondary);
        pointer-events: none;
    }

    /* Better select dropdowns */
    select {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 16px center;
        padding-right: 40px;
        appearance: none;
    }
}

/* ===== Mobile Hero Section Improvements ===== */

@media (max-width: 768px) {
    /* Remove conflicting hero styles - handled in main styles.css */
}

/* ===== Mobile Footer Simplification ===== */

@media (max-width: 768px) {
    footer {
        padding: 8px 0 !important;
        margin-bottom: 0 !important; /* Remove conflicting margin */
        min-height: auto !important;
    }

    /* Hide ALL footer content except copyright */
    .footer-content,
    .footer-grid,
    .footer-column,
    .footer-links,
    .footer-social,
    .social-links,
    .footer-newsletter,
    .footer-contact-info,
    .footer-company-info {
        display: none !important;
    }

    /* Show only minimal copyright */
    .footer-bottom {
        padding: 8px 0 !important;
        border-top: none !important;
        text-align: center !important;
        background: transparent !important;
    }

    .footer-bottom p {
        font-size: 10px !important;
        margin: 1px 0 !important;
        line-height: 1.1 !important;
        color: #888 !important;
    }

    /* Very compact developer info */
    .developer-info {
        margin-top: 2px !important;
    }

    .developer-info p {
        font-size: 9px !important;
        margin: 0 !important;
        color: #666 !important;
    }

    .developer-info a {
        color: var(--primary-color) !important;
        text-decoration: none !important;
    }
}

/* ===== Mobile-specific Animations ===== */

@media (max-width: 768px) {
    /* Reduce or eliminate animations that might cause performance issues */
    .service-card:hover,
    .industry-card:hover,
    .btn:hover {
        transform: none;
    }

    /* Use simpler hover effects */
    .btn:active,
    .action-btn:active,
    a:active {
        opacity: 0.7;
    }
}

/* ===== Dark Theme Mobile Adjustments ===== */

@media (max-width: 768px) {
    .dark-theme .mobile-menu-toggle {
        background: rgba(30, 30, 40, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-theme .sidebar-toggle {
        background: var(--primary-dark);
    }
}

/* ===== Mobile Tracking Page Improvements ===== */

@media (max-width: 768px) {
    /* Tracking info layout */
    .tracking-info {
        flex-direction: column;
    }

    .tracking-details {
        width: 100%;
    }

    .tracking-map-container {
        width: 100%;
        margin-top: 20px;
    }

    /* Map section grid improvements */
    .map-section-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 30px;
    }

    /* Adjust map height for mobile */
    #tracking-map {
        height: 300px !important;
        position: relative;
        z-index: 5;
    }

    /* Ensure current location info doesn't overlap */
    .current-location-info {
        position: relative;
        z-index: 10;
        margin-top: 20px;
    }

    /* Shipment progress */
    .shipment-progress {
        padding: 16px;
        margin-top: 20px;
    }

    .progress-step {
        padding-left: 30px;
    }

    .progress-step::before {
        left: 0;
    }

    /* Share button - position relative instead of fixed to prevent overlapping */
    .share-tracking {
        position: relative;
        margin-top: 20px;
        margin-bottom: 10px;
        z-index: 20;
        width: 100%;
        display: flex;
        justify-content: center;
    }

    .share-btn {
        width: auto;
        padding: 10px 20px;
        border-radius: 30px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    }

    /* Improve shipment details grid on mobile */
    .shipment-details-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Adjust timeline for better mobile display */
    .timeline {
        margin-left: 10px;
    }
}

/* ===== Mobile Login/Register Improvements ===== */

@media (max-width: 768px) {
    .auth-container {
        width: 100%;
        padding: 20px;
    }

    .auth-form {
        padding: 20px;
    }

    .auth-options {
        flex-direction: column;
        gap: 10px;
    }
}

/* ===== Mobile Notification Improvements ===== */

@media (max-width: 768px) {
    .notification {
        width: 90%;
        max-width: none;
        left: 5%;
        right: 5%;
        padding: 15px;
    }

    .notification-content {
        font-size: 14px;
    }
}

/* ===== Pull to Refresh ===== */

.refresh-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: transform 0.3s ease;
    transform: translateY(-100%);
}

.refresh-indicator i {
    margin-right: 10px;
}

.refresh-indicator.ready {
    background-color: var(--secondary-color);
}

.refresh-indicator i.rotating {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
