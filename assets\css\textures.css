/* CSS-based textures for backgrounds */

.texture-dots {
    background-image: radial-gradient(var(--primary-color) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
    opacity: 0.05;
}

.texture-grid {
    background-image: 
        linear-gradient(var(--primary-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--primary-color) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.05;
}

.texture-diagonal-lines {
    background-image: repeating-linear-gradient(
        45deg,
        var(--primary-color),
        var(--primary-color) 1px,
        transparent 1px,
        transparent 10px
    );
    opacity: 0.05;
}

.texture-waves {
    background-image: 
        radial-gradient(circle at 100% 50%, transparent 20%, rgba(92, 43, 226, 0.05) 21%, rgba(92, 43, 226, 0.05) 34%, transparent 35%, transparent),
        radial-gradient(circle at 0% 50%, transparent 20%, rgba(92, 43, 226, 0.05) 21%, rgba(92, 43, 226, 0.05) 34%, transparent 35%, transparent);
    background-size: 60px 120px;
    background-position: 0 0;
}

.texture-noise {
    position: relative;
}

.texture-noise::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIzMDAiIGhlaWdodD0iMzAwIj48ZmlsdGVyIGlkPSJhIiB4PSIwIiB5PSIwIj48ZmVUdXJidWxlbmNlIHR5cGU9ImZyYWN0YWxOb2lzZSIgYmFzZUZyZXF1ZW5jeT0iLjc1IiBzdGl0Y2hUaWxlcz0ic3RpdGNoIi8+PGZlQ29sb3JNYXRyaXggdHlwZT0ic2F0dXJhdGUiIHZhbHVlcz0iMCIvPjwvZmlsdGVyPjxwYXRoIGZpbHRlcj0idXJsKCNhKSIgb3BhY2l0eT0iLjA1IiBkPSJNMCAwaDMwMHYzMDBIMHoiLz48L3N2Zz4=');
    pointer-events: none;
}

/* Apply textures to specific sections */
.hero.textured {
    position: relative;
}

.hero.textured::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(var(--primary-color) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.05;
    pointer-events: none;
}

.service-detail.textured,
.industry-detail.textured {
    position: relative;
}

.service-detail.textured::before,
.industry-detail.textured::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(var(--primary-color) 1px, transparent 1px),
        linear-gradient(90deg, var(--primary-color) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.05;
    pointer-events: none;
}

.cta-section.textured {
    position: relative;
}

.cta-section.textured::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: repeating-linear-gradient(
        45deg,
        var(--primary-color),
        var(--primary-color) 1px,
        transparent 1px,
        transparent 10px
    );
    opacity: 0.05;
    pointer-events: none;
}

/* Dark theme adjustments */
.dark-theme .texture-dots,
.dark-theme .texture-grid,
.dark-theme .texture-diagonal-lines,
.dark-theme .texture-waves,
.dark-theme .texture-noise::before,
.dark-theme .hero.textured::before,
.dark-theme .service-detail.textured::before,
.dark-theme .industry-detail.textured::before,
.dark-theme .cta-section.textured::before {
    opacity: 0.03;
}
