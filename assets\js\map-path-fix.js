/**
 * Map Path Fix - Ensures all SVG paths in the map are properly styled
 * This script fixes the issue where paths appear as colored regions instead of lines
 */
document.addEventListener('DOMContentLoaded', function() {
    // Function to fix all SVG paths in the map
    function fixMapPaths() {
        // Wait for the map to be fully initialized
        setTimeout(function() {
            // Find all path elements in the map
            const pathElements = document.querySelectorAll('.leaflet-overlay-pane svg path');
            let fixedCount = 0;
            let skippedCount = 0;

            // Apply fixes to each path
            pathElements.forEach(function(path) {
                // Skip paths that already have animation classes or specific route classes
                if (path.classList.contains('custom-ant-path') ||
                    path.classList.contains('custom-ant-path-pulse') ||
                    path.classList.contains('leaflet-ant-path') ||
                    path.classList.contains('ant-path-animated') ||
                    path.classList.contains('road-path') ||
                    path.classList.contains('air-path') ||
                    path.classList.contains('sea-path') ||
                    path.classList.contains('rail-path') ||
                    path.classList.contains('route-path') ||
                    path.classList.contains('main-route') ||
                    path.classList.contains('segment-route')) {
                    skippedCount++;
                    return;
                }

                // Ensure no fill
                path.setAttribute('fill', 'none');
                path.setAttribute('fill-opacity', '0');

                // Add a class for easier styling
                path.classList.add('map-path-fixed');
                fixedCount++;
            });

            console.log('Map paths fixed:', pathElements.length, fixedCount, skippedCount);
        }, 1000); // Wait 1 second for the map to initialize
    }

    // Run the fix when the page loads
    fixMapPaths();

    // Also run the fix when the map is resized or zoomed
    window.addEventListener('resize', fixMapPaths);

    // Try to detect map zoom events
    const mapElement = document.getElementById('tracking-map');
    if (mapElement) {
        // Use MutationObserver to detect changes in the map
        const observer = new MutationObserver(fixMapPaths);
        observer.observe(mapElement, {
            childList: true,
            subtree: true
        });
    }
});
