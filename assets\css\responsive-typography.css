/* ===== COMPREHENSIVE RESPONSIVE TYPOGRAPHY ===== */

/*
 * This file provides comprehensive responsive typography for the entire application
 * Uses clamp() function for fluid typography that scales smoothly across all devices
 * Ensures minimum font sizes for accessibility and maximum sizes for readability
 */

/* ===== BASE TYPOGRAPHY ===== */

/* Root font size - scales with viewport but maintains readability */
html {
    font-size: clamp(14px, 2.5vw, 16px);
}

/* Body text with responsive line height */
body {
    font-size: 1rem; /* Uses the root font size */
    line-height: clamp(1.4, 1.6, 1.8);
}

/* ===== HEADINGS ===== */

/* Primary heading - hero sections, page titles */
h1, .h1, .hero-title {
    font-size: clamp(1.8rem, 6vw, 3.5rem);
    line-height: clamp(1.1, 1.2, 1.3);
    margin-bottom: clamp(16px, 4vw, 32px);
    font-weight: 700;
}

/* Secondary heading - section titles */
h2, .h2, .section-title {
    font-size: clamp(1.5rem, 5vw, 2.5rem);
    line-height: clamp(1.2, 1.3, 1.4);
    margin-bottom: clamp(14px, 3vw, 24px);
    font-weight: 600;
}

/* Tertiary heading - subsection titles */
h3, .h3, .subsection-title {
    font-size: clamp(1.2rem, 4vw, 2rem);
    line-height: clamp(1.2, 1.3, 1.4);
    margin-bottom: clamp(12px, 2.5vw, 20px);
    font-weight: 600;
}

/* Quaternary heading - card titles, form sections */
h4, .h4, .card-title {
    font-size: clamp(1.1rem, 3.5vw, 1.5rem);
    line-height: clamp(1.3, 1.4, 1.5);
    margin-bottom: clamp(10px, 2vw, 16px);
    font-weight: 600;
}

/* Fifth level heading - small section titles */
h5, .h5 {
    font-size: clamp(1rem, 3vw, 1.25rem);
    line-height: clamp(1.3, 1.4, 1.5);
    margin-bottom: clamp(8px, 1.5vw, 14px);
    font-weight: 600;
}

/* Sixth level heading - minor headings */
h6, .h6 {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    line-height: clamp(1.3, 1.4, 1.5);
    margin-bottom: clamp(6px, 1vw, 12px);
    font-weight: 600;
}

/* ===== TEXT ELEMENTS ===== */

/* Paragraphs */
p {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: clamp(1.5, 1.6, 1.7);
    margin-bottom: clamp(12px, 3vw, 20px);
}

/* Lead paragraph - introductory text */
.lead {
    font-size: clamp(16px, 3.5vw, 20px);
    line-height: clamp(1.4, 1.5, 1.6);
    margin-bottom: clamp(16px, 4vw, 28px);
    font-weight: 400;
}

/* Small text */
small, .small {
    font-size: clamp(12px, 2vw, 14px);
    line-height: clamp(1.4, 1.5, 1.6);
}

/* Large text */
.large {
    font-size: clamp(18px, 4vw, 22px);
    line-height: clamp(1.4, 1.5, 1.6);
}

/* ===== LISTS ===== */

/* List items */
ul li, ol li {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: clamp(1.5, 1.6, 1.7);
    margin-bottom: clamp(4px, 1vw, 8px);
}

/* Navigation lists */
nav ul li {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: 1.4;
}

/* ===== INTERACTIVE ELEMENTS ===== */

/* Buttons */
.btn, button, input[type="submit"], input[type="button"] {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: 1.4;
    padding: clamp(10px, 2.5vw, 14px) clamp(16px, 4vw, 24px);
    min-height: 44px; /* Minimum touch target */
}

/* Links */
a {
    font-size: inherit;
    line-height: inherit;
}

/* ===== FORM ELEMENTS ===== */

/* Input fields */
input, select, textarea {
    font-size: clamp(14px, 2.5vw, 16px); /* Prevents iOS zoom */
    line-height: 1.4;
    padding: clamp(8px, 2vw, 12px);
}

/* Labels */
label {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: 1.4;
    margin-bottom: clamp(4px, 1vw, 8px);
}

/* ===== DATA DISPLAY ===== */

/* Tables */
table, .table {
    font-size: clamp(12px, 2vw, 14px);
}

.table th, .table td {
    font-size: clamp(12px, 2vw, 14px);
    line-height: 1.4;
    padding: clamp(6px, 1.5vw, 10px) clamp(8px, 2vw, 12px);
}

/* Table headers */
.table th {
    font-weight: 600;
}

/* ===== SPECIAL ELEMENTS ===== */

/* Statistics and metrics */
.stat-number {
    font-size: clamp(1.5rem, 6vw, 3rem);
    line-height: 1.1;
    font-weight: 700;
}

.stat-label {
    font-size: clamp(12px, 2vw, 14px);
    line-height: 1.3;
    font-weight: 500;
}

/* Badges and labels */
.badge, .label {
    font-size: clamp(11px, 1.5vw, 13px);
    line-height: 1.2;
    padding: clamp(2px, 0.5vw, 4px) clamp(6px, 1.5vw, 8px);
}

/* Code and preformatted text */
code, pre {
    font-size: clamp(12px, 2vw, 14px);
    line-height: 1.4;
}

/* Blockquotes */
blockquote {
    font-size: clamp(16px, 3vw, 18px);
    line-height: clamp(1.5, 1.6, 1.7);
    margin: clamp(16px, 4vw, 24px) 0;
}

/* ===== MOBILE SPECIFIC OVERRIDES ===== */

@media (max-width: 768px) {
    /* Ensure minimum sizes for mobile readability */
    body {
        font-size: 16px; /* Fixed size to prevent iOS zoom issues */
    }
    
    /* Form elements must be 16px to prevent zoom */
    input, select, textarea {
        font-size: 16px !important;
    }
    
    /* Buttons need adequate touch targets */
    .btn, button {
        min-height: 48px;
        font-size: 16px;
    }
    
    /* Navigation items need larger touch targets */
    nav ul li a {
        font-size: 16px;
        padding: clamp(12px, 3vw, 16px);
    }
}

/* ===== TABLET SPECIFIC ===== */

@media (min-width: 769px) and (max-width: 1024px) {
    /* Tablet-specific adjustments */
    body {
        font-size: 15px;
    }
    
    h1, .h1 {
        font-size: clamp(2rem, 5vw, 2.8rem);
    }
    
    h2, .h2 {
        font-size: clamp(1.6rem, 4vw, 2.2rem);
    }
}

/* ===== LARGE SCREEN OPTIMIZATIONS ===== */

@media (min-width: 1400px) {
    /* Prevent text from becoming too large on very wide screens */
    html {
        font-size: 16px; /* Cap the root font size */
    }
    
    h1, .h1 {
        font-size: 3.5rem; /* Cap heading sizes */
    }
    
    h2, .h2 {
        font-size: 2.5rem;
    }
    
    .container {
        max-width: 1200px; /* Maintain readable line lengths */
    }
}
