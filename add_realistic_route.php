<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>Adding Realistic Route Test Data</h1>";

try {
    // First, clear existing tracking data for a clean test
    $conn->exec("DELETE FROM tracking_updates WHERE shipment_id IN (SELECT id FROM shipments WHERE tracking_number = 'ROUTE123')");
    $conn->exec("DELETE FROM shipments WHERE tracking_number = 'ROUTE123'");
    
    echo "<p>Cleared existing test data.</p>";
    
    // Create a test shipment
    $stmt = $conn->prepare("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, estimated_delivery)
                          VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    $trackingNumber = 'ROUTE123';
    $customerName = 'Route Test Customer';
    $origin = 'New York, USA';
    $destination = 'Los Angeles, USA';
    $status = 'in_transit';
    $createdAt = date('Y-m-d H:i:s', strtotime("-7 days"));
    $estimatedDelivery = date('Y-m-d', strtotime("+3 days"));
    
    $stmt->execute([$trackingNumber, $customerName, $origin, $destination, $status, $createdAt, $estimatedDelivery]);
    $shipmentId = $conn->lastInsertId();
    
    echo "<p>Created test shipment with ID: $shipmentId</p>";
    
    // Define tracking points with exact coordinates for a realistic route
    $trackingPoints = [
        // Origin - New York
        [
            'location' => 'New York Distribution Center, USA',
            'status' => 'pending',
            'lat' => 40.7128,
            'lng' => -74.0060,
            'notes' => 'Package received at origin facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-7 days'))
        ],
        // First transit point - Philadelphia
        [
            'location' => 'Philadelphia Transit Hub, USA',
            'status' => 'in_transit',
            'lat' => 39.9526,
            'lng' => -75.1652,
            'notes' => 'Package in transit through Philadelphia',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-6 days'))
        ],
        // Second transit point - Pittsburgh
        [
            'location' => 'Pittsburgh Sorting Facility, USA',
            'status' => 'in_transit',
            'lat' => 40.4406,
            'lng' => -79.9959,
            'notes' => 'Package processed at Pittsburgh sorting facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-5 days'))
        ],
        // Third transit point - Columbus
        [
            'location' => 'Columbus Transit Hub, USA',
            'status' => 'in_transit',
            'lat' => 39.9612,
            'lng' => -82.9988,
            'notes' => 'Package in transit through Columbus',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-4 days'))
        ],
        // Fourth transit point - Indianapolis
        [
            'location' => 'Indianapolis Distribution Center, USA',
            'status' => 'in_transit',
            'lat' => 39.7684,
            'lng' => -86.1581,
            'notes' => 'Package processed at Indianapolis distribution center',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-3 days'))
        ],
        // Fifth transit point - St. Louis
        [
            'location' => 'St. Louis Transit Hub, USA',
            'status' => 'in_transit',
            'lat' => 38.6270,
            'lng' => -90.1994,
            'notes' => 'Package in transit through St. Louis',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-2 days 12 hours'))
        ],
        // Sixth transit point - Kansas City
        [
            'location' => 'Kansas City Sorting Facility, USA',
            'status' => 'in_transit',
            'lat' => 39.0997,
            'lng' => -94.5786,
            'notes' => 'Package processed at Kansas City sorting facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-2 days'))
        ],
        // Seventh transit point - Denver
        [
            'location' => 'Denver Distribution Center, USA',
            'status' => 'in_transit',
            'lat' => 39.7392,
            'lng' => -104.9903,
            'notes' => 'Package processed at Denver distribution center',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-1 day 12 hours'))
        ],
        // Eighth transit point - Las Vegas
        [
            'location' => 'Las Vegas Transit Hub, USA',
            'status' => 'in_transit',
            'lat' => 36.1699,
            'lng' => -115.1398,
            'notes' => 'Package in transit through Las Vegas',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ],
        // Current location - Barstow
        [
            'location' => 'Barstow Transit Center, USA',
            'status' => 'in_transit',
            'lat' => 34.8958,
            'lng' => -117.0173,
            'notes' => 'Package in transit to final destination',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-12 hours'))
        ]
    ];
    
    // Insert tracking updates
    $insertStmt = $conn->prepare("
        INSERT INTO tracking_updates 
        (shipment_id, location, status, latitude, longitude, notes, timestamp)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($trackingPoints as $point) {
        $insertStmt->execute([
            $shipmentId,
            $point['location'],
            $point['status'],
            $point['lat'],
            $point['lng'],
            $point['notes'],
            $point['timestamp']
        ]);
        
        echo "<p>Added tracking point: {$point['location']} ({$point['status']})</p>";
    }
    
    echo "<h2>Realistic Route Data Added Successfully</h2>";
    echo "<p>Use tracking number <strong>ROUTE123</strong> to test the tracking map with a realistic route.</p>";
    echo "<p><a href='tracking/index.php?tracking_number=ROUTE123'>View Tracking Map</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
