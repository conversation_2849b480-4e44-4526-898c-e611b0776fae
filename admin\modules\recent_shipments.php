<?php
/**
 * Recent Shipments Module
 * 
 * Displays a table of recent shipments
 */

// If recent shipments are not passed, fetch them
if (!isset($recentShipments)) {
    // Get recent shipments
    $db->query("SELECT * FROM shipments ORDER BY created_at DESC LIMIT 5");
    $recentShipments = $db->resultSet();

    // If query failed, initialize with empty array
    if (!$recentShipments) {
        $recentShipments = [];
    }
}
?>

<!-- Recent Shipments Section -->
<div class="recent-shipments">
    <div class="section-header">
        <h2 class="section-title">Recent Shipments</h2>
        <a href="manage-shipments.php" class="view-all">View All</a>
    </div>

    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>Tracking #</th>
                    <th>Customer</th>
                    <th>Origin</th>
                    <th>Destination</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php if(empty($recentShipments)): ?>
                    <tr>
                        <td colspan="7" class="no-data">No shipments found</td>
                    </tr>
                <?php else: ?>
                    <?php foreach($recentShipments as $shipment): ?>
                        <tr>
                            <td><?php echo $shipment['tracking_number']; ?></td>
                            <td><?php echo $shipment['customer_name']; ?></td>
                            <td><?php echo $shipment['origin']; ?></td>
                            <td><?php echo $shipment['destination']; ?></td>
                            <td><span class="status-badge <?php echo getStatusClass($shipment['status']); ?>"><?php echo ucfirst($shipment['status']); ?></span></td>
                            <td><?php echo formatDate($shipment['created_at']); ?></td>
                            <td class="actions">
                                <a href="shipment-details.php?id=<?php echo $shipment['id']; ?>" class="action-btn view-btn" title="View Details"><i class="fas fa-eye"></i></a>
                                <a href="edit-shipment.php?id=<?php echo $shipment['id']; ?>" class="action-btn edit-btn" title="Edit Shipment"><i class="fas fa-edit"></i></a>
                                <a href="add-update.php?id=<?php echo $shipment['id']; ?>" class="action-btn update-btn" title="Add Update"><i class="fas fa-plus-circle"></i></a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
