<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if(!isLoggedIn()) {
    setMessage('Please login to access the dashboard', 'error');
    redirect('../index.php');
}

if(!isAdmin()) {
    setMessage('You do not have permission to access system settings', 'error');
    redirect('index.php');
}

// Initialize settings array
$settings = [];

// Check if settings table exists
try {
    $tableCheck = $conn->query("SHOW TABLES LIKE 'system_settings'");
    $settingsTable = $tableCheck->fetchAll(PDO::FETCH_ASSOC);

    if (empty($settingsTable)) {
        // Create settings table
        $conn->exec("CREATE TABLE IF NOT EXISTS system_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_key VARCHAR(50) NOT NULL UNIQUE,
            setting_value TEXT,
            setting_group VARCHAR(50) NOT NULL DEFAULT 'general',
            setting_type VARCHAR(20) NOT NULL DEFAULT 'text',
            setting_label VARCHAR(100) NOT NULL,
            setting_description TEXT,
            is_public BOOLEAN NOT NULL DEFAULT 0,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NULL
        )");

        // Insert default settings
        $defaultSettings = [
            // General Settings
            [
                'setting_key' => 'site_name',
                'setting_value' => 'TransLogix Tracking System',
                'setting_group' => 'general',
                'setting_type' => 'text',
                'setting_label' => 'Site Name',
                'setting_description' => 'The name of your tracking system',
                'is_public' => 1
            ],
            [
                'setting_key' => 'company_name',
                'setting_value' => 'TransLogix',
                'setting_group' => 'general',
                'setting_type' => 'text',
                'setting_label' => 'Company Name',
                'setting_description' => 'Your company name',
                'is_public' => 1
            ],
            [
                'setting_key' => 'contact_email',
                'setting_value' => '<EMAIL>',
                'setting_group' => 'general',
                'setting_type' => 'email',
                'setting_label' => 'Contact Email',
                'setting_description' => 'Primary contact email address',
                'is_public' => 1
            ],
            [
                'setting_key' => 'contact_phone',
                'setting_value' => '+****************',
                'setting_group' => 'general',
                'setting_type' => 'text',
                'setting_label' => 'Contact Phone',
                'setting_description' => 'Primary contact phone number',
                'is_public' => 1
            ],

            // Notification Settings
            [
                'setting_key' => 'enable_email_notifications',
                'setting_value' => '1',
                'setting_group' => 'notifications',
                'setting_type' => 'boolean',
                'setting_label' => 'Enable Email Notifications',
                'setting_description' => 'Send email notifications for shipment updates',
                'is_public' => 0
            ],
            [
                'setting_key' => 'notification_sender_email',
                'setting_value' => '<EMAIL>',
                'setting_group' => 'notifications',
                'setting_type' => 'email',
                'setting_label' => 'Notification Sender Email',
                'setting_description' => 'Email address used to send notifications',
                'is_public' => 0
            ],

            // Shipment Settings
            [
                'setting_key' => 'default_estimated_delivery_days',
                'setting_value' => '7',
                'setting_group' => 'shipments',
                'setting_type' => 'number',
                'setting_label' => 'Default Estimated Delivery Days',
                'setting_description' => 'Default number of days for estimated delivery',
                'is_public' => 0
            ],
            [
                'setting_key' => 'tracking_number_prefix',
                'setting_value' => 'TL',
                'setting_group' => 'shipments',
                'setting_type' => 'text',
                'setting_label' => 'Tracking Number Prefix',
                'setting_description' => 'Prefix for automatically generated tracking numbers',
                'is_public' => 0
            ],

            // Appearance Settings
            [
                'setting_key' => 'primary_color',
                'setting_value' => '#4a6cf7',
                'setting_group' => 'appearance',
                'setting_type' => 'color',
                'setting_label' => 'Primary Color',
                'setting_description' => 'Primary color for the website',
                'is_public' => 1
            ],
            [
                'setting_key' => 'secondary_color',
                'setting_value' => '#6c757d',
                'setting_group' => 'appearance',
                'setting_type' => 'color',
                'setting_label' => 'Secondary Color',
                'setting_description' => 'Secondary color for the website',
                'is_public' => 1
            ],
            [
                'setting_key' => 'enable_dark_mode',
                'setting_value' => '1',
                'setting_group' => 'appearance',
                'setting_type' => 'boolean',
                'setting_label' => 'Enable Dark Mode Option',
                'setting_description' => 'Allow users to switch to dark mode',
                'is_public' => 1
            ],

            // System Settings
            [
                'setting_key' => 'maintenance_mode',
                'setting_value' => '0',
                'setting_group' => 'system',
                'setting_type' => 'boolean',
                'setting_label' => 'Maintenance Mode',
                'setting_description' => 'Put the site in maintenance mode',
                'is_public' => 0
            ],
            [
                'setting_key' => 'maintenance_message',
                'setting_value' => 'We are currently performing scheduled maintenance. Please check back soon.',
                'setting_group' => 'system',
                'setting_type' => 'textarea',
                'setting_label' => 'Maintenance Message',
                'setting_description' => 'Message to display during maintenance mode',
                'is_public' => 0
            ],

            // Email Notification Settings
            [
                'setting_key' => 'enable_email_notifications',
                'setting_value' => '1',
                'setting_group' => 'notifications',
                'setting_type' => 'boolean',
                'setting_label' => 'Enable Email Notifications',
                'setting_description' => 'Send email notifications for shipment updates and other events',
                'is_public' => 0
            ],
            [
                'setting_key' => 'notification_sender_email',
                'setting_value' => '<EMAIL>',
                'setting_group' => 'notifications',
                'setting_type' => 'email',
                'setting_label' => 'Notification Sender Email',
                'setting_description' => 'Email address used as the sender for all notifications',
                'is_public' => 0
            ],
            [
                'setting_key' => 'admin_email',
                'setting_value' => '<EMAIL>',
                'setting_group' => 'notifications',
                'setting_type' => 'email',
                'setting_label' => 'Admin Email',
                'setting_description' => 'Email address for receiving admin notifications',
                'is_public' => 0
            ]
        ];

        $insertStmt = $conn->prepare("INSERT INTO system_settings
            (setting_key, setting_value, setting_group, setting_type, setting_label, setting_description, is_public)
            VALUES (?, ?, ?, ?, ?, ?, ?)");

        foreach ($defaultSettings as $setting) {
            $insertStmt->execute([
                $setting['setting_key'],
                $setting['setting_value'],
                $setting['setting_group'],
                $setting['setting_type'],
                $setting['setting_label'],
                $setting['setting_description'],
                $setting['is_public']
            ]);
        }
    }

    // Get all settings except geocoding settings
    $settingsQuery = $conn->query("SELECT * FROM system_settings WHERE setting_group != 'geocoding' ORDER BY setting_group, id");
    $allSettings = $settingsQuery->fetchAll(PDO::FETCH_ASSOC);

    // Organize settings by group
    foreach ($allSettings as $setting) {
        $settings[$setting['setting_group']][] = $setting;
    }

} catch (PDOException $e) {
    setMessage('Database error: ' . $e->getMessage(), 'error');
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_settings'])) {
    try {
        // Start transaction
        $conn->beginTransaction();

        // Update settings
        $updateStmt = $conn->prepare("UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = ?");

        foreach ($_POST['settings'] as $key => $value) {
            // Sanitize input - using htmlspecialchars instead of deprecated FILTER_SANITIZE_STRING
            $key = htmlspecialchars($key, ENT_QUOTES, 'UTF-8');

            // Handle boolean values from checkboxes
            if (isset($_POST['setting_types'][$key]) && $_POST['setting_types'][$key] === 'boolean') {
                $value = isset($value) ? '1' : '0';
            }

            $updateStmt->execute([$value, $key]);
        }

        // Commit transaction
        $conn->commit();

        $_SESSION['message'] = 'Settings updated successfully';
        $_SESSION['message_type'] = 'success';

        // Use JavaScript redirect instead of header() to avoid "headers already sent" warning
        echo "<script>window.location.href = 'settings.php';</script>";
        exit;

    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        setMessage('Error updating settings: ' . $e->getMessage(), 'error');
    }
}

// Include header
include_once '../includes/header.php';
?>

<!-- Admin Settings Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">
            <h1>System Settings</h1>
            <p>Configure your tracking system settings</p>
            <div class="admin-actions">
                <a href="index.php" class="btn secondary-btn">Back to Dashboard</a>
                <a href="geocoding-settings.php" class="btn primary-btn">Geocoding Settings</a>
            </div>
        </div>
    </div>
</section>

<!-- Settings Section -->
<section class="settings-section">
    <div class="container">
        <form method="POST" action="settings.php" class="settings-form">
            <div class="settings-tabs">
                <div class="tabs-navigation">
                    <?php
                    $firstGroup = true;
                    foreach ($settings as $group => $groupSettings):
                    ?>
                        <button type="button" class="tab-button <?php echo $firstGroup ? 'active' : ''; ?>" data-tab="<?php echo $group; ?>">
                            <?php echo ucfirst($group); ?>
                        </button>
                    <?php
                    $firstGroup = false;
                    endforeach;
                    ?>
                </div>

                <div class="tabs-content">
                    <?php
                    $firstGroup = true;
                    foreach ($settings as $group => $groupSettings):
                    ?>
                        <div class="tab-pane <?php echo $firstGroup ? 'active' : ''; ?>" id="<?php echo $group; ?>">
                            <h2><?php echo ucfirst($group); ?> Settings</h2>

                            <?php foreach ($groupSettings as $setting): ?>
                                <div class="form-group">
                                    <label for="<?php echo $setting['setting_key']; ?>"><?php echo $setting['setting_label']; ?></label>

                                    <?php if ($setting['setting_type'] === 'textarea'): ?>
                                        <textarea
                                            name="settings[<?php echo $setting['setting_key']; ?>]"
                                            id="<?php echo $setting['setting_key']; ?>"
                                            class="form-control"
                                            rows="3"
                                        ><?php echo htmlspecialchars($setting['setting_value']); ?></textarea>

                                    <?php elseif ($setting['setting_type'] === 'boolean'): ?>
                                        <div class="toggle-switch">
                                            <input
                                                type="checkbox"
                                                name="settings[<?php echo $setting['setting_key']; ?>]"
                                                id="<?php echo $setting['setting_key']; ?>"
                                                value="1"
                                                <?php echo $setting['setting_value'] == '1' ? 'checked' : ''; ?>
                                            >
                                            <label for="<?php echo $setting['setting_key']; ?>" class="toggle-label"></label>
                                            <input type="hidden" name="setting_types[<?php echo htmlspecialchars($setting['setting_key'], ENT_QUOTES, 'UTF-8'); ?>]" value="boolean">
                                        </div>

                                    <?php elseif ($setting['setting_type'] === 'color'): ?>
                                        <input
                                            type="color"
                                            name="settings[<?php echo $setting['setting_key']; ?>]"
                                            id="<?php echo $setting['setting_key']; ?>"
                                            value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                            class="form-control color-picker"
                                        >

                                    <?php elseif ($setting['setting_type'] === 'number'): ?>
                                        <input
                                            type="number"
                                            name="settings[<?php echo $setting['setting_key']; ?>]"
                                            id="<?php echo $setting['setting_key']; ?>"
                                            value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                            class="form-control"
                                        >

                                    <?php else: ?>
                                        <input
                                            type="<?php echo $setting['setting_type']; ?>"
                                            name="settings[<?php echo $setting['setting_key']; ?>]"
                                            id="<?php echo $setting['setting_key']; ?>"
                                            value="<?php echo htmlspecialchars($setting['setting_value']); ?>"
                                            class="form-control"
                                        >
                                    <?php endif; ?>

                                    <?php if (!empty($setting['setting_description'])): ?>
                                        <p class="form-text"><?php echo $setting['setting_description']; ?></p>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php
                    $firstGroup = false;
                    endforeach;
                    ?>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" name="save_settings" class="btn primary-btn">Save Settings</button>
                <a href="index.php" class="btn secondary-btn">Cancel</a>
            </div>
        </form>
    </div>
</section>

<!-- Settings JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            button.classList.add('active');
            const tabId = button.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Form validation
    const settingsForm = document.querySelector('.settings-form');

    settingsForm.addEventListener('submit', function(event) {
        let hasErrors = false;

        // Validate email fields
        const emailInputs = document.querySelectorAll('input[type="email"]');
        emailInputs.forEach(input => {
            if (input.value && !isValidEmail(input.value)) {
                showError(input, 'Please enter a valid email address');
                hasErrors = true;
            } else {
                clearError(input);
            }
        });

        // Prevent form submission if there are errors
        if (hasErrors) {
            event.preventDefault();
        }
    });

    // Email validation function
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Show error message
    function showError(input, message) {
        const formGroup = input.closest('.form-group');
        let errorElement = formGroup.querySelector('.error-message');

        if (!errorElement) {
            errorElement = document.createElement('p');
            errorElement.className = 'error-message';
            formGroup.appendChild(errorElement);
        }

        errorElement.textContent = message;
        input.classList.add('error');
    }

    // Clear error message
    function clearError(input) {
        const formGroup = input.closest('.form-group');
        const errorElement = formGroup.querySelector('.error-message');

        if (errorElement) {
            errorElement.remove();
        }

        input.classList.remove('error');
    }
});
</script>

<?php
// Add CSS for settings page
echo "<style>";
readfile(__DIR__ . '/../assets/css/admin-mobile.css');
readfile(__DIR__ . '/../assets/css/sidebar-fix.css'); // Add sidebar fix CSS
echo "</style>";

echo "<script>";
readfile(__DIR__ . '/../assets/js/admin-mobile.js');
echo "</script>";

// Add additional script to ensure sidebar close button works
echo "<script>
// Ensure sidebar close button is properly initialized
document.addEventListener('DOMContentLoaded', function() {
    // Check if sidebar close button exists, if not create it
    const dashboardSidebar = document.querySelector('.dashboard-sidebar');
    if (dashboardSidebar && !dashboardSidebar.querySelector('.sidebar-close')) {
        const closeButton = document.createElement('button');
        closeButton.className = 'sidebar-close';
        closeButton.innerHTML = '<i class=\"fas fa-times\"></i>';
        closeButton.setAttribute('aria-label', 'Close Sidebar');
        closeButton.style.cursor = 'pointer';
        dashboardSidebar.prepend(closeButton);

        // Add event listener to close button
        closeButton.addEventListener('click', function() {
            dashboardSidebar.classList.remove('active');
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.classList.remove('active');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            if (sidebarToggle) sidebarToggle.classList.remove('active');
            document.body.style.overflow = '';
        });

        console.log('Sidebar close button initialized');
    }
});
</script>";

// Add settings-specific CSS
?>
<style>
/* Settings Page Styles */
.settings-section {
    padding: 40px 0;
}

.settings-form {
    background-color: var(--bg-secondary);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.settings-tabs {
    display: flex;
    flex-direction: column;
}

.tabs-navigation {
    display: flex;
    overflow-x: auto;
    background-color: var(--bg-color);
    border-bottom: 1px solid var(--border-color);
}

.tab-button {
    padding: 15px 20px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--text-color);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.tab-button.active {
    border-bottom-color: var(--primary-color);
    color: var(--primary-color);
}

.tabs-content {
    padding: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane h2 {
    margin-top: 0;
    margin-bottom: 20px;
    font-size: 1.5rem;
    color: var(--text-color);
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    background-color: var(--input-bg);
    color: var(--text-color);
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-text {
    margin-top: 5px;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.form-actions {
    padding: 20px 30px;
    background-color: var(--bg-color);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-label {
    display: block;
    width: 50px;
    height: 26px;
    background-color: #ccc;
    border-radius: 13px;
    cursor: pointer;
    position: relative;
    transition: background-color 0.3s ease;
}

.toggle-label:before {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    top: 3px;
    left: 3px;
    transition: transform 0.3s ease;
}

.toggle-switch input:checked + .toggle-label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + .toggle-label:before {
    transform: translateX(24px);
}

/* Color Picker */
.color-picker {
    height: 40px;
    padding: 5px;
    width: 100px;
}

/* Error Styles */
.error-message {
    color: #dc3545;
    font-size: 0.85rem;
    margin-top: 5px;
}

.form-control.error {
    border-color: #dc3545;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .tabs-navigation {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .tabs-content {
        padding: 20px;
    }

    .form-actions {
        flex-direction: column;
    }

    .form-actions .btn {
        width: 100%;
    }
}
</style>

<?php include_once '../includes/footer.php'; ?>
