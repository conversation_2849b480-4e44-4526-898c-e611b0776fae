<?php
/**
 * Performance Metrics Module
 * 
 * Displays performance metrics in the sidebar
 */

// If performance metrics are not passed, fetch them
if (!isset($performanceMetrics)) {
    // Calculate performance metrics
    $db->query("SELECT
                (SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delivery_rate,
                (SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delay_rate,
                AVG(DATEDIFF(delivered_at, created_at)) as avg_delivery_time,
                (SUM(CASE WHEN status = 'delivered' AND DATEDIFF(delivered_at, created_at) <= 3 THEN 1 ELSE 0 END) /
                 NULLIF(SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END), 0)) * 100 as on_time_rate,
                MAX(DATEDIFF(delivered_at, created_at)) as max_delivery_time,
                MIN(DATEDIFF(delivered_at, created_at)) as min_delivery_time
                FROM shipments
                WHERE delivered_at IS NOT NULL OR status = 'delivered'");
    $performanceMetrics = $db->single();

    // If query failed, initialize with default values
    if (!$performanceMetrics) {
        $performanceMetrics = [
            'delivery_rate' => 0,
            'delay_rate' => 0,
            'avg_delivery_time' => 0,
            'on_time_rate' => 0,
            'max_delivery_time' => 0,
            'min_delivery_time' => 0
        ];
    }
}

// If current month performance metrics are not passed, fetch them
if (!isset($currentMonthPerformance)) {
    // Calculate performance metrics for current month
    $currentMonth = date('Y-m');
    $db->query("SELECT
                (SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delivery_rate,
                (SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delay_rate,
                AVG(DATEDIFF(delivered_at, created_at)) as avg_delivery_time,
                (SUM(CASE WHEN status = 'delivered' AND DATEDIFF(delivered_at, created_at) <= 3 THEN 1 ELSE 0 END) /
                 NULLIF(SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END), 0)) * 100 as on_time_rate
                FROM shipments
                WHERE (delivered_at IS NOT NULL OR status = 'delivered')
                AND DATE_FORMAT(created_at, '%Y-%m') = :current_month");
    $db->bind(':current_month', $currentMonth);
    $currentMonthPerformance = $db->single();

    // If query failed, initialize with default values
    if (!$currentMonthPerformance) {
        $currentMonthPerformance = [
            'delivery_rate' => 0,
            'delay_rate' => 0,
            'avg_delivery_time' => 0,
            'on_time_rate' => 0
        ];
    }
}

// If busiest routes are not passed, fetch them
if (!isset($busiestRoutes)) {
    // Get busiest routes
    $db->query("SELECT
                CONCAT(origin, ' to ', destination) as route,
                COUNT(*) as shipment_count,
                AVG(DATEDIFF(delivered_at, created_at)) as avg_delivery_time
                FROM shipments
                WHERE status = 'delivered'
                GROUP BY origin, destination
                ORDER BY shipment_count DESC
                LIMIT 3");
    $busiestRoutes = $db->resultSet();

    // If query failed, initialize with empty array
    if (!$busiestRoutes) {
        $busiestRoutes = [];
    }
}
?>

<!-- Performance Metrics -->
<div class="performance-metrics">
    <div class="metrics-header">
        <h2 class="section-title">Performance Metrics</h2>
        <div class="header-actions">
            <button class="refresh-stats" title="Refresh Metrics" data-module="performance_metrics"><i class="fas fa-sync-alt"></i></button>
        </div>
    </div>

    <!-- Overall Performance -->
    <div class="metrics-section">
        <h3 class="metrics-subheader">Overall Performance</h3>

        <div class="metric-item">
            <div class="metric-label">
                <div class="metric-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="metric-name">Delivery Success Rate</div>
            </div>
            <?php
            $deliveryRate = round($performanceMetrics['delivery_rate'] ?? 0, 1);
            ?>
            <div class="metric-value"><?php echo $deliveryRate; ?>%</div>
            <div class="progress-container">
                <div class="progress-bar" style="width: <?php echo $deliveryRate; ?>%"></div>
            </div>
        </div>

        <div class="metric-item">
            <div class="metric-label">
                <div class="metric-icon">
                    <i class="fas fa-truck"></i>
                </div>
                <div class="metric-name">On-Time Delivery Rate</div>
            </div>
            <?php
            $onTimeRate = round($performanceMetrics['on_time_rate'] ?? 0, 1);
            ?>
            <div class="metric-value"><?php echo $onTimeRate; ?>%</div>
            <div class="progress-container">
                <div class="progress-bar" style="width: <?php echo $onTimeRate; ?>%"></div>
            </div>
        </div>

        <div class="metric-item">
            <div class="metric-label">
                <div class="metric-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="metric-name">Delay Rate</div>
            </div>
            <?php
            $delayRate = round($performanceMetrics['delay_rate'] ?? 0, 1);
            ?>
            <div class="metric-value"><?php echo $delayRate; ?>%</div>
            <div class="progress-container">
                <div class="progress-bar" style="width: <?php echo $delayRate; ?>%"></div>
            </div>
        </div>

        <div class="metric-item">
            <div class="metric-label">
                <div class="metric-icon">
                    <i class="fas fa-route"></i>
                </div>
                <div class="metric-name">Avg. Delivery Time</div>
            </div>
            <?php
            $avgDeliveryTime = round($performanceMetrics['avg_delivery_time'] ?? 0, 1);
            ?>
            <div class="metric-value"><?php echo $avgDeliveryTime; ?> days</div>
        </div>
    </div>

    <!-- Current Month Performance -->
    <div class="metrics-section">
        <h3 class="metrics-subheader">Current Month Performance</h3>

        <div class="metrics-summary">
            <div class="summary-row">
                <div class="summary-label">Delivery Success Rate</div>
                <div class="summary-value">
                    <?php echo round($currentMonthPerformance['delivery_rate'] ?? 0, 1); ?>%
                    <?php
                    $rateChange = ($currentMonthPerformance['delivery_rate'] ?? 0) - ($performanceMetrics['delivery_rate'] ?? 0);
                    if (abs($rateChange) >= 0.1):
                    ?>
                    <span class="change-indicator <?php echo $rateChange >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo round(abs($rateChange), 1); ?>% <?php echo $rateChange >= 0 ? 'better' : 'worse'; ?> than average
                    </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="summary-row">
                <div class="summary-label">On-Time Delivery Rate</div>
                <div class="summary-value">
                    <?php echo round($currentMonthPerformance['on_time_rate'] ?? 0, 1); ?>%
                    <?php
                    $rateChange = ($currentMonthPerformance['on_time_rate'] ?? 0) - ($performanceMetrics['on_time_rate'] ?? 0);
                    if (abs($rateChange) >= 0.1):
                    ?>
                    <span class="change-indicator <?php echo $rateChange >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo round(abs($rateChange), 1); ?>% <?php echo $rateChange >= 0 ? 'better' : 'worse'; ?> than average
                    </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="summary-row">
                <div class="summary-label">Delay Rate</div>
                <div class="summary-value">
                    <?php echo round($currentMonthPerformance['delay_rate'] ?? 0, 1); ?>%
                    <?php
                    $rateChange = ($performanceMetrics['delay_rate'] ?? 0) - ($currentMonthPerformance['delay_rate'] ?? 0);
                    if (abs($rateChange) >= 0.1):
                    ?>
                    <span class="change-indicator <?php echo $rateChange >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo round(abs($rateChange), 1); ?>% <?php echo $rateChange >= 0 ? 'better' : 'worse'; ?> than average
                    </span>
                    <?php endif; ?>
                </div>
            </div>

            <div class="summary-row">
                <div class="summary-label">Avg. Delivery Time</div>
                <div class="summary-value">
                    <?php echo round($currentMonthPerformance['avg_delivery_time'] ?? 0, 1); ?> days
                    <?php
                    $timeChange = ($performanceMetrics['avg_delivery_time'] ?? 0) - ($currentMonthPerformance['avg_delivery_time'] ?? 0);
                    if (abs($timeChange) >= 0.1):
                    ?>
                    <span class="change-indicator <?php echo $timeChange >= 0 ? 'positive' : 'negative'; ?>">
                        <?php echo round(abs($timeChange), 1); ?> days <?php echo $timeChange >= 0 ? 'faster' : 'slower'; ?> than average
                    </span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Busiest Routes -->
    <div class="metrics-section">
        <h3 class="metrics-subheader">Busiest Routes</h3>

        <div class="routes-list">
            <?php if(empty($busiestRoutes)): ?>
                <div class="no-routes">No route data available</div>
            <?php else: ?>
                <?php foreach($busiestRoutes as $index => $route): ?>
                    <div class="route-item">
                        <div class="route-rank">#<?php echo $index + 1; ?></div>
                        <div class="route-details">
                            <div class="route-name"><?php echo $route['route']; ?></div>
                            <div class="route-stats">
                                <span class="route-count"><?php echo $route['shipment_count']; ?> shipments</span>
                                <span class="route-time"><?php echo round($route['avg_delivery_time'], 1); ?> days avg. delivery time</span>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</div>
