/**
 * Admin Form Tabs - Handles tab switching in admin forms
 */
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    function initTabs() {
        const tabButtons = document.querySelectorAll('.tab-btn');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabId = this.getAttribute('data-tab');
                const tabContent = document.getElementById(tabId);
                const tabContainer = this.closest('form');

                // Remove active class from all tabs and contents
                tabContainer.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });

                tabContainer.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // Add active class to current tab and content
                this.classList.add('active');
                if (tabContent) {
                    tabContent.classList.add('active');
                }
            });
        });
    }

    // Initialize tabs
    initTabs();

    // Handle edit shipment modal
    const editButtons = document.querySelectorAll('.edit-shipment-btn');

    editButtons.forEach(button => {
        button.addEventListener('click', function() {
            const shipmentId = this.getAttribute('data-id');

            // Fetch shipment data
            fetch(`get-shipment.php?id=${shipmentId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const shipment = data.shipment;

                        // Set basic info
                        document.getElementById('edit_shipment_id').value = shipment.id;
                        document.getElementById('edit_tracking_number').value = shipment.tracking_number;
                        document.getElementById('edit_customer_name').value = shipment.customer_name;
                        document.getElementById('edit_origin').value = shipment.origin;
                        document.getElementById('edit_destination').value = shipment.destination;
                        document.getElementById('edit_status').value = shipment.status;
                        document.getElementById('edit_estimated_delivery').value = shipment.estimated_delivery;

                        // Set shopper info
                        if (document.getElementById('edit_shopper_name')) {
                            document.getElementById('edit_shopper_name').value = shipment.shopper_name || '';
                            document.getElementById('edit_shopper_email').value = shipment.shopper_email || '';
                            document.getElementById('edit_shopper_phone').value = shipment.shopper_phone || '';
                            document.getElementById('edit_shopper_address').value = shipment.shopper_address || '';
                        }

                        // Set receiver info
                        if (document.getElementById('edit_receiver_name')) {
                            document.getElementById('edit_receiver_name').value = shipment.receiver_name || '';
                            document.getElementById('edit_receiver_email').value = shipment.receiver_email || '';
                            document.getElementById('edit_receiver_phone').value = shipment.receiver_phone || '';
                            document.getElementById('edit_receiver_address').value = shipment.receiver_address || '';
                        }

                        // Set package info
                        if (document.getElementById('edit_package_weight')) {
                            document.getElementById('edit_package_weight').value = shipment.package_weight || '';
                            document.getElementById('edit_package_dimensions').value = shipment.package_dimensions || '';
                            document.getElementById('edit_shipping_service').value = shipment.shipping_service || '';
                            document.getElementById('edit_shipping_cost').value = shipment.shipping_cost || '';
                        }

                        // Display current package picture if exists
                        const currentPicture = document.getElementById('current_package_picture');
                        if (currentPicture) {
                            if (shipment.package_picture) {
                                currentPicture.innerHTML = `
                                    <img src="../uploads/packages/${shipment.package_picture}" alt="Current Package Picture" class="img-thumbnail">
                                    <p class="form-text">Current package picture</p>
                                `;
                            } else {
                                currentPicture.innerHTML = `
                                    <img src="../assets/images/defaults/package-placeholder.svg" alt="Default Package" class="img-thumbnail default-package-image">
                                    <p class="form-text">No custom package picture available</p>
                                `;
                            }
                        }

                        // Show the modal
                        document.getElementById('editShipmentModal').classList.add('show');
                    } else {
                        console.error('Error fetching shipment data:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        });
    });

    // Handle form submission with tabs
    const shipmentForms = document.querySelectorAll('form[action="manage-shipments.php"]');

    shipmentForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // Validate required fields
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');

                    // Find the tab containing this field and activate it
                    const tabContent = field.closest('.tab-content');
                    if (tabContent) {
                        const tabId = tabContent.id;
                        const tabBtn = form.querySelector(`.tab-btn[data-tab="${tabId}"]`);
                        if (tabBtn) {
                            tabBtn.click();
                        }
                    }
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Please fill in all required fields');
            }
        });
    });

    // Re-initialize tabs when modals are opened
    const modalTriggers = document.querySelectorAll('[data-toggle="modal"]');

    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            setTimeout(initTabs, 100);
        });
    });
});
