<?php
require_once 'config.php';
require_once 'db.php';

/**
 * Helper Functions
 */

/**
 * Redirect to another page
 * @param string $page The URL to redirect to
 * @return void
 */
function redirect($page) {
    // Check if headers have already been sent
    if (!headers_sent()) {
        // Use header redirect if headers haven't been sent
        header("Location: $page");
        exit;
    } else {
        // Use JavaScript redirect if headers have been sent
        echo "<script>window.location.href = '$page';</script>";
        exit;
    }
}

/**
 * Set a notification to be displayed on the next page load
 * @param string $message The message to display
 * @param string $type The message type (success, error, info, warning)
 * @param int $duration How long to display the notification in milliseconds (0 for no auto-close)
 */
function setNotification($message, $type = 'info', $duration = 5000) {
    if (!isset($_SESSION['notifications'])) {
        $_SESSION['notifications'] = [];
    }

    $_SESSION['notifications'][] = [
        'message' => $message,
        'type' => $type,
        'duration' => $duration
    ];
}

/**
 * Set a success notification
 * @param string $message The message to display
 * @param int $duration How long to display the notification in milliseconds
 */
function setSuccessNotification($message, $duration = 5000) {
    setNotification($message, 'success', $duration);
}

/**
 * Set an error notification
 * @param string $message The message to display
 * @param int $duration How long to display the notification in milliseconds
 */
function setErrorNotification($message, $duration = 5000) {
    setNotification($message, 'error', $duration);
}

/**
 * Set a warning notification
 * @param string $message The message to display
 * @param int $duration How long to display the notification in milliseconds
 */
function setWarningNotification($message, $duration = 5000) {
    setNotification($message, 'warning', $duration);
}

/**
 * Set an info notification
 * @param string $message The message to display
 * @param int $duration How long to display the notification in milliseconds
 */
function setInfoNotification($message, $duration = 5000) {
    setNotification($message, 'info', $duration);
}

/**
 * Get all pending notifications and clear them from the session
 * @return array Array of notification objects
 */
function getNotifications() {
    $notifications = isset($_SESSION['notifications']) ? $_SESSION['notifications'] : [];
    unset($_SESSION['notifications']);
    return $notifications;
}

/**
 * Display notifications in the page
 * This function outputs HTML that will be processed by the JavaScript notification system
 */
function displayNotifications() {
    $notifications = getNotifications();

    if (!empty($notifications)) {
        echo '<div id="flash-messages">';
        foreach ($notifications as $notification) {
            echo '<div class="flash-message" data-type="' . htmlspecialchars($notification['type']) . '">' .
                 htmlspecialchars($notification['message']) . '</div>';
        }
        echo '</div>';
    }
}

/**
 * Set message for user feedback (legacy function for backward compatibility)
 * @param string $message The message to display
 * @param string $type The message type (success, error, info, warning)
 */
function setMessage($message, $type = 'info') {
    setNotification($message, $type);
}

/**
 * Display session flash messages (legacy function for backward compatibility)
 */
function displayMessage() {
    // For backward compatibility, we'll still show the old-style messages
    // but also convert them to notifications
    if(isset($_SESSION['message'])) {
        $type = isset($_SESSION['message_type']) ? $_SESSION['message_type'] : 'info';
        echo '<div class="message ' . $type . '">' . $_SESSION['message'] . '</div>';

        // Also add to notifications for the new system
        setNotification($_SESSION['message'], $type);

        // Clear the message
        unset($_SESSION['message']);
        unset($_SESSION['message_type']);
    }
}

/**
 * Check if user is logged in
 * @return bool Whether the user is logged in
 */
function isLoggedIn() {
    // First check if user is already logged in via session
    if (isset($_SESSION['user_id']) && !empty($_SESSION['user_id'])) {
        return true;
    }

    // If not logged in, check for remember me cookie
    if (isset($_COOKIE['remember_me'])) {
        return attemptRememberMeLogin();
    }

    return false;
}

/**
 * Attempt to log in user using remember me cookie
 * @return bool Whether the login was successful
 */
function attemptRememberMeLogin() {
    global $conn;

    // Check if the cookie has the correct format
    $cookieValue = $_COOKIE['remember_me'];
    $parts = explode(':', $cookieValue);

    if (count($parts) !== 2) {
        // Invalid cookie format, clear it
        setcookie('remember_me', '', time() - 3600, '/');
        return false;
    }

    list($selector, $validator) = $parts;

    try {
        // Check if remember_tokens table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'remember_tokens'");
        if ($tableCheck->rowCount() == 0) {
            return false; // Table doesn't exist
        }

        // Find the token in the database
        $stmt = $conn->prepare("SELECT rt.*, u.*
                              FROM remember_tokens rt
                              JOIN users u ON rt.user_id = u.id
                              WHERE rt.selector = ? AND rt.expires > NOW()");
        $stmt->execute([$selector]);
        $token = $stmt->fetch();

        if (!$token) {
            // Token not found or expired, clear the cookie
            setcookie('remember_me', '', time() - 3600, '/');
            return false;
        }

        // Verify the validator
        if (password_verify($validator, $token['hashed_validator'])) {
            // Token is valid, log the user in
            $_SESSION['user_id'] = $token['user_id'];
            $_SESSION['username'] = $token['username'];
            $_SESSION['user_role'] = $token['role'];

            // Regenerate the token for security (optional)
            $newValidator = bin2hex(random_bytes(32));
            $hashedValidator = password_hash($newValidator, PASSWORD_DEFAULT);
            $expires = date('Y-m-d H:i:s', time() + 30 * 24 * 60 * 60);

            // Update the token in the database
            $updateStmt = $conn->prepare("UPDATE remember_tokens
                                        SET hashed_validator = ?, expires = ?
                                        WHERE selector = ?");
            $updateStmt->execute([$hashedValidator, $expires, $selector]);

            // Set a new cookie
            $cookieValue = $selector . ':' . $newValidator;
            setcookie(
                'remember_me',
                $cookieValue,
                time() + 30 * 24 * 60 * 60,  // 30 days
                '/',                          // Path
                '',                           // Domain
                false,                        // Secure (set to true in production with HTTPS)
                true                          // HttpOnly
            );

            return true;
        }

        // Invalid validator, clear the cookie
        setcookie('remember_me', '', time() - 3600, '/');
        return false;

    } catch (PDOException $e) {
        error_log("Error in remember me login: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if user is admin
 * @return bool Whether the user is an admin
 */
function isAdmin() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'admin';
}

/**
 * Check if user is staff
 * @return bool Whether the user is staff
 */
function isStaff() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'staff';
}

/**
 * Check if user is customer
 * @return bool Whether the user is a customer
 */
function isCustomer() {
    return isset($_SESSION['user_role']) && $_SESSION['user_role'] == 'customer';
}

/**
 * Check if user has permission to perform an action
 * @param string $permission The permission to check
 * @return bool Whether the user has the permission
 */
function hasPermission($permission) {
    // Admin has all permissions
    if (isAdmin()) {
        return true;
    }

    // Check specific permissions based on user role
    switch ($permission) {
        case 'create_shipment':
            return isAdmin() || isStaff();
        case 'view_shipment':
            return isLoggedIn(); // All logged-in users can view shipments
        case 'update_shipment':
            return isAdmin() || isStaff();
        case 'delete_shipment':
            return isAdmin(); // Only admin can delete shipments
        case 'view_all_shipments':
            return isAdmin() || isStaff();
        case 'add_tracking_update':
            return isAdmin() || isStaff();
        default:
            return false;
    }
}

/**
 * Check if user can create shipments
 * @return bool Whether the user can create shipments
 */
function canCreateShipment() {
    return hasPermission('create_shipment');
}

/**
 * Check if user can view a shipment
 * @param array $shipment The shipment data
 * @return bool Whether the user can view the shipment
 */
function canViewShipment($shipment) {
    // Admin and staff can view all shipments
    if (hasPermission('view_all_shipments')) {
        return true;
    }

    // Customers can only view their own shipments
    if (isCustomer() && isset($shipment['user_id']) && isset($_SESSION['user_id'])) {
        return $shipment['user_id'] == $_SESSION['user_id'];
    }

    return false;
}

/**
 * Check if user can update a shipment
 * @return bool Whether the user can update shipments
 */
function canUpdateShipment() {
    return hasPermission('update_shipment');
}

/**
 * Check if user can add tracking updates
 * @return bool Whether the user can add tracking updates
 */
function canAddTrackingUpdate() {
    return hasPermission('add_tracking_update');
}

/**
 * Log out the user
 * @return void
 */
function logoutUser() {
    global $conn;

    // If there's a remember me cookie, delete the token from the database
    if (isset($_COOKIE['remember_me'])) {
        $cookieValue = $_COOKIE['remember_me'];
        $parts = explode(':', $cookieValue);

        if (count($parts) === 2) {
            $selector = $parts[0];

            try {
                // Check if remember_tokens table exists
                $tableCheck = $conn->query("SHOW TABLES LIKE 'remember_tokens'");
                if ($tableCheck->rowCount() > 0) {
                    // Delete the token
                    $stmt = $conn->prepare("DELETE FROM remember_tokens WHERE selector = ?");
                    $stmt->execute([$selector]);
                }
            } catch (PDOException $e) {
                error_log("Error deleting remember token: " . $e->getMessage());
            }
        }

        // Clear the cookie
        setcookie('remember_me', '', time() - 3600, '/');
    }

    // Clear session variables
    $_SESSION = [];

    // Destroy the session
    if (session_status() === PHP_SESSION_ACTIVE) {
        session_destroy();
    }
}

/**
 * Sanitize input data
 * @param string $data The data to sanitize
 * @return string Sanitized data
 */
function sanitize($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Generate random tracking number
 * @deprecated Use the updated version below
 */
// This function has been moved and updated below

/**
 * Get shipment by tracking number
 */
function getShipmentByTrackingNumber($trackingNumber) {
    global $db;

    $db->query("SELECT * FROM shipments WHERE tracking_number = :tracking_number");
    $db->bind(':tracking_number', $trackingNumber);

    return $db->single();
}

/**
 * Get tracking updates for a shipment
 */
function getTrackingUpdates($shipmentId) {
    global $db;

    $db->query("SELECT * FROM tracking_updates WHERE shipment_id = :shipment_id ORDER BY timestamp DESC");
    $db->bind(':shipment_id', $shipmentId);

    return $db->resultSet();
}

/**
 * Format date
 */
function formatDate($date) {
    return date('F j, Y, g:i a', strtotime($date));
}

// This function has been moved and updated below

/**
 * Get all shipments
 */
function getAllShipments() {
    global $db;

    $db->query("SELECT * FROM shipments ORDER BY created_at DESC");

    return $db->resultSet();
}

/**
 * Get all users
 */
function getAllUsers() {
    global $db;

    $db->query("SELECT * FROM users ORDER BY created_at DESC");

    return $db->resultSet();
}

/**
 * Get user by ID
 */
function getUserById($userId) {
    global $db;

    $db->query("SELECT * FROM users WHERE id = :id");
    $db->bind(':id', $userId);

    return $db->single();
}

/**
 * Get user by username
 */
function getUserByUsername($username) {
    global $db;

    $db->query("SELECT * FROM users WHERE username = :username");
    $db->bind(':username', $username);

    return $db->single();
}

/**
 * Add tracking update
 * @deprecated Use the updated version below
 */
// This function has been moved and updated below

/**
 * Update shipment status
 * @deprecated Use the updated version below
 */
// This function has been moved and updated below

/**
 * Debug logging function
 */
function debugLog($message, $data = null) {
    $logMessage = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        $logMessage .= " - Data: " . print_r($data, true);
    }
    error_log($logMessage);
}

/**
 * Generate navigation menu items
 * @return array Array of navigation items
 */
function getNavigationItems() {
    $current_page = basename($_SERVER['PHP_SELF']);
    $current_dir = dirname($_SERVER['PHP_SELF']);

    $nav_items = [
        [
            'url' => SITE_URL . '/index.php',
            'text' => 'Home',
            'active' => $current_page == 'index.php' && $current_dir == '/'
        ],
        [
            'url' => SITE_URL . '/services.php',
            'text' => 'Services',
            'active' => $current_page == 'services.php'
        ],
        [
            'url' => SITE_URL . '/industries.php',
            'text' => 'Industries',
            'active' => $current_page == 'industries.php'
        ],
        [
            'url' => SITE_URL . '/about.php',
            'text' => 'About Us',
            'active' => $current_page == 'about.php'
        ],
        [
            'url' => SITE_URL . '/contact.php',
            'text' => 'Contact',
            'active' => $current_page == 'contact.php'
        ],
        [
            'url' => SITE_URL . '/tracking/index.php',
            'text' => 'Track',
            'active' => $current_dir == '/tracking'
        ]
    ];

    // Add login/dashboard button based on login status
    if (isLoggedIn()) {
        // Determine dashboard URL based on user role
        $dashboardUrl = SITE_URL . '/admin/index.php';
        if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'customer') {
            $dashboardUrl = SITE_URL . '/user/index.php';
        }

        $nav_items[] = [
            'url' => $dashboardUrl,
            'text' => 'Dashboard',
            'class' => 'login-btn',
            'active' => strpos($current_dir, '/admin') !== false || strpos($current_dir, '/user') !== false
        ];
    } else {
        // Add register link
        $nav_items[] = [
            'url' => SITE_URL . '/register.php',
            'text' => 'Register',
            'class' => 'register-btn',
            'active' => $current_page == 'register.php'
        ];

        // Add login button
        $nav_items[] = [
            'url' => '#',
            'text' => 'Login',
            'class' => 'login-btn',
            'id' => 'login-button',
            'active' => false
        ];
    }

    // Add theme toggle
    $nav_items[] = [
        'type' => 'theme-toggle',
        'icon' => isset($_COOKIE['theme']) && $_COOKIE['theme'] === 'dark' ? 'fa-sun' : 'fa-moon'
    ];

    return $nav_items;
}

/**
 * Format time ago
 * @param string $timestamp The timestamp to format
 * @return string Formatted time ago string
 */
function timeAgo($timestamp) {
    // Check if timestamp is empty or invalid
    if (empty($timestamp)) {
        return 'Unknown time';
    }

    $time = strtotime($timestamp);

    // Check if strtotime returned false (invalid timestamp)
    if ($time === false) {
        return 'Invalid date';
    }

    $now = time();
    $diff = $now - $time;

    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $mins = floor($diff / 60);
        return $mins . ' minute' . ($mins > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 172800) {
        return 'Yesterday';
    } elseif ($diff < 604800) {
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 2592000) {
        $weeks = floor($diff / 604800);
        return $weeks . ' week' . ($weeks > 1 ? 's' : '') . ' ago';
    } else {
        return date('M j, Y', $time);
    }
}

/**
 * Report Functions
 */

/**
 * Get shipment status report
 */
function getShipmentStatusReport($startDate, $endDate) {
    global $db;

    $db->query("SELECT status, COUNT(*) as count FROM shipments
               WHERE created_at BETWEEN :start_date AND :end_date
               GROUP BY status");
    $db->bind(':start_date', $startDate . ' 00:00:00');
    $db->bind(':end_date', $endDate . ' 23:59:59');

    $results = $db->resultSet();
    $report = [];

    foreach($results as $row) {
        $report[$row['status']] = $row['count'];
    }

    return $report;
}

/**
 * Get monthly shipment report
 */
function getMonthlyShipmentReport($startDate, $endDate) {
    global $db;

    $db->query("SELECT DATE_FORMAT(created_at, '%Y-%m') as month, COUNT(*) as count
               FROM shipments
               WHERE created_at BETWEEN :start_date AND :end_date
               GROUP BY DATE_FORMAT(created_at, '%Y-%m')
               ORDER BY month");
    $db->bind(':start_date', $startDate . ' 00:00:00');
    $db->bind(':end_date', $endDate . ' 23:59:59');

    $results = $db->resultSet();
    $report = [];

    foreach($results as $row) {
        $monthName = date('F Y', strtotime($row['month'] . '-01'));
        $report[$monthName] = $row['count'];
    }

    return $report;
}

/**
 * Get top destinations report
 */
function getTopDestinationsReport($startDate, $endDate) {
    global $db;

    $db->query("SELECT destination, COUNT(*) as count
               FROM shipments
               WHERE created_at BETWEEN :start_date AND :end_date
               GROUP BY destination
               ORDER BY count DESC
               LIMIT 10");
    $db->bind(':start_date', $startDate . ' 00:00:00');
    $db->bind(':end_date', $endDate . ' 23:59:59');

    $results = $db->resultSet();
    $report = [];

    foreach($results as $row) {
        $report[$row['destination']] = $row['count'];
    }

    return $report;
}

/**
 * Get delivery time report
 */
function getDeliveryTimeReport($startDate, $endDate) {
    global $db;

    $db->query("SELECT
                  CONCAT(origin, ' to ', destination) as route,
                  AVG(DATEDIFF(delivered_at, created_at)) as avg_days
                FROM shipments
                WHERE
                  created_at BETWEEN :start_date AND :end_date
                  AND status = 'delivered'
                  AND delivered_at IS NOT NULL
                GROUP BY origin, destination
                ORDER BY avg_days DESC");
    $db->bind(':start_date', $startDate . ' 00:00:00');
    $db->bind(':end_date', $endDate . ' 23:59:59');

    $results = $db->resultSet();
    $report = [];

    foreach($results as $row) {
        $report[$row['route']] = round($row['avg_days'], 1);
    }

    return $report;
}

/**
 * Get the CSS class for a shipment status
 * @param string $status The shipment status
 * @return string The CSS class for the status
 */
function getStatusClass($status) {
    // Normalize status (handle both hyphen and underscore formats)
    $status = str_replace('-', '_', $status);

    switch($status) {
        case 'pending':
            return 'status-pending';
        case 'in_transit':
            return 'status-transit';
        case 'delivered':
            return 'status-delivered';
        case 'delayed':
            return 'status-delayed';
        case 'cancelled':
            return 'status-cancelled';
        default:
            return 'status-pending';
    }
}

/**
 * Generate a random tracking number
 * @return string A random tracking number
 */
function generateTrackingNumber() {
    // Get prefix from settings or use default
    $prefix = getSetting('tracking_number_prefix', 'TL');
    $number = mt_rand(1000000, 9999999);
    return "$prefix$number";
}

/**
 * Add a tracking update for a shipment
 * @param int $shipmentId The shipment ID
 * @param string $location The location of the update
 * @param string $status The status of the update
 * @param float $latitude The latitude of the location (optional)
 * @param float $longitude The longitude of the location (optional)
 * @param string $notes Additional notes (optional)
 * @return bool True if the update was added successfully, false otherwise
 */
function addTrackingUpdate($shipmentId, $location, $status, $latitude = null, $longitude = null, $notes = '') {
    global $db;

    $db->query("INSERT INTO tracking_updates (shipment_id, location, status, latitude, longitude, notes)
                VALUES (:shipment_id, :location, :status, :latitude, :longitude, :notes)");
    $db->bind(':shipment_id', $shipmentId);
    $db->bind(':location', $location);
    $db->bind(':status', $status);
    $db->bind(':latitude', $latitude);
    $db->bind(':longitude', $longitude);
    $db->bind(':notes', $notes);

    return $db->execute();
}

/**
 * Update the status of a shipment
 * @param int $shipmentId The shipment ID
 * @param string $status The new status
 * @return bool True if the status was updated successfully, false otherwise
 */
function updateShipmentStatus($shipmentId, $status) {
    global $db;

    $db->query("UPDATE shipments SET status = :status WHERE id = :id");
    $db->bind(':status', $status);
    $db->bind(':id', $shipmentId);

    return $db->execute();
}

/**
 * Get a system setting
 * @param string $key The setting key
 * @param mixed $default The default value if the setting doesn't exist
 * @return mixed The setting value or default value
 */
function getSetting($key, $default = null) {
    global $conn;

    try {
        // Check if settings table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'system_settings'");
        $settingsTable = $tableCheck->fetchAll(PDO::FETCH_ASSOC);

        if (empty($settingsTable)) {
            return $default;
        }

        // Get setting value
        $stmt = $conn->prepare("SELECT setting_value FROM system_settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($result && isset($result['setting_value'])) {
            return $result['setting_value'];
        }
    } catch (PDOException $e) {
        debugLog('Error getting setting: ' . $e->getMessage());
    }

    return $default;
}

/**
 * Get all system settings
 * @param string $group Optional group to filter settings by
 * @param bool $publicOnly Whether to only return public settings
 * @return array Array of settings
 */
function getAllSettings($group = null, $publicOnly = false) {
    global $conn;
    $settings = [];

    try {
        // Check if settings table exists
        $tableCheck = $conn->query("SHOW TABLES LIKE 'system_settings'");
        $settingsTable = $tableCheck->fetchAll(PDO::FETCH_ASSOC);

        if (empty($settingsTable)) {
            return $settings;
        }

        // Build query
        $sql = "SELECT * FROM system_settings";
        $params = [];

        if ($group) {
            $sql .= " WHERE setting_group = ?";
            $params[] = $group;

            if ($publicOnly) {
                $sql .= " AND is_public = 1";
            }
        } else if ($publicOnly) {
            $sql .= " WHERE is_public = 1";
        }

        $sql .= " ORDER BY setting_group, id";

        // Execute query
        $stmt = $conn->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Organize settings
        foreach ($results as $setting) {
            if (isset($setting['setting_key']) && isset($setting['setting_value'])) {
                $settings[$setting['setting_key']] = $setting['setting_value'];
            }
        }
    } catch (PDOException $e) {
        debugLog('Error getting all settings: ' . $e->getMessage());
    }

    return $settings;
}

/**
 * Generate the header HTML
 * @return string Header HTML
 */
function generateHeader() {
    $nav_items = getNavigationItems();

    ob_start();
    ?>
    <header>
        <div class="logo">
            <!-- Logo container is intentionally empty - will be populated by logo-generator.js -->
        </div>
        <nav id="main-nav">
            <ul>
                <?php foreach ($nav_items as $item): ?>
                    <?php if (isset($item['type']) && $item['type'] === 'theme-toggle'): ?>
                        <li class="theme-toggle">
                            <button id="theme-toggle"><i class="fas <?php echo $item['icon']; ?>"></i></button>
                        </li>
                    <?php else: ?>
                        <li<?php echo isset($item['class']) ? ' class="' . $item['class'] . '"' : ''; ?>>
                            <a href="<?php echo $item['url']; ?>"<?php echo $item['active'] ? ' class="active"' : ''; ?><?php echo isset($item['id']) ? ' id="' . $item['id'] . '"' : ''; ?>>
                                <?php echo $item['text']; ?>
                            </a>
                        </li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ul>
        </nav>
    </header>
    <?php
    return ob_get_clean();
}

/**
 * Generate bottom navigation for mobile
 */
function generateBottomNav() {
    $navigationItems = getNavigationItems();
    $currentPage = basename($_SERVER['PHP_SELF'], '.php');

    ob_start();
    ?>
    <div class="mobile-bottom-nav">
        <div class="mobile-nav-container">
            <?php
            // Filter out theme toggle items and show all navigation items
            $filteredItems = array_filter($navigationItems, function($item) {
                return !isset($item['type']) || $item['type'] !== 'theme-toggle';
            });

            foreach ($filteredItems as $item):
                if (!isset($item['url']) || !isset($item['text'])) continue; // Skip invalid items
                $isActive = ($currentPage === basename($item['url'], '.php')) ? 'active' : '';
                $idAttr = isset($item['id']) ? ' id="' . $item['id'] . '"' : '';
            ?>
                <a href="<?php echo $item['url']; ?>"<?php echo $idAttr; ?> class="mobile-nav-item <?php echo $isActive; ?>">
                    <i data-feather="<?php echo getBottomNavIcon($item['text']); ?>"></i>
                    <span><?php echo $item['text']; ?></span>
                </a>
            <?php
            endforeach;
            ?>

            <?php
            // Login/Dashboard buttons are already handled in the filtered navigation items above
            // No need to add them separately here
            ?>

            <!-- Theme Toggle -->
            <button id="mobile-theme-toggle" class="mobile-nav-item" type="button">
                <i data-feather="moon"></i>
                <span>Theme</span>
            </button>
        </div>
    </div>
    <?php
    return ob_get_clean();
}

/**
 * Get appropriate icon for bottom navigation items
 */
function getBottomNavIcon($text) {
    $icons = [
        'Home' => 'home',
        'Services' => 'truck',
        'Industries' => 'briefcase',
        'About' => 'info',
        'About Us' => 'info',
        'Contact' => 'mail',
        'Track' => 'search',
        'Dashboard' => 'activity',
        'Login' => 'log-in',
        'Register' => 'user-plus',
        'Logout' => 'log-out'
    ];

    return isset($icons[$text]) ? $icons[$text] : 'circle';
}


