<?php
/**
 * Maintenance Dashboard
 *
 * This page provides a centralized interface for all maintenance and debugging tools.
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/maintenance_tools.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Handle tool management actions
if (isset($_POST['action']) && $_POST['action'] === 'toggle_tool' && isset($_POST['tool_id'])) {
    $toolId = $_POST['tool_id'];
    $enabled = isset($_POST['enabled']) ? (bool)$_POST['enabled'] : false;

    // In a real implementation, this would update a database setting
    // For now, we'll just show a message
    $tool = getMaintenanceToolById($toolId);
    if ($tool) {
        $status = $enabled ? 'enabled' : 'disabled';
        setSuccessNotification("Tool '{$tool['name']}' has been {$status}. Note: This is just a simulation, tool status will reset when the page is reloaded.");
    }

    // Redirect to avoid form resubmission
    redirect('maintenance.php');
    exit;
}

// Set page title
$pageTitle = 'Maintenance Dashboard';

// Add maintenance-specific CSS
$additionalCss = [
    SITE_URL . '/assets/css/maintenance-fix.css'
];

// Include header
include_once 'includes/header.php';
?>

<!-- Maintenance Dashboard Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">
            <h1><i class="fas fa-tools"></i> Maintenance Dashboard</h1>
            <p>Centralized access to all maintenance and debugging tools</p>
            <div class="admin-actions">
                <a href="admin/index.php" class="btn primary-btn">Back to Admin Dashboard</a>
                <a href="manage_tools.php" class="btn secondary-btn">Manage Tools</a>
                <a href="cleanup_tools.php" class="btn danger-btn">Cleanup Redundant Files</a>
            </div>
            <div class="admin-actions legacy-tools-action">
                <a href="#" class="btn outline-btn" id="toggleLegacyTools">Show Legacy Tools</a>
            </div>
        </div>
    </div>
</section>

<!-- Maintenance Tools Section -->
<section class="admin-section">
    <div class="container">
        <?php
        // Get all categories
        $categories = getMaintenanceToolCategories();

        // Display tools by category
        foreach ($categories as $category):
            // Skip debug category initially (will be shown when "Show Legacy Tools" is clicked)
            $isDebugCategory = ($category === 'debug');

            // Get tools for this category
            $tools = getMaintenanceToolsByCategory($category, true);

            // Skip empty categories
            if (empty($tools) && !$isDebugCategory) continue;

            // Get category name and icon
            $categoryName = getMaintenanceToolCategoryName($category);
            $categoryIcon = getMaintenanceToolCategoryIcon($category);
        ?>
        <div class="category-section <?php echo $isDebugCategory ? 'legacy-tools' : ''; ?>" <?php echo $isDebugCategory ? 'style="display: none;"' : ''; ?>>
            <div class="category-header">
                <h2><i class="<?php echo $categoryIcon; ?>"></i> <?php echo $categoryName; ?></h2>
            </div>

            <div class="tools-grid">
                <?php foreach ($tools as $tool): ?>
                <div class="tool-card">
                    <div class="tool-tooltip"><?php echo htmlspecialchars($tool['description']); ?></div>
                    <div class="tool-icon">
                        <i class="<?php echo $tool['icon']; ?>"></i>
                    </div>
                    <div class="tool-info">
                        <h3><?php echo $tool['name']; ?></h3>
                        <p><?php echo $tool['description']; ?></p>
                    </div>
                    <div class="tool-actions">
                        <a href="<?php echo $tool['file']; ?>" class="btn primary-btn tool-btn">Run Tool</a>
                        <button type="button" class="btn secondary-btn tool-btn manage-btn" data-tool-id="<?php echo $tool['id']; ?>" data-tool-name="<?php echo $tool['name']; ?>">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>

                <?php if ($isDebugCategory): ?>
                <!-- Legacy/Debug Tools -->
                <?php
                // Get all tools including disabled ones
                $allTools = getMaintenanceToolsByCategory($category, false);
                $disabledTools = array_filter($allTools, function($tool) {
                    return !$tool['enabled'];
                });

                foreach ($disabledTools as $tool):
                ?>
                <div class="tool-card disabled-tool" title="<?php echo htmlspecialchars($tool['description'] . ' (Disabled)'); ?>">
                    <div class="tool-icon">
                        <i class="<?php echo $tool['icon']; ?>"></i>
                    </div>
                    <div class="tool-info">
                        <h3><?php echo $tool['name']; ?></h3>
                        <p><?php echo $tool['description']; ?></p>
                        <span class="disabled-badge">Disabled</span>
                    </div>
                    <div class="tool-actions">
                        <a href="<?php echo $tool['file']; ?>" class="btn outline-btn tool-btn">Run Tool</a>
                        <button type="button" class="btn secondary-btn tool-btn manage-btn" data-tool-id="<?php echo $tool['id']; ?>" data-tool-name="<?php echo $tool['name']; ?>">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        <?php endforeach; ?>

        <!-- Tool Management Modal -->
        <div id="toolManagementModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Manage Tool: <span id="modalToolName"></span></h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <form id="toolManagementForm" method="post" action="maintenance.php">
                        <input type="hidden" name="action" value="toggle_tool">
                        <input type="hidden" name="tool_id" id="modalToolId">

                        <div class="form-group">
                            <label for="toolEnabled">Tool Status:</label>
                            <div class="toggle-switch">
                                <input type="checkbox" id="toolEnabled" name="enabled" value="1">
                                <label for="toolEnabled"></label>
                            </div>
                            <p class="form-help">Enable or disable this tool in the maintenance dashboard.</p>
                        </div>

                        <div class="danger-zone">
                            <h3><i class="fas fa-exclamation-triangle"></i> Danger Zone</h3>
                            <p>The following actions are destructive and cannot be undone.</p>

                            <div class="danger-actions">
                                <button type="button" id="deleteToolBtn" class="btn danger-btn">
                                    <i class="fas fa-trash"></i> Delete Tool File
                                </button>
                            </div>
                        </div>

                        <div class="form-actions">
                            <button type="submit" class="btn primary-btn">Save Changes</button>
                            <button type="button" class="btn secondary-btn close-modal">Cancel</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div id="deleteConfirmModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>Confirm Deletion</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <div class="warning-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Are you sure you want to delete the file for <strong id="deleteToolName"></strong>?</p>
                        <p>This action cannot be undone and may break functionality that depends on this file.</p>
                    </div>

                    <div class="form-actions">
                        <button type="button" id="confirmDeleteBtn" class="btn danger-btn">Yes, Delete File</button>
                        <button type="button" class="btn secondary-btn close-modal">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
/* Maintenance Dashboard Styles */
.category-section {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    margin-bottom: 30px;
}

.category-header {
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.category-header h2 {
    display: flex;
    align-items: center;
    gap: 10px;
    color: var(--primary-color);
    margin: 0;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.tool-card {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
}

.dark-theme .tool-card {
    background-color: rgba(30, 30, 30, 0.7);
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.tool-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 15px;
}

.tool-info {
    flex-grow: 1;
}

.tool-info h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.tool-info p {
    color: var(--text-secondary);
    margin-bottom: 15px;
    font-size: 0.9rem;
}

.tool-actions {
    display: flex;
    gap: 10px;
    margin-top: auto;
}

.tool-btn {
    flex: 1;
    text-align: center;
    padding: 8px 12px;
    font-size: 0.9rem;
}

.manage-btn {
    flex: 0 0 40px;
}

/* Disabled Tool Styles */
.disabled-tool {
    opacity: 0.7;
    border: 1px dashed var(--border-color);
}

.disabled-badge {
    display: inline-block;
    background-color: #dc3545;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    margin-bottom: 10px;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: var(--glass-bg);
    margin: 10% auto;
    padding: 0;
    width: 500px;
    max-width: 90%;
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    animation: modalFadeIn 0.3s;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-body {
    padding: 20px;
}

.close {
    color: var(--text-secondary);
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: var(--primary-color);
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

/* Danger Zone */
.danger-zone {
    margin-top: 30px;
    padding: 20px;
    border: 1px solid #dc3545;
    border-radius: 8px;
    background-color: rgba(220, 53, 69, 0.05);
}

.danger-zone h3 {
    color: #dc3545;
    margin-top: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.danger-actions {
    margin-top: 15px;
}

.danger-btn {
    background-color: #dc3545;
    color: white;
}

.danger-btn:hover {
    background-color: #c82333;
}

/* Warning Message */
.warning-message {
    text-align: center;
    margin-bottom: 20px;
}

.warning-message i {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 15px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.form-help {
    font-size: 0.85rem;
    color: var(--text-secondary);
    margin-top: 5px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* Enhanced Hover Styles */
.tool-card {
    position: relative;
    transition: all 0.3s ease;
    z-index: 1;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    z-index: 2;
}

.tool-card:hover::before {
    content: "";
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: 15px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    opacity: 0.2;
    z-index: -1;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 0.2;
    }
    50% {
        opacity: 0.3;
    }
    100% {
        opacity: 0.2;
    }
}

/* Animation */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle Legacy Tools
    const toggleLegacyBtn = document.getElementById('toggleLegacyTools');
    const legacyToolsSection = document.querySelector('.legacy-tools');

    if (toggleLegacyBtn && legacyToolsSection) {
        toggleLegacyBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const isVisible = legacyToolsSection.style.display !== 'none';
            legacyToolsSection.style.display = isVisible ? 'none' : 'block';
            toggleLegacyBtn.textContent = isVisible ? 'Show Legacy Tools' : 'Hide Legacy Tools';
        });
    }

    // Tool Management Modal
    const toolManagementModal = document.getElementById('toolManagementModal');
    const deleteConfirmModal = document.getElementById('deleteConfirmModal');
    const manageButtons = document.querySelectorAll('.manage-btn');
    const closeButtons = document.querySelectorAll('.close, .close-modal');
    const deleteToolBtn = document.getElementById('deleteToolBtn');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

    // Open tool management modal
    manageButtons.forEach(button => {
        button.addEventListener('click', function() {
            const toolId = this.getAttribute('data-tool-id');
            const toolName = this.getAttribute('data-tool-name');

            document.getElementById('modalToolId').value = toolId;
            document.getElementById('modalToolName').textContent = toolName;

            // For demo purposes, we'll just set the checkbox randomly
            // In a real implementation, this would be based on the tool's actual status
            document.getElementById('toolEnabled').checked = Math.random() > 0.5;

            toolManagementModal.style.display = 'block';
        });
    });

    // Close modals
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            toolManagementModal.style.display = 'none';
            deleteConfirmModal.style.display = 'none';
        });
    });

    // Open delete confirmation modal
    if (deleteToolBtn) {
        deleteToolBtn.addEventListener('click', function() {
            const toolName = document.getElementById('modalToolName').textContent;
            document.getElementById('deleteToolName').textContent = toolName;

            toolManagementModal.style.display = 'none';
            deleteConfirmModal.style.display = 'block';
        });
    }

    // Handle delete confirmation
    if (confirmDeleteBtn) {
        confirmDeleteBtn.addEventListener('click', function() {
            const toolId = document.getElementById('modalToolId').value;

            // In a real implementation, this would make an AJAX request to delete the file
            // For now, we'll just show an alert
            alert('This is a simulation. In a real implementation, the file would be deleted.');

            deleteConfirmModal.style.display = 'none';
        });
    }

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === toolManagementModal) {
            toolManagementModal.style.display = 'none';
        }
        if (event.target === deleteConfirmModal) {
            deleteConfirmModal.style.display = 'none';
        }
    });
});
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
