<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Check if shipments table exists
$db->query("SHOW TABLES LIKE 'shipments'");
$shipmentsTable = $db->resultSet();

if (empty($shipmentsTable)) {
    echo "Shipments table does not exist.<br>";
    exit;
} else {
    echo "Shipments table exists.<br>";
}

// Count total shipments
$db->query("SELECT COUNT(*) as total FROM shipments");
$totalShipments = $db->single();
echo "Total shipments: " . ($totalShipments['total'] ?? 0) . "<br>";

// Count shipments by status
$db->query("SELECT status, COUNT(*) as count FROM shipments GROUP BY status");
$shipmentsByStatus = $db->resultSet();
echo "Shipments by status:<br>";
echo "<pre>";
print_r($shipmentsByStatus);
echo "</pre>";

// Get sample shipment data
echo "Sample shipment data (5 records):<br>";
$db->query("SELECT * FROM shipments LIMIT 5");
$sampleShipments = $db->resultSet();
echo "<pre>";
print_r($sampleShipments);
echo "</pre>";

// Check if the database connection is working
try {
    $testQuery = $conn->query("SELECT 1");
    echo "Database connection is working.<br>";
} catch (PDOException $e) {
    echo "Database connection error: " . $e->getMessage() . "<br>";
}

// Test the stats query directly
try {
    $statsQuery = $conn->query("SELECT
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
        SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
        SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
        SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
        COUNT(DISTINCT origin) as origin_count,
        COUNT(DISTINCT destination) as destination_count,
        COUNT(DISTINCT customer_name) as customer_count
        FROM shipments");
    
    $statsResult = $statsQuery->fetch(PDO::FETCH_ASSOC);
    echo "Stats query result:<br>";
    echo "<pre>";
    print_r($statsResult);
    echo "</pre>";
} catch (PDOException $e) {
    echo "Stats query error: " . $e->getMessage() . "<br>";
}

echo "<a href='admin/index.php'>Go to Admin Dashboard</a>";
