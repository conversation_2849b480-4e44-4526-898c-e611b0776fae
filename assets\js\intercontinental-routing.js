/**
 * Intercontinental Routing Implementation
 * This file provides functionality to route shipments through airports or seaports
 * for intercontinental travel.
 */

// Global variable to store major airports and seaports
const transportHubs = {
    // Major international airports with [lat, lng]
    airports: {
        // North America
        'JFK': { name: 'John F. Kennedy International Airport', location: [40.6413, -73.7781], city: 'New York, USA' },
        'LAX': { name: 'Los Angeles International Airport', location: [33.9416, -118.4085], city: 'Los Angeles, USA' },
        'ORD': { name: 'O\'Hare International Airport', location: [41.9742, -87.9073], city: 'Chicago, USA' },
        'ATL': { name: 'Hartsfield-Jackson Atlanta International Airport', location: [33.6407, -84.4277], city: 'Atlanta, USA' },
        'YYZ': { name: 'Toronto Pearson International Airport', location: [43.6777, -79.6248], city: 'Toronto, Canada' },
        'MEX': { name: 'Mexico City International Airport', location: [19.4363, -99.0721], city: 'Mexico City, Mexico' },

        // Europe
        'LHR': { name: 'London Heathrow Airport', location: [51.4700, -0.4543], city: 'London, UK' },
        'CDG': { name: 'Charles de <PERSON> Airport', location: [49.0097, 2.5479], city: 'Paris, France' },
        'FRA': { name: 'Frankfurt Airport', location: [50.0379, 8.5622], city: 'Frankfurt, Germany' },
        'AMS': { name: 'Amsterdam Airport Schiphol', location: [52.3105, 4.7683], city: 'Amsterdam, Netherlands' },
        'MAD': { name: 'Adolfo Suárez Madrid–Barajas Airport', location: [40.4983, -3.5676], city: 'Madrid, Spain' },
        'FCO': { name: 'Leonardo da Vinci International Airport', location: [41.8045, 12.2508], city: 'Rome, Italy' },

        // Asia
        'PEK': { name: 'Beijing Capital International Airport', location: [40.0799, 116.6031], city: 'Beijing, China' },
        'HND': { name: 'Tokyo Haneda Airport', location: [35.5494, 139.7798], city: 'Tokyo, Japan' },
        'SIN': { name: 'Singapore Changi Airport', location: [1.3644, 103.9915], city: 'Singapore' },
        'ICN': { name: 'Incheon International Airport', location: [37.4602, 126.4407], city: 'Seoul, South Korea' },
        'BOM': { name: 'Chhatrapati Shivaji Maharaj International Airport', location: [19.0896, 72.8656], city: 'Mumbai, India' },
        'DXB': { name: 'Dubai International Airport', location: [25.2532, 55.3657], city: 'Dubai, UAE' },

        // Australia/Oceania
        'SYD': { name: 'Sydney Airport', location: [-33.9399, 151.1753], city: 'Sydney, Australia' },
        'MEL': { name: 'Melbourne Airport', location: [-37.6690, 144.8410], city: 'Melbourne, Australia' },
        'AKL': { name: 'Auckland Airport', location: [-37.0082, 174.7850], city: 'Auckland, New Zealand' },

        // Africa
        'JNB': { name: 'O.R. Tambo International Airport', location: [-26.1367, 28.2411], city: 'Johannesburg, South Africa' },
        'CAI': { name: 'Cairo International Airport', location: [30.1219, 31.4056], city: 'Cairo, Egypt' },
        'LOS': { name: 'Murtala Muhammed International Airport', location: [6.5774, 3.3214], city: 'Lagos, Nigeria' },

        // South America
        'GRU': { name: 'São Paulo–Guarulhos International Airport', location: [-23.4356, -46.4731], city: 'São Paulo, Brazil' },
        'EZE': { name: 'Ministro Pistarini International Airport', location: [-34.8222, -58.5358], city: 'Buenos Aires, Argentina' },
        'BOG': { name: 'El Dorado International Airport', location: [4.7016, -74.1469], city: 'Bogotá, Colombia' }
    },

    // Major seaports with [lat, lng]
    seaports: {
        // North America
        'NYC': { name: 'Port of New York and New Jersey', location: [40.6700, -74.0000], city: 'New York, USA' },
        'LAP': { name: 'Port of Los Angeles', location: [33.7395, -118.2610], city: 'Los Angeles, USA' },
        'HOU': { name: 'Port of Houston', location: [29.7604, -95.3698], city: 'Houston, USA' },
        'VAN': { name: 'Port of Vancouver', location: [49.2827, -123.1207], city: 'Vancouver, Canada' },
        'VER': { name: 'Port of Veracruz', location: [19.2000, -96.1400], city: 'Veracruz, Mexico' },

        // Europe
        'ROT': { name: 'Port of Rotterdam', location: [51.9225, 4.4792], city: 'Rotterdam, Netherlands' },
        'ANT': { name: 'Port of Antwerp', location: [51.2234, 4.4000], city: 'Antwerp, Belgium' },
        'HAM': { name: 'Port of Hamburg', location: [53.5511, 9.9937], city: 'Hamburg, Germany' },
        'VAL': { name: 'Port of Valencia', location: [39.4500, -0.3000], city: 'Valencia, Spain' },
        'PIR': { name: 'Port of Piraeus', location: [37.9428, 23.6395], city: 'Athens, Greece' },

        // Asia
        'SHP': { name: 'Port of Shanghai', location: [31.2304, 121.4737], city: 'Shanghai, China' },
        'SIN': { name: 'Port of Singapore', location: [1.2655, 103.8200], city: 'Singapore' },
        'BUS': { name: 'Port of Busan', location: [35.1796, 129.0756], city: 'Busan, South Korea' },
        'KAO': { name: 'Port of Kaohsiung', location: [22.6273, 120.2857], city: 'Kaohsiung, Taiwan' },
        'MUM': { name: 'Jawaharlal Nehru Port', location: [18.9490, 72.9525], city: 'Mumbai, India' },

        // Australia/Oceania
        'SYP': { name: 'Port Botany', location: [-33.9680, 151.2159], city: 'Sydney, Australia' },
        'MEP': { name: 'Port of Melbourne', location: [-37.8136, 144.9631], city: 'Melbourne, Australia' },
        'AUP': { name: 'Ports of Auckland', location: [-36.8404, 174.7787], city: 'Auckland, New Zealand' },

        // Africa
        'DUR': { name: 'Port of Durban', location: [-29.8700, 31.0500], city: 'Durban, South Africa' },
        'TAN': { name: 'Port of Tangier Med', location: [35.8969, -5.5050], city: 'Tangier, Morocco' },
        'LAG': { name: 'Lagos Port Complex', location: [6.4500, 3.4000], city: 'Lagos, Nigeria' },

        // South America
        'SAN': { name: 'Port of Santos', location: [-23.9619, -46.3042], city: 'Santos, Brazil' },
        'BUE': { name: 'Port of Buenos Aires', location: [-34.6037, -58.3816], city: 'Buenos Aires, Argentina' },
        'CAL': { name: 'Port of Callao', location: [-12.0500, -77.1300], city: 'Lima, Peru' }
    }
};

// Define continents with their approximate bounding boxes
const continents = {
    'North America': {
        bounds: [
            [15, -170], // Southwest corner [lat, lng]
            [85, -50]   // Northeast corner [lat, lng]
        ]
    },
    'South America': {
        bounds: [
            [-60, -90],
            [15, -30]
        ]
    },
    'Europe': {
        bounds: [
            [35, -10],
            [70, 40]
        ]
    },
    'Africa': {
        bounds: [
            [-40, -20],
            [40, 55]
        ]
    },
    'Asia': {
        bounds: [
            [0, 40],
            [70, 180]
        ]
    },
    'Australia/Oceania': {
        bounds: [
            [-50, 110],
            [0, 180]
        ]
    }
};

/**
 * Determine which continent a coordinate belongs to
 * @param {Array} coord - [latitude, longitude]
 * @return {String|null} - Continent name or null if not found
 */
function getContinentForCoordinate(coord) {
    const [lat, lng] = coord;

    for (const [continent, data] of Object.entries(continents)) {
        const [[swLat, swLng], [neLat, neLng]] = data.bounds;

        // Check if the coordinate is within the continent's bounds
        if (lat >= swLat && lat <= neLat && lng >= swLng && lng <= neLng) {
            return continent;
        }
    }

    // If no match found, return null
    return null;
}

/**
 * Calculate the distance between two coordinates using the Haversine formula
 * @param {Array} coord1 - [latitude, longitude]
 * @param {Array} coord2 - [latitude, longitude]
 * @return {Number} - Distance in kilometers
 */
function calculateDistance(coord1, coord2) {
    const [lat1, lon1] = coord1;
    const [lat2, lon2] = coord2;

    const R = 6371; // Radius of the Earth in km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;

    const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon/2) * Math.sin(dLon/2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;

    return distance;
}

/**
 * Find the nearest transport hub (airport or seaport) to a given coordinate
 * @param {Array} coord - [latitude, longitude]
 * @param {String} hubType - 'airports' or 'seaports'
 * @return {Object} - The nearest hub with its details
 */
function findNearestHub(coord, hubType) {
    let nearestHub = null;
    let minDistance = Infinity;

    for (const [code, hub] of Object.entries(transportHubs[hubType])) {
        const distance = calculateDistance(coord, hub.location);

        if (distance < minDistance) {
            minDistance = distance;
            nearestHub = {
                code,
                ...hub,
                distance
            };
        }
    }

    return nearestHub;
}

/**
 * Determine if a route is intercontinental
 * @param {Array} origin - [latitude, longitude]
 * @param {Array} destination - [latitude, longitude]
 * @return {Boolean} - True if the route crosses continents
 */
function isIntercontinentalRoute(origin, destination) {
    const originContinent = getContinentForCoordinate(origin);
    const destinationContinent = getContinentForCoordinate(destination);

    // If we can't determine the continent, assume it's not intercontinental
    if (!originContinent || !destinationContinent) {
        return false;
    }

    // If the continents are different, it's intercontinental
    return originContinent !== destinationContinent;
}

/**
 * Generate waypoints for an intercontinental route
 * @param {Array} origin - [latitude, longitude]
 * @param {Array} destination - [latitude, longitude]
 * @param {String} preference - 'auto', 'air', or 'sea'
 * @return {Array} - Array of waypoints with their details
 */
function generateIntercontinentalRoute(origin, destination, preference = 'auto') {
    // Check if the route is actually intercontinental
    if (!isIntercontinentalRoute(origin, destination)) {
        console.log('Route is not intercontinental, returning direct route');
        // For non-intercontinental routes, just return a direct route with origin and destination
        // This will cause the tracking-map.js to use the original routing method
        return [
            { latLng: L.latLng(origin[0], origin[1]), transportMode: 'road', name: 'Origin' },
            { latLng: L.latLng(destination[0], destination[1]), transportMode: 'road', name: 'Destination' }
        ];
    }

    // Determine which transport mode to use based on preference
    let departureHubType, arrivalHubType;

    if (preference === 'air') {
        departureHubType = arrivalHubType = 'airports';
    } else if (preference === 'sea') {
        departureHubType = arrivalHubType = 'seaports';
    } else {
        // Auto mode - calculate the distance to determine the best mode
        const distance = calculateDistance(origin, destination);

        if (distance > 5000) {
            // For very long distances, prefer air travel
            departureHubType = arrivalHubType = 'airports';
        } else {
            // For shorter intercontinental routes, prefer sea travel
            departureHubType = arrivalHubType = 'seaports';
        }
    }

    // Find the nearest departure and arrival hubs
    const departureHub = findNearestHub(origin, departureHubType);
    const arrivalHub = findNearestHub(destination, arrivalHubType);

    // Create the waypoints for the route
    const waypoints = [
        {
            latLng: L.latLng(origin[0], origin[1]),
            transportMode: 'road',
            name: 'Origin'
        },
        {
            latLng: L.latLng(departureHub.location[0], departureHub.location[1]),
            transportMode: 'road',
            name: departureHub.name,
            isHub: true,
            hubType: departureHubType === 'airports' ? 'airport' : 'seaport',
            hubCode: departureHub.code,
            city: departureHub.city
        },
        {
            latLng: L.latLng(arrivalHub.location[0], arrivalHub.location[1]),
            transportMode: departureHubType === 'airports' ? 'air' : 'sea',
            name: arrivalHub.name,
            isHub: true,
            hubType: arrivalHubType === 'airports' ? 'airport' : 'seaport',
            hubCode: arrivalHub.code,
            city: arrivalHub.city
        },
        {
            latLng: L.latLng(destination[0], destination[1]),
            transportMode: 'road',
            name: 'Destination'
        }
    ];

    return waypoints;
}

/**
 * Create a custom route for intercontinental travel
 * @param {Array} waypoints - Array of waypoints with their details
 * @return {Object} - Route object with coordinates and other details
 */
function createIntercontinentalRoute(waypoints) {
    // Extract coordinates for the route
    const coordinates = waypoints.map(wp => wp.latLng);

    // Create segments between waypoints
    const segments = [];
    for (let i = 0; i < waypoints.length - 1; i++) {
        const from = waypoints[i];
        const to = waypoints[i + 1];

        segments.push({
            from,
            to,
            transportMode: to.transportMode // Use the destination's transport mode for the segment
        });
    }

    // Create a route object
    const route = {
        name: 'Intercontinental Route',
        coordinates,
        waypoints,
        segments,
        summary: {
            totalDistance: calculateTotalDistance(waypoints),
            totalTime: estimateTotalTime(segments)
        }
    };

    return route;
}

/**
 * Calculate the total distance of a route
 * @param {Array} waypoints - Array of waypoints
 * @return {Number} - Total distance in meters
 */
function calculateTotalDistance(waypoints) {
    let totalDistance = 0;

    for (let i = 0; i < waypoints.length - 1; i++) {
        const from = waypoints[i].latLng;
        const to = waypoints[i + 1].latLng;

        totalDistance += from.distanceTo(to);
    }

    return totalDistance;
}

/**
 * Estimate the total time for a route
 * @param {Array} segments - Array of route segments
 * @return {Number} - Estimated time in seconds
 */
function estimateTotalTime(segments) {
    let totalTime = 0;

    for (const segment of segments) {
        const distance = segment.from.latLng.distanceTo(segment.to.latLng);
        let speed;

        // Set speed based on transport mode (in m/s)
        switch (segment.transportMode) {
            case 'air':
                speed = 250; // ~900 km/h
                break;
            case 'sea':
                speed = 10; // ~36 km/h
                break;
            case 'rail':
                speed = 30; // ~108 km/h
                break;
            case 'road':
            default:
                speed = 15; // ~54 km/h
                break;
        }

        totalTime += distance / speed;
    }

    return totalTime;
}

/**
 * Create a custom router for intercontinental routes
 * @param {Object} options - Router options
 * @return {Object} - Custom router object
 */
function createIntercontinentalRouter(options) {
    const defaultOptions = {
        preference: 'auto'
    };

    const routerOptions = { ...defaultOptions, ...options };

    return {
        route: function(waypoints, callback, context) {
            // We only need the first and last waypoints for intercontinental routing
            const origin = [waypoints[0].latLng.lat, waypoints[0].latLng.lng];
            const destination = [waypoints[waypoints.length - 1].latLng.lat, waypoints[waypoints.length - 1].latLng.lng];

            // Generate intercontinental route
            const routeWaypoints = generateIntercontinentalRoute(origin, destination, routerOptions.preference);

            // Create the route object
            const route = createIntercontinentalRoute(routeWaypoints);

            // Call the callback with the route
            callback.call(context || this, null, [route]);
        }
    };
}

// Export the functions for use in other files
window.isIntercontinentalRoute = isIntercontinentalRoute;
window.generateIntercontinentalRoute = generateIntercontinentalRoute;
window.createIntercontinentalRouter = createIntercontinentalRouter;
