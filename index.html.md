<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Global Logistics & Transport Solutions</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="page-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <div class="logo-content">
                <i class="fas fa-truck-fast logo-icon"></i> <span>Global TransLogix</span>
            </div>
        </div>
        <div class="mobile-menu-toggle" id="mobile-menu-toggle">
            <i class="fas fa-bars"></i>
        </div>
        <nav id="main-nav">
            <ul>
                <li><a href="index.html" class="active">Home</a></li>
                <li><a href="services.html">Services</a></li>
                <li><a href="industries.html">Industries</a></li>
                <li><a href="about.html">About Us</a></li>
                <li><a href="contact.html">Contact</a></li>
                <li class="login-btn"><a href="#" id="login-button">Login</a></li>
                <li class="theme-toggle"><button id="theme-toggle"><i class="fas fa-moon"></i></button></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Integrated Logistics Solutions for Global Business</h1>
                <p>We make logistics easy through reliable transport and supply chain services worldwide.</p>
                <div class="cta-buttons">
                    <a href="contact.html" class="btn primary-btn">Get Started</a>
                    <a href="about.html" class="btn secondary-btn">Learn More</a>
                    <a href="#tracking" class="btn teal-btn">Track Shipment</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Tracking Section -->
    <section id="tracking" class="tracking">
        <div class="container">
            <div class="tracking-box">
                <h2>Track Your Shipment</h2>
                <form class="tracking-form">
                    <div class="form-group">
                        <label for="tracking-number">Tracking Number</label>
                        <input type="text" id="tracking-number" placeholder="Enter your tracking number">
                    </div>
                    <button type="submit" class="btn primary-btn">Track</button>
                </form>
                <div class="route-search">
                    <h3>Find Routes & Rates</h3>
                    <form class="route-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="origin">From</label>
                                <input type="text" id="origin" placeholder="Origin">
                            </div>
                            <div class="form-group">
                                <label for="destination">To</label>
                                <input type="text" id="destination" placeholder="Destination">
                            </div>
                        </div>
                        <button type="submit" class="btn primary-btn">Search</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section class="services">
        <div class="container">
            <h2 class="section-title">Our Logistics Solutions</h2>
            <p class="section-subtitle">From warehouse to your doorstep, we develop solutions that meet customer needs across the entire supply chain.</p>

            <div class="services-grid">
                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-ship"></i>
                    </div>
                    <h3>Ocean Freight</h3>
                    <p>Reliable container shipping services connecting global markets with efficiency and care.</p>
                    <a href="services.html#ocean-freight" class="learn-more">Learn more →</a>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-truck"></i>
                    </div>
                    <h3>Land Transport</h3>
                    <p>Comprehensive inland logistics solutions for first and last mile delivery needs.</p>
                    <a href="services.html#land-transport" class="learn-more">Learn more →</a>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                    <h3>Warehousing</h3>
                    <p>Strategic storage solutions with advanced inventory management systems.</p>
                    <a href="services.html#warehousing" class="learn-more">Learn more →</a>
                </div>

                <div class="service-card">
                    <div class="service-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <h3>Supply Chain</h3>
                    <p>End-to-end supply chain management taking complexity out of logistics for you.</p>
                    <a href="services.html#supply-chain" class="learn-more">Learn more →</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Industries Section -->
    <section class="industries">
        <div class="container">
            <h2 class="section-title">Industry Sectors We Serve</h2>
            <p class="section-subtitle">Regardless of your industry or key markets, we offer global and local logistics solutions that enable businesses of all sizes to grow.</p>

            <div class="industries-grid">
                <a href="industries.html#retail">
                    <div class="industry-card">
                        <div class="industry-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <h3>Retail</h3>
                    </div>
                </a>

                <a href="industries.html#fashion">
                    <div class="industry-card">
                        <div class="industry-icon">
                            <i class="fas fa-tshirt"></i>
                        </div>
                        <h3>Fashion & Lifestyle</h3>
                    </div>
                </a>

                <a href="industries.html#chemicals">
                    <div class="industry-card">
                        <div class="industry-icon">
                            <i class="fas fa-flask"></i>
                        </div>
                        <h3>Chemicals</h3>
                    </div>
                </a>

                <a href="industries.html#cold-chain">
                    <div class="industry-card">
                        <div class="industry-icon">
                            <i class="fas fa-snowflake"></i>
                        </div>
                        <h3>Cold Chain</h3>
                    </div>
                </a>

                <a href="industries.html#automotive">
                    <div class="industry-card">
                        <div class="industry-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <h3>Automotive</h3>
                    </div>
                </a>

                <div class="industry-card">
                    <div class="industry-icon">
                        <i class="fas fa-medkit"></i>
                    </div>
                    <h3>Healthcare</h3>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Simplify Your Logistics?</h2>
                <p>Register now to access our digital platform and manage your shipments online.</p>
                <a href="#" class="btn primary-btn">Register Now</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <div class="logo-content">
                        <i class="fas fa-truck-fast logo-icon"></i> <span>Global TransLogix</span>
                    </div>
                    <p>Your trusted partner in global logistics and transportation solutions.</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div class="footer-col">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="services.html#ocean-freight">Ocean Freight</a></li>
                        <li><a href="services.html#land-transport">Land Transport</a></li>
                        <li><a href="services.html#air-freight">Air Freight</a></li>
                        <li><a href="services.html#warehousing">Warehousing</a></li>
                        <li><a href="services.html#supply-chain">Supply Chain</a></li>
                    </ul>
                </div>

                <div class="footer-col">
                    <h4>Industries</h4>
                    <ul>
                        <li><a href="industries.html#retail">Retail</a></li>
                        <li><a href="industries.html#automotive">Automotive</a></li>
                        <li><a href="industries.html#healthcare">Healthcare</a></li>
                        <li><a href="industries.html#chemicals">Chemicals</a></li>
                        <li><a href="industries.html#technology">Technology</a></li>
                    </ul>
                </div>

                <div class="footer-col">
                    <h4>Contact Us</h4>
                    <ul class="contact-info">
                        <li><i class="fas fa-map-marker-alt"></i> 123 Logistics Way, Transport City</li>
                        <li><i class="fas fa-phone"></i> +****************</li>
                        <li><i class="fas fa-envelope"></i> <EMAIL></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2023 TransLogix. All rights reserved.</p>
                <ul class="footer-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Sitemap</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <!-- Theme Toggle and Mobile Menu Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Theme Toggle Functionality
            const themeToggle = document.getElementById('theme-toggle');
            const body = document.body;
            const icon = themeToggle.querySelector('i');

            // Check for saved theme preference or use device preference
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark' || (!savedTheme && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                body.classList.add('dark-theme');
                icon.classList.replace('fa-moon', 'fa-sun');
            }

            // Toggle theme on button click
            themeToggle.addEventListener('click', function() {
                body.classList.toggle('dark-theme');

                // Update icon
                if (body.classList.contains('dark-theme')) {
                    icon.classList.replace('fa-moon', 'fa-sun');
                    localStorage.setItem('theme', 'dark');
                } else {
                    icon.classList.replace('fa-sun', 'fa-moon');
                    localStorage.setItem('theme', 'light');
                }
            });

            // Mobile Menu Functionality
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const nav = document.getElementById('main-nav');

            // Toggle mobile menu
            mobileMenuToggle.addEventListener('click', function() {
                nav.classList.toggle('active');
                body.classList.toggle('menu-active');

                // Toggle menu icon between bars and X
                const menuIcon = mobileMenuToggle.querySelector('i');
                if (nav.classList.contains('active')) {
                    menuIcon.classList.replace('fa-bars', 'fa-times');
                } else {
                    menuIcon.classList.replace('fa-times', 'fa-bars');
                }
            });

            // Close mobile menu when clicking outside
            document.addEventListener('click', function(event) {
                const isClickInsideNav = nav.contains(event.target);
                const isClickOnToggle = mobileMenuToggle.contains(event.target);

                if (!isClickInsideNav && !isClickOnToggle && nav.classList.contains('active')) {
                    nav.classList.remove('active');
                    body.classList.remove('menu-active');
                    mobileMenuToggle.querySelector('i').classList.replace('fa-times', 'fa-bars');
                }
            });

            // Close mobile menu when window is resized to desktop size
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992 && nav.classList.contains('active')) {
                    nav.classList.remove('active');
                    body.classList.remove('menu-active');
                    mobileMenuToggle.querySelector('i').classList.replace('fa-times', 'fa-bars');
                }
            });

            // Login Popup Functionality
            const loginButton = document.getElementById('login-button');
            const loginPopup = document.getElementById('login-popup');
            const loginOverlay = document.getElementById('login-overlay');
            const closeLogin = document.getElementById('close-login');
            const loginForm = document.getElementById('login-form');

            // Open login popup
            loginButton.addEventListener('click', function(e) {
                e.preventDefault();
                loginPopup.classList.add('active');
                loginOverlay.classList.add('active');
            });

            // Close login popup
            closeLogin.addEventListener('click', function() {
                loginPopup.classList.remove('active');
                loginOverlay.classList.remove('active');
            });

            // Close on overlay click
            loginOverlay.addEventListener('click', function() {
                loginPopup.classList.remove('active');
                loginOverlay.classList.remove('active');
            });

            // Handle login form submission
            document.getElementById('login-form').addEventListener('submit', function(e) {
                e.preventDefault();

                const username = document.getElementById('login_username').value;
                const password = document.getElementById('login_password').value;
                const remember = document.getElementById('remember').checked;

                // Debug log
                console.log('Submitting:', {
                    username: username,
                    password: password ? 'set' : 'empty',
                    remember: remember
                });

                const formData = new FormData();
                formData.append('login_username', username);
                formData.append('login_password', password);
                formData.append('remember', remember ? '1' : '0');

                fetch('includes/handle-login.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.text())
                .then(data => {
                    console.log('Raw response:', data); // Debug log
                    try {
                        const jsonData = JSON.parse(data);
                        console.log('Parsed response:', jsonData); // Debug log
                        if (jsonData.status === 'success') {
                            window.location.href = jsonData.redirect;
                        } else {
                            if (jsonData.debug) {
                                console.log('Debug info:', jsonData.debug); // Debug log
                            }
                            alert(jsonData.message);
                        }
                    } catch (e) {
                        console.error('Error parsing JSON:', e);
                        console.log('Raw response:', data);
                        alert('An error occurred during login');
                    }
                })
                .catch(error => {
                    console.error('Fetch error:', error);
                    alert('An error occurred during login');
                });
            });

            // Close login popup when Escape key is pressed
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && loginPopup.classList.contains('active')) {
                    loginPopup.classList.remove('active');
                    loginOverlay.classList.remove('active');
                }
            });
        });
    </script>

    <!-- Login Popup -->
    <div id="login-popup" class="login-popup">
        <div class="login-popup-content">
            <div class="login-header">
                <h3>Login to Your Account</h3>
                <button id="close-login" class="close-login"><i class="fas fa-times"></i></button>
            </div>
            <form class="login-form" id="login-form" method="POST">
                <div class="form-group">
                    <label for="login_username">Username</label>
                    <input type="text" id="login_username" name="login_username" placeholder="Enter your username" required>
                </div>
                <div class="form-group">
                    <label for="login_password">Password</label>
                    <input type="password" id="login_password" name="login_password" placeholder="Enter your password" required>
                </div>
                <div class="form-options">
                    <div class="remember-me">
                        <input type="checkbox" id="remember" name="remember">
                        <label for="remember">Remember me</label>
                    </div>
                </div>
                <button type="submit" class="btn primary-btn">Login</button>
            </form>
            <div class="login-footer">
                <p>Don't have an account? <a href="contact.html">Register Now</a></p>
            </div>
        </div>
    </div>

    <!-- Login Popup Overlay -->
    <div id="login-overlay" class="login-overlay"></div>
</body>
</html>
