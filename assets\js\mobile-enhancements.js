/**
 * Mobile Enhancements
 * This script adds mobile-specific functionality to improve the user experience
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on a mobile device
    const isMobile = window.innerWidth <= 768;
    
    // Add mobile class to body if on mobile
    if (isMobile) {
        document.body.classList.add('is-mobile');
    }
    
    // ===== Transform tables for mobile view =====
    function transformTablesToMobile() {
        // Only transform on mobile devices
        if (!isMobile) return;
        
        // Find all tables that should be responsive
        const tables = document.querySelectorAll('.data-table, .shipments-table, .tracking-table');
        
        tables.forEach(table => {
            // Add mobile card table class
            table.classList.add('mobile-card-table');
            
            // Get header text for each column
            const headerTexts = [];
            const headerCells = table.querySelectorAll('thead th');
            headerCells.forEach(th => {
                headerTexts.push(th.textContent.trim());
            });
            
            // Add data-label attribute to each cell based on its column header
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                cells.forEach((cell, index) => {
                    if (index < headerTexts.length) {
                        cell.setAttribute('data-label', headerTexts[index]);
                    }
                });
            });
        });
    }
    
    // ===== Enhance form elements for mobile =====
    function enhanceFormElements() {
        if (!isMobile) return;
        
        // Add floating label behavior
        const formInputs = document.querySelectorAll('input:not([type="checkbox"]):not([type="radio"]), select, textarea');
        
        formInputs.forEach(input => {
            // Skip if already in a floating container
            if (input.parentElement.classList.contains('form-floating')) return;
            
            const label = document.querySelector(`label[for="${input.id}"]`);
            if (!label) return;
            
            // Create floating container
            const floatingContainer = document.createElement('div');
            floatingContainer.classList.add('form-floating');
            
            // Move input into container
            input.parentNode.insertBefore(floatingContainer, input);
            floatingContainer.appendChild(input);
            floatingContainer.appendChild(label);
        });
    }
    
    // ===== Improve scrolling behavior =====
    function improveScrolling() {
        // Smooth scroll to anchors
        document.querySelectorAll('a[href^="#"]:not([href="#"])').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                
                if (targetElement) {
                    // Smooth scroll to element
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Close mobile menu if open
                    const nav = document.getElementById('main-nav');
                    if (nav && nav.classList.contains('active')) {
                        nav.classList.remove('active');
                        document.body.classList.remove('menu-active');
                        const menuToggle = document.getElementById('mobile-menu-toggle');
                        if (menuToggle) {
                            menuToggle.querySelector('i').classList.replace('fa-times', 'fa-bars');
                        }
                    }
                }
            });
        });
    }
    
    // ===== Add pull-to-refresh for tracking page =====
    function setupPullToRefresh() {
        if (!isMobile) return;
        
        // Only on tracking page
        const trackingInfo = document.querySelector('.tracking-info');
        if (!trackingInfo) return;
        
        let touchStartY = 0;
        let touchEndY = 0;
        const minSwipeDistance = 100;
        const refreshIndicator = document.createElement('div');
        refreshIndicator.classList.add('refresh-indicator');
        refreshIndicator.innerHTML = '<i class="fas fa-sync-alt"></i><span>Pull to refresh</span>';
        refreshIndicator.style.display = 'none';
        
        document.body.insertBefore(refreshIndicator, document.body.firstChild);
        
        document.addEventListener('touchstart', function(e) {
            touchStartY = e.touches[0].clientY;
            
            // Only show indicator if at top of page
            if (window.scrollY <= 10) {
                refreshIndicator.style.display = 'flex';
                refreshIndicator.style.transform = 'translateY(-100%)';
            }
        }, false);
        
        document.addEventListener('touchmove', function(e) {
            if (window.scrollY > 10) return;
            
            touchEndY = e.touches[0].clientY;
            const distance = touchEndY - touchStartY;
            
            if (distance > 0 && distance < 200) {
                refreshIndicator.style.transform = `translateY(${distance - 100}%)`;
                
                if (distance > minSwipeDistance) {
                    refreshIndicator.querySelector('span').textContent = 'Release to refresh';
                    refreshIndicator.classList.add('ready');
                } else {
                    refreshIndicator.querySelector('span').textContent = 'Pull to refresh';
                    refreshIndicator.classList.remove('ready');
                }
            }
        }, false);
        
        document.addEventListener('touchend', function(e) {
            const distance = touchEndY - touchStartY;
            
            if (distance > minSwipeDistance && window.scrollY <= 10) {
                refreshIndicator.style.transform = 'translateY(0)';
                refreshIndicator.querySelector('i').classList.add('rotating');
                refreshIndicator.querySelector('span').textContent = 'Refreshing...';
                
                // Reload the page after animation
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                refreshIndicator.style.transform = 'translateY(-100%)';
                setTimeout(() => {
                    refreshIndicator.style.display = 'none';
                }, 300);
            }
        }, false);
    }
    
    // ===== Enhance mobile navigation =====
    function enhanceMobileNavigation() {
        if (!isMobile) return;

        const nav = document.getElementById('main-nav');
        if (!nav) return;

        // Navigation enhancement without adding icons to navigation links
        // Icons are disabled for navigation links per user preference to keep clean header style
        // Theme toggle and mobile menu toggle keep their Font Awesome icons
        const navItems = nav.querySelectorAll('ul li a:not(.theme-toggle button)');

        navItems.forEach(item => {
            // Skip theme toggle button
            if (item.closest('.theme-toggle')) return;

            // Just ensure proper mobile styling without adding icons to navigation links
            item.style.padding = '12px 16px';
            item.style.display = 'flex';
            item.style.alignItems = 'center';
            item.style.width = '100%';
            item.style.fontWeight = '500';
        });
    }
    
    // ===== Add mobile-specific touch gestures =====
    function addTouchGestures() {
        if (!isMobile) return;
        
        // Swipe to navigate on tracking page
        const trackingSteps = document.querySelector('.shipment-progress');
        if (trackingSteps) {
            let touchStartX = 0;
            let touchEndX = 0;
            const minSwipeDistance = 50;
            
            trackingSteps.addEventListener('touchstart', function(e) {
                touchStartX = e.touches[0].clientX;
            }, false);
            
            trackingSteps.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].clientX;
                handleSwipe();
            }, false);
            
            function handleSwipe() {
                const distance = touchEndX - touchStartX;
                
                // If significant horizontal swipe
                if (Math.abs(distance) > minSwipeDistance) {
                    // Get current tracking ID
                    const currentTrackingId = new URLSearchParams(window.location.search).get('tracking');
                    if (!currentTrackingId) return;
                    
                    // Get all tracking IDs from local storage
                    const recentTrackings = JSON.parse(localStorage.getItem('recentTrackings') || '[]');
                    const currentIndex = recentTrackings.indexOf(currentTrackingId);
                    
                    if (currentIndex !== -1) {
                        let newIndex;
                        
                        // Swipe right (previous)
                        if (distance > 0 && currentIndex > 0) {
                            newIndex = currentIndex - 1;
                            window.location.href = `tracking.php?tracking=${recentTrackings[newIndex]}`;
                        }
                        // Swipe left (next)
                        else if (distance < 0 && currentIndex < recentTrackings.length - 1) {
                            newIndex = currentIndex + 1;
                            window.location.href = `tracking.php?tracking=${recentTrackings[newIndex]}`;
                        }
                    }
                }
            }
        }
    }
    
    // ===== Optimize images for mobile =====
    function optimizeImages() {
        if (!isMobile) return;
        
        // Add lazy loading to all images
        document.querySelectorAll('img').forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
        });
    }
    
    // Run all mobile enhancements
    transformTablesToMobile();
    enhanceFormElements();
    improveScrolling();
    setupPullToRefresh();
    enhanceMobileNavigation();
    addTouchGestures();
    optimizeImages();
    
    // Update on resize
    window.addEventListener('resize', function() {
        const wasMobile = document.body.classList.contains('is-mobile');
        const isMobileNow = window.innerWidth <= 768;
        
        // Update mobile class if changed
        if (isMobileNow && !wasMobile) {
            document.body.classList.add('is-mobile');
            transformTablesToMobile();
            enhanceFormElements();
        } else if (!isMobileNow && wasMobile) {
            document.body.classList.remove('is-mobile');
        }
    });
});
