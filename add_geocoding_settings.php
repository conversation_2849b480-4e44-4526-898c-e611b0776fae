<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

try {
    global $conn;
    
    // Check if settings already exist
    $stmt = $conn->query("SELECT COUNT(*) as count FROM system_settings WHERE setting_key IN ('opencage_api_key', 'geocoding_provider')");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "<h2>Geocoding settings already exist</h2>";
        echo "<p>Some geocoding settings are already in the database. You can update them in the admin settings page.</p>";
    } else {
        // Add geocoding settings
        $settings = [
            [
                'setting_key' => 'opencage_api_key',
                'setting_value' => '',
                'setting_description' => 'API key for OpenCage Geocoder',
                'setting_group' => 'geocoding',
                'is_public' => 0
            ],
            [
                'setting_key' => 'geocoding_provider',
                'setting_value' => 'opencage',
                'setting_description' => 'Preferred geocoding provider (opencage, nominatim)',
                'setting_group' => 'geocoding',
                'is_public' => 0
            ],
            [
                'setting_key' => 'enable_geocoding',
                'setting_value' => '1',
                'setting_description' => 'Enable automatic geocoding of locations',
                'setting_group' => 'geocoding',
                'is_public' => 0
            ]
        ];
        
        $stmt = $conn->prepare("
            INSERT INTO system_settings 
            (setting_key, setting_value, setting_description, setting_group, is_public) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($settings as $setting) {
            $stmt->execute([
                $setting['setting_key'],
                $setting['setting_value'],
                $setting['setting_description'],
                $setting['setting_group'],
                $setting['is_public']
            ]);
        }
        
        echo "<h2>Geocoding settings added successfully</h2>";
        echo "<p>Geocoding settings have been added to the database. You can now configure them in the admin settings page.</p>";
    }
    
    // Create geocoding_cache table if it doesn't exist
    $tableCheck = $conn->query("SHOW TABLES LIKE 'geocoding_cache'");
    if ($tableCheck->rowCount() == 0) {
        $conn->exec("CREATE TABLE geocoding_cache (
            id INT AUTO_INCREMENT PRIMARY KEY,
            query_type ENUM('geocode', 'reverse') NOT NULL,
            query_string VARCHAR(255) NOT NULL,
            result TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX (query_type, query_string(191))
        )");
        
        echo "<p>Geocoding cache table created successfully.</p>";
    } else {
        echo "<p>Geocoding cache table already exists.</p>";
    }
    
    echo "<p><a href='admin/settings.php' class='btn'>Go to Settings</a></p>";
    
} catch (PDOException $e) {
    echo "<h2>Error</h2>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
}
?>
