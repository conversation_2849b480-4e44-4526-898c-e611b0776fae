<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>Adding Test Tracking Data</h1>";

try {
    // First, clear existing tracking data for a clean test
    $conn->exec("DELETE FROM tracking_updates WHERE shipment_id IN (SELECT id FROM shipments WHERE tracking_number = 'TEST123')");
    $conn->exec("DELETE FROM shipments WHERE tracking_number = 'TEST123'");
    
    echo "<p>Cleared existing test data.</p>";
    
    // Create a test shipment
    $stmt = $conn->prepare("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, estimated_delivery)
                          VALUES (?, ?, ?, ?, ?, ?, ?)");
    
    $trackingNumber = 'TEST123';
    $customerName = 'Test Customer';
    $origin = 'New York, USA';
    $destination = 'Los Angeles, USA';
    $status = 'in_transit';
    $createdAt = date('Y-m-d H:i:s', strtotime("-5 days"));
    $estimatedDelivery = date('Y-m-d', strtotime("+2 days"));
    
    $stmt->execute([$trackingNumber, $customerName, $origin, $destination, $status, $createdAt, $estimatedDelivery]);
    $shipmentId = $conn->lastInsertId();
    
    echo "<p>Created test shipment with ID: $shipmentId</p>";
    
    // Define tracking points with exact coordinates
    $trackingPoints = [
        [
            'location' => 'New York, USA',
            'status' => 'pending',
            'lat' => 40.7128,
            'lng' => -74.0060,
            'notes' => 'Shipment received at origin facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-5 days'))
        ],
        [
            'location' => 'Philadelphia, USA',
            'status' => 'in_transit',
            'lat' => 39.9526,
            'lng' => -75.1652,
            'notes' => 'Shipment in transit',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-4 days'))
        ],
        [
            'location' => 'Chicago, USA',
            'status' => 'in_transit',
            'lat' => 41.8781,
            'lng' => -87.6298,
            'notes' => 'Shipment arrived at sorting facility',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-3 days'))
        ],
        [
            'location' => 'Denver, USA',
            'status' => 'in_transit',
            'lat' => 39.7392,
            'lng' => -104.9903,
            'notes' => 'Shipment in transit to destination',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-2 days'))
        ],
        [
            'location' => 'Las Vegas, USA',
            'status' => 'in_transit',
            'lat' => 36.1699,
            'lng' => -115.1398,
            'notes' => 'Shipment in transit to final destination',
            'timestamp' => date('Y-m-d H:i:s', strtotime('-1 day'))
        ]
    ];
    
    // Insert tracking updates
    $insertStmt = $conn->prepare("
        INSERT INTO tracking_updates 
        (shipment_id, location, status, latitude, longitude, notes, timestamp)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($trackingPoints as $point) {
        $insertStmt->execute([
            $shipmentId,
            $point['location'],
            $point['status'],
            $point['lat'],
            $point['lng'],
            $point['notes'],
            $point['timestamp']
        ]);
        
        echo "<p>Added tracking point: {$point['location']} ({$point['status']})</p>";
    }
    
    echo "<h2>Test Data Added Successfully</h2>";
    echo "<p>Use tracking number <strong>TEST123</strong> to test the tracking map.</p>";
    echo "<p><a href='tracking/index.php?tracking_number=TEST123'>View Tracking Map</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
