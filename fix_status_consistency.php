<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>Fixing Status Format Consistency</h1>";

try {
    // Step 1: Check if the shipments table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'shipments'");
    if ($tableCheck->rowCount() == 0) {
        echo "<p>Shipments table does not exist. No changes needed.</p>";
        exit;
    }
    
    // Step 2: Check the current ENUM definition
    $stmt = $conn->query("SHOW COLUMNS FROM shipments WHERE Field = 'status'");
    $column = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$column) {
        echo "<p>Status column not found in shipments table.</p>";
        exit;
    }
    
    echo "<p>Current status column definition: " . $column['Type'] . "</p>";
    
    // Step 3: Check if we need to update the ENUM definition
    $needsSchemaUpdate = false;
    if (strpos($column['Type'], "'in-transit'") !== false) {
        $needsSchemaUpdate = true;
        echo "<p>Found 'in-transit' in ENUM definition. Schema update needed.</p>";
    } else {
        echo "<p>ENUM definition already uses 'in_transit'. No schema update needed.</p>";
    }
    
    // Step 4: Update the ENUM definition if needed
    if ($needsSchemaUpdate) {
        $newType = str_replace("'in-transit'", "'in_transit'", $column['Type']);
        $conn->exec("ALTER TABLE shipments MODIFY COLUMN status $newType");
        echo "<p>Updated ENUM definition to use 'in_transit'.</p>";
    }
    
    // Step 5: Check if there are any shipments with the old format
    $checkStmt = $conn->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'in-transit'");
    $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "<p>Found {$result['count']} shipments with the old 'in-transit' format.</p>";
        
        // Update the status format
        $updateStmt = $conn->prepare("UPDATE shipments SET status = 'in_transit' WHERE status = 'in-transit'");
        $updateStmt->execute();
        
        echo "<p>Successfully updated {$updateStmt->rowCount()} shipments to use 'in_transit' format.</p>";
    } else {
        echo "<p>No shipments found with the old 'in-transit' format. No data updates needed.</p>";
    }
    
    // Step 6: Check for any other status values that need to be updated
    $statuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
    $validStatusList = "'" . implode("', '", $statuses) . "'";
    
    $invalidStmt = $conn->query("SELECT id, tracking_number, status FROM shipments WHERE status NOT IN ($validStatusList)");
    $invalidShipments = $invalidStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invalidShipments) > 0) {
        echo "<p>Found " . count($invalidShipments) . " shipments with invalid status values:</p>";
        echo "<ul>";
        
        foreach ($invalidShipments as $shipment) {
            echo "<li>Shipment #{$shipment['id']} ({$shipment['tracking_number']}): '{$shipment['status']}'</li>";
            
            // Try to normalize the status
            $normalizedStatus = str_replace('-', '_', $shipment['status']);
            
            if (in_array($normalizedStatus, $statuses)) {
                $updateInvalidStmt = $conn->prepare("UPDATE shipments SET status = ? WHERE id = ?");
                $updateInvalidStmt->execute([$normalizedStatus, $shipment['id']]);
                echo " - Updated to '$normalizedStatus'";
            } else {
                echo " - Could not automatically fix. Setting to 'pending'";
                $updateInvalidStmt = $conn->prepare("UPDATE shipments SET status = 'pending' WHERE id = ?");
                $updateInvalidStmt->execute([$shipment['id']]);
            }
        }
        
        echo "</ul>";
    } else {
        echo "<p>No shipments found with invalid status values.</p>";
    }
    
    // Step 7: Check tracking_updates table for consistency
    $trackingUpdatesCheck = $conn->query("SHOW TABLES LIKE 'tracking_updates'");
    if ($trackingUpdatesCheck->rowCount() > 0) {
        echo "<p>Checking tracking_updates table for status consistency...</p>";
        
        // Update any 'in-transit' values in tracking_updates
        $updateTrackingStmt = $conn->prepare("UPDATE tracking_updates SET status = 'in_transit' WHERE status = 'in-transit'");
        $updateTrackingStmt->execute();
        $updatedTrackingCount = $updateTrackingStmt->rowCount();
        
        if ($updatedTrackingCount > 0) {
            echo "<p>Updated $updatedTrackingCount tracking updates from 'in-transit' to 'in_transit'.</p>";
        } else {
            echo "<p>No tracking updates needed to be fixed.</p>";
        }
    }
    
    echo "<p>Status format consistency check completed successfully.</p>";
    echo "<p><a href='admin/index.php'>Return to Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
