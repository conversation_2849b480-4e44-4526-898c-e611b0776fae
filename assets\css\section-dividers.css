/* Section Dividers CSS */

.section-divider {
    height: 70px;
    position: relative;
    overflow: hidden;
    margin-top: -35px;
    margin-bottom: -35px;
    z-index: 1;
    pointer-events: none;
}

.section-divider svg {
    position: absolute;
    width: 100%;
    height: 70px;
    fill: var(--bg-color);
}

.section-divider.dark svg {
    fill: var(--bg-secondary);
}

/* Dark theme adjustments */
.dark-theme .section-divider svg {
    fill: var(--bg-color);
}

.dark-theme .section-divider.dark svg {
    fill: var(--bg-secondary);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .section-divider {
        height: 50px;
        margin-top: -25px;
        margin-bottom: -25px;
    }

    .section-divider svg {
        height: 50px;
    }
}

@media (max-width: 576px) {
    .section-divider {
        height: 40px;
        margin-top: -20px;
        margin-bottom: -20px;
    }

    .section-divider svg {
        height: 40px;
    }
}
