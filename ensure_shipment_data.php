<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if shipments table exists
try {
    $tableCheck = $conn->query("SHOW TABLES LIKE 'shipments'");
    $shipmentsTable = $tableCheck->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($shipmentsTable)) {
        echo "Shipments table does not exist. Creating it now...<br>";
        
        // Create shipments table
        $conn->exec("CREATE TABLE IF NOT EXISTS shipments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tracking_number VARCHAR(20) NOT NULL,
            customer_name VARCHAR(100) NOT NULL,
            origin VARCHAR(100) NOT NULL,
            destination VARCHAR(100) NOT NULL,
            status ENUM('pending', 'in_transit', 'delivered', 'delayed', 'cancelled') NOT NULL DEFAULT 'pending',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NULL,
            delivered_at DATETIME NULL,
            estimated_delivery DATE NULL,
            notes TEXT NULL
        )");
        
        echo "Shipments table created successfully.<br>";
    } else {
        echo "Shipments table exists.<br>";
    }
    
    // Check if there's data in the shipments table
    $countQuery = $conn->query("SELECT COUNT(*) as count FROM shipments");
    $countResult = $countQuery->fetch(PDO::FETCH_ASSOC);
    
    if ($countResult['count'] < 10) {
        echo "Not enough data in shipments table. Adding sample data...<br>";
        
        // Add sample data
        $statuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
        $origins = ['New York, USA', 'Los Angeles, USA', 'Chicago, USA', 'London, UK', 'Paris, France'];
        $destinations = ['Miami, USA', 'Dallas, USA', 'Seattle, USA', 'Manchester, UK', 'Lyon, France'];
        $customers = ['John Smith', 'Jane Doe', 'Robert Johnson', 'Emily Brown', 'Michael Davis'];
        
        // Prepare the insert statement
        $insertStmt = $conn->prepare("INSERT INTO shipments 
            (tracking_number, customer_name, origin, destination, status, created_at, delivered_at, estimated_delivery) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        
        // Generate 50 sample shipments
        for ($i = 0; $i < 50; $i++) {
            $trackingNumber = 'TL' . mt_rand(1000000, 9999999);
            $customerName = $customers[array_rand($customers)];
            $origin = $origins[array_rand($origins)];
            $destination = $destinations[array_rand($destinations)];
            $status = $statuses[array_rand($statuses)];
            
            // Random date within the past year
            $daysAgo = mt_rand(0, 365);
            $createdAt = date('Y-m-d H:i:s', strtotime("-$daysAgo days"));
            
            // Set delivered_at if status is delivered
            $deliveredAt = null;
            if ($status === 'delivered') {
                $deliveryDays = mt_rand(1, 10);
                $deliveredAt = date('Y-m-d H:i:s', strtotime($createdAt . " +$deliveryDays days"));
            }
            
            // Set estimated delivery date
            $estimatedDelivery = date('Y-m-d', strtotime($createdAt . " +7 days"));
            
            // Insert shipment
            $insertStmt->execute([
                $trackingNumber,
                $customerName,
                $origin,
                $destination,
                $status,
                $createdAt,
                $deliveredAt,
                $estimatedDelivery
            ]);
        }
        
        echo "Added 50 sample shipments.<br>";
        
        // Make sure we have shipments in the current month
        $currentMonth = date('Y-m');
        $currentMonthQuery = $conn->prepare("SELECT COUNT(*) as count FROM shipments WHERE DATE_FORMAT(created_at, '%Y-%m') = ?");
        $currentMonthQuery->execute([$currentMonth]);
        $currentMonthCount = $currentMonthQuery->fetch(PDO::FETCH_ASSOC);
        
        if ($currentMonthCount['count'] < 10) {
            echo "Adding shipments for the current month...<br>";
            
            // Add 10 shipments for the current month
            for ($i = 0; $i < 10; $i++) {
                $trackingNumber = 'TL' . mt_rand(1000000, 9999999);
                $customerName = $customers[array_rand($customers)];
                $origin = $origins[array_rand($origins)];
                $destination = $destinations[array_rand($destinations)];
                $status = $statuses[array_rand($statuses)];
                
                // Random date within the current month
                $daysAgo = mt_rand(0, date('j') - 1);
                $createdAt = date('Y-m-d H:i:s', strtotime("-$daysAgo days"));
                
                // Set delivered_at if status is delivered
                $deliveredAt = null;
                if ($status === 'delivered') {
                    $deliveryDays = mt_rand(1, 5);
                    $deliveredAt = date('Y-m-d H:i:s', strtotime($createdAt . " +$deliveryDays days"));
                }
                
                // Set estimated delivery date
                $estimatedDelivery = date('Y-m-d', strtotime($createdAt . " +7 days"));
                
                // Insert shipment
                $insertStmt->execute([
                    $trackingNumber,
                    $customerName,
                    $origin,
                    $destination,
                    $status,
                    $createdAt,
                    $deliveredAt,
                    $estimatedDelivery
                ]);
            }
            
            echo "Added 10 shipments for the current month.<br>";
        }
    } else {
        echo "Shipments table has enough data (" . $countResult['count'] . " records).<br>";
    }
    
    echo "Data check complete. <a href='admin/index.php'>Go to Admin Dashboard</a>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "<br>";
}
