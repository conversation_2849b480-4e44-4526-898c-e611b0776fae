<?php
// Define the root directory path
$root_path = dirname(dirname(dirname(__FILE__)));

// Include required files using absolute paths
require_once $root_path . '/includes/config.php';
require_once $root_path . '/includes/functions.php';

// Get settings for appearance
$siteName = getSetting('site_name', SITE_NAME);
$primaryColor = getSetting('primary_color', '#4a6cf7');
$secondaryColor = getSetting('secondary_color', '#6c757d');
$enableDarkMode = getSetting('enable_dark_mode', '1');
$maintenanceMode = getSetting('maintenance_mode', '0');
$maintenanceMessage = getSetting('maintenance_message', '');

// Check if current page is admin page
$isAdminPage = strpos($_SERVER['PHP_SELF'], '/admin/') !== false;
?>
<!DOCTYPE html>
<html lang="en"
      data-primary-color="<?php echo htmlspecialchars($primaryColor); ?>"
      data-secondary-color="<?php echo htmlspecialchars($secondaryColor); ?>"
      data-enable-dark-mode="<?php echo htmlspecialchars($enableDarkMode); ?>"
      data-maintenance-mode="<?php echo htmlspecialchars($maintenanceMode); ?>"
      data-maintenance-message="<?php echo htmlspecialchars($maintenanceMessage); ?>"
      data-site-name="<?php echo htmlspecialchars($siteName); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="base-path" content="<?php echo rtrim(SITE_URL, '/'); ?>">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . htmlspecialchars($siteName) : htmlspecialchars($siteName); ?></title>

    <!-- CSS Files with absolute paths for user section -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/styles.css">
    <!-- Apply settings immediately to prevent flash -->
    <style>
        :root {
            --primary-color: <?php echo $primaryColor; ?> !important;
            --secondary-color: <?php echo $secondaryColor; ?> !important;
        }
    </style>
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/page-styles.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/inner-pages.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/enhanced-design.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/textures.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/placeholder-images.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/enhanced-search.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/enhanced-cards.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/notifications.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/logo.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/user-dashboard.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/mobile-optimization.css">
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/responsive-typography.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://unpkg.com/feather-icons"></script>

    <?php if($isAdminPage): ?>
        <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin-crud.css">
        <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/admin-dashboard-fix.css">
        <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/logo-preview.css">
    <?php endif; ?>

    <?php if(isset($additionalCss)): ?>
        <?php foreach($additionalCss as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Apply settings script -->
    <script src="<?php echo SITE_URL; ?>/assets/js/apply-settings.js"></script>
</head>
<body class="<?php echo isset($_COOKIE['theme']) && $_COOKIE['theme'] === 'dark' ? 'dark-theme' : ''; ?> <?php echo $isAdminPage ? 'admin-page' : ''; ?>">
    <?php
    echo generateHeader();
    displayMessage();
    ?>