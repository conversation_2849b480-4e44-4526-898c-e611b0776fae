/* Admin Mobile Responsive Styles */

/* Base Mobile Styles */
@media (max-width: 1200px) {
    /* Dashboard Layout */
    .dashboard-grid {
        grid-template-columns: 1fr !important;
        gap: 20px;
    }

    /* Chart containers */
    .chart-wrapper {
        height: 250px;
    }
}

@media (max-width: 992px) {
    /* Stats Grid */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px;
    }

    /* Sidebar Toggle Button */
    .sidebar-toggle {
        display: flex !important;
        align-items: center;
        justify-content: center;
        z-index: 1001;
    }

    /* Dashboard Sidebar */
    .dashboard-sidebar {
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: 100vh;
        background-color: var(--bg-color);
        z-index: 1000;
        overflow-y: auto;
        padding: 20px;
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
        transition: right 0.3s ease;
    }

    .dashboard-sidebar.active {
        right: 0;
    }

    /* Admin Actions */
    .admin-actions {
        flex-direction: column;
        align-items: stretch;
        width: 100%;
    }

    .admin-actions .btn {
        width: 100%;
        margin-bottom: 10px;
    }

    /* Table Responsiveness */
    .table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .data-table {
        min-width: 650px;
    }
}

@media (max-width: 768px) {


    /* Stats Grid */
    .stats-grid {
        grid-template-columns: 1fr !important;
    }

    /* Stat Cards */
    .stat-card {
        flex-direction: row;
        align-items: center;
        padding: 15px;
    }

    .stat-icon {
        margin-right: 15px;
        margin-bottom: 0;
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .stat-info {
        text-align: left;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    /* Chart Containers */
    .chart-wrapper {
        height: 200px;
    }

    /* Admin Header */
    .admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;

    }
}

@media (max-width: 576px) {
    /* Container Padding */
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }



    /* Dashboard Sidebar */
    .dashboard-sidebar {
        width: 100%;
        right: -100%;
    }

    /* Stats and Metrics */
    .stat-number {
        font-size: 1.3rem;
    }

    .metric-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .metric-label {
        width: 100%;
    }

    /* Table Actions */
    .actions {
        flex-wrap: wrap;
        gap: 5px;
    }

    .action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.9rem;
    }


}

/* Overlay for mobile sidebar */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

.sidebar-overlay.active {
    display: block;
}


