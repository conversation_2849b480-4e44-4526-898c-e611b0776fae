<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// This script fixes the functions.php file by ensuring it has proper PHP closing tags
// and no unexpected content at the end

echo "<h1>Functions.php Repair Tool</h1>";

$functionsFile = 'includes/functions.php';

if (!file_exists($functionsFile)) {
    echo "<p style='color: red;'>Error: functions.php file not found at: $functionsFile</p>";
    exit;
}

echo "<p>Found functions.php file. Checking content...</p>";

// Get the file content
$content = file_get_contents($functionsFile);
if ($content === false) {
    echo "<p style='color: red;'>Error: Could not read the functions.php file.</p>";
    exit;
}

echo "<p>File size: " . strlen($content) . " bytes</p>";

// Find the last function in the file
$lastFunctionPos = strrpos($content, 'function ');
if ($lastFunctionPos === false) {
    echo "<p style='color: red;'>Error: Could not find any functions in the file.</p>";
    exit;
}

echo "<p>Last function found at position: $lastFunctionPos</p>";

// Find the closing brace of the last function
$braceLevel = 0;
$lastClosingBrace = 0;
for ($i = $lastFunctionPos; $i < strlen($content); $i++) {
    if ($content[$i] === '{') {
        $braceLevel++;
    } elseif ($content[$i] === '}') {
        $braceLevel--;
        if ($braceLevel === 0) {
            $lastClosingBrace = $i;
            break;
        }
    }
}

if ($lastClosingBrace === 0) {
    echo "<p style='color: red;'>Error: Could not find the closing brace of the last function.</p>";
    exit;
}

echo "<p>Last function closing brace found at position: $lastClosingBrace</p>";

// Truncate the file at the last function's closing brace and add proper closing
$newContent = substr($content, 0, $lastClosingBrace + 1) . "\n// End of functions.php - No content should appear after this line\n?>";

// Write the fixed content back to the file
if (file_put_contents($functionsFile, $newContent)) {
    echo "<p style='color: green;'>Successfully fixed functions.php!</p>";
    echo "<p>New file size: " . strlen($newContent) . " bytes</p>";
    echo "<p>The file now ends with proper PHP closing tags and no extra content.</p>";
} else {
    echo "<p style='color: red;'>Error: Failed to write the fixed content back to functions.php.</p>";
    echo "<p>Please check file permissions and try again.</p>";
}

echo "<p><a href='install.php'>Go to installation page</a></p>";
?>
