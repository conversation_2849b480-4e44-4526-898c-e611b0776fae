/**
 * Simple Tracking Map with Leaflet Routing Machine
 * This file provides a clean implementation using Leaflet Routing Machine's built-in functionality
 */

// Initialize the tracking map when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Check if tracking map element exists
    const mapElement = document.getElementById('tracking-map');
    if (!mapElement) {
        console.error('Map element not found');
        return;
    }
    
    console.log('Initializing tracking map...');
    
    // Force map element to be visible with proper dimensions
    mapElement.style.height = '500px';
    mapElement.style.width = '100%';
    mapElement.style.position = 'relative';
    mapElement.style.zIndex = '1';
    mapElement.style.backgroundColor = '#f8f9fa';
    
    // Initialize the map
    const map = L.map('tracking-map', {
        zoomControl: false, // We'll add it in a different position
        center: [39.8283, -98.5795],
        zoom: 4,
        minZoom: 2,
        maxZoom: 18
    });
    
    // Add zoom control to the top-right corner
    L.control.zoom({
        position: 'topright'
    }).addTo(map);
    
    // Use OpenStreetMap tiles (more reliable)
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
    }).addTo(map);
    
    // Get tracking updates from the data attribute
    const trackingUpdatesJson = mapElement.getAttribute('data-tracking-updates');
    if (!trackingUpdatesJson) {
        console.error('No tracking updates found');
        return;
    }
    
    // Parse tracking updates
    let trackingUpdates;
    try {
        trackingUpdates = JSON.parse(trackingUpdatesJson);
        console.log('Tracking updates:', trackingUpdates);
    } catch (error) {
        console.error('Error parsing tracking updates:', error);
        return;
    }
    
    // Check if we have tracking updates
    if (!trackingUpdates || !Array.isArray(trackingUpdates) || trackingUpdates.length === 0) {
        console.error('Invalid tracking updates');
        return;
    }
    
    // Extract waypoints from tracking updates
    const waypoints = [];
    const waypointData = [];
    
    // Process tracking updates to extract waypoints
    trackingUpdates.forEach((update) => {
        // Skip updates without coordinates
        if (!update.latitude || !update.longitude) {
            return;
        }
        
        const lat = parseFloat(update.latitude);
        const lng = parseFloat(update.longitude);
        
        // Add to waypoints for routing
        waypoints.push(L.latLng(lat, lng));
        waypointData.push(update);
    });
    
    // If we have at least origin and destination
    if (waypoints.length >= 2) {
        // Create a loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'map-loading';
        loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Calculating route...';
        mapElement.appendChild(loadingIndicator);
        
        // Set a timeout for route calculation
        const routeTimeout = setTimeout(function() {
            console.log('Route calculation timed out');
            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }
            
            // Fall back to direct line
            createFallbackRoute(waypoints, waypointData, map);
            
            // Show error notification
            if (typeof showNotification === 'function') {
                showNotification('warning', 'Route calculation timed out. Showing approximate path instead.', 5000);
            } else {
                console.warn('Route calculation timed out. Showing approximate path instead.');
            }
        }, 10000); // 10 second timeout
        
        // Determine transportation mode based on shipment data
        let transportMode = 'driving';
        let routeColor = '#5c2be2'; // Default purple color
        
        // Try to get shipment data
        const shipmentDataJson = mapElement.getAttribute('data-shipment');
        if (shipmentDataJson) {
            try {
                const shipmentData = JSON.parse(shipmentDataJson);
                if (shipmentData.transport_mode) {
                    const mode = shipmentData.transport_mode.toLowerCase();
                    if (mode.includes('air') || mode.includes('flight')) {
                        transportMode = 'air';
                        routeColor = '#3366CC'; // Blue for air
                    } else if (mode.includes('sea') || mode.includes('ocean') || mode.includes('ship')) {
                        transportMode = 'sea';
                        routeColor = '#0077be'; // Ocean blue for sea
                    } else if (mode.includes('rail') || mode.includes('train')) {
                        transportMode = 'rail';
                        routeColor = '#333333'; // Dark gray for rail
                    }
                }
            } catch (error) {
                console.error('Error parsing shipment data:', error);
            }
        }
        
        console.log('Using transport mode:', transportMode);
        
        // Create different route types based on transport mode
        if (transportMode === 'air') {
            // For air transport, create a curved arc
            createAirRoute(waypoints, waypointData, map, routeColor);
            
            // Clear the timeout
            clearTimeout(routeTimeout);
            
            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }
        } 
        else if (transportMode === 'sea') {
            // For sea transport, create a route with wave pattern
            createSeaRoute(waypoints, waypointData, map, routeColor);
            
            // Clear the timeout
            clearTimeout(routeTimeout);
            
            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }
        }
        else if (transportMode === 'rail') {
            // For rail transport, create a straighter route
            createRailRoute(waypoints, waypointData, map, routeColor);
            
            // Clear the timeout
            clearTimeout(routeTimeout);
            
            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }
        }
        else {
            // For road transport, use Leaflet Routing Machine
            // Initialize Leaflet Routing Machine
            const routingControl = L.Routing.control({
                waypoints: waypoints,
                routeWhileDragging: false,
                showAlternatives: false,
                fitSelectedRoutes: true,
                show: false, // Don't show the routing interface
                lineOptions: {
                    styles: [
                        {color: routeColor, opacity: 0.8, weight: 5}
                    ],
                    addWaypoints: false,
                    extendToWaypoints: true,
                    missingRouteTolerance: 0
                },
                createMarker: function() {
                    return null; // Don't create default markers
                },
                router: L.Routing.osrm({
                    serviceUrl: 'https://router.project-osrm.org/route/v1',
                    profile: 'driving',
                    timeout: 8000 // 8 second timeout
                })
            }).addTo(map);
            
            // Handle route calculation
            routingControl.on('routesfound', function(e) {
                console.log('Routes found:', e.routes);
                
                // Clear the timeout since we found a route
                clearTimeout(routeTimeout);
                
                // Remove loading indicator
                if (loadingIndicator.parentNode) {
                    loadingIndicator.parentNode.removeChild(loadingIndicator);
                }
                
                // Check if we have a valid route
                if (e.routes && e.routes.length > 0) {
                    console.log('Valid route found with coordinates:', e.routes[0].coordinates.length);
                    
                    // Get the calculated route
                    const route = e.routes[0];
                    const routeCoordinates = route.coordinates;
                    
                    // Add custom styling to the route
                    enhanceRoute(routeCoordinates, waypointData, map, transportMode, routeColor);
                    
                    // Update shipment progress
                    updateShipmentProgress(waypointData, routeCoordinates);
                } else {
                    console.warn('No valid routes found');
                    
                    // Fall back to direct line
                    createFallbackRoute(waypoints, waypointData, map);
                }
            });
            
            // Handle routing errors
            routingControl.on('routingerror', function(e) {
                console.error('Routing error:', e.error);
                
                // Remove loading indicator
                if (loadingIndicator.parentNode) {
                    loadingIndicator.parentNode.removeChild(loadingIndicator);
                }
                
                // Clear the timeout
                clearTimeout(routeTimeout);
                
                // Fall back to direct line
                createFallbackRoute(waypoints, waypointData, map);
                
                // Show error notification
                if (typeof showNotification === 'function') {
                    showNotification('error', 'Could not calculate the exact route. Showing approximate path instead.', 5000);
                }
            });
        }
    } else {
        console.error('Not enough waypoints for routing');
    }
    
    console.log('Leaflet Routing Machine map initialized successfully');
});

/**
 * Create a curved arc for air routes
 */
function createAirRoute(waypoints, waypointData, map, routeColor) {
    if (waypoints.length < 2) return;
    
    const coordinates = [];
    const start = waypoints[0];
    const end = waypoints[waypoints.length - 1];
    
    // Create a curved arc for flight path
    const distance = start.distanceTo(end) / 1000; // km
    const midPoint = L.latLng(
        (start.lat + end.lat) / 2,
        (start.lng + end.lng) / 2
    );
    
    // Higher arc for longer distances
    const arcHeight = Math.min(distance * 0.15, 500) / 1000; // Max 500km height
    const numPoints = Math.max(Math.ceil(distance / 500), 10); // More points for longer distances
    
    // Generate arc points
    for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints;
        const lat = start.lat * (1 - t) * (1 - t) + midPoint.lat * 2 * (1 - t) * t + end.lat * t * t;
        const lng = start.lng * (1 - t) * (1 - t) + midPoint.lng * 2 * (1 - t) * t + end.lng * t * t;
        
        // Add altitude component (arc)
        const alt = Math.sin(Math.PI * t) * arcHeight;
        const adjustedLat = lat + alt;
        
        coordinates.push(L.latLng(adjustedLat, lng));
    }
    
    // Create the path with air route styling
    const path = L.polyline(coordinates, {
        color: routeColor,
        weight: 4,
        opacity: 0.8,
        lineJoin: 'round',
        dashArray: '10, 10',
        className: 'air-route'
    }).addTo(map);
    
    // Add airplane icons along the path
    L.polylineDecorator(path, {
        patterns: [
            {
                offset: 25,
                repeat: 300,
                symbol: L.Symbol.marker({
                    rotate: true,
                    markerOptions: {
                        icon: L.divIcon({
                            html: '<i class="fas fa-plane" style="color:' + routeColor + ';"></i>',
                            className: 'air-icon',
                            iconSize: [20, 20]
                        })
                    }
                })
            }
        ]
    }).addTo(map);
    
    // Add markers for each waypoint
    addWaypointMarkers(waypointData, map);
    
    // Fit map to path
    map.fitBounds(path.getBounds(), {
        padding: [50, 50]
    });
    
    // Update shipment progress
    updateShipmentProgress(waypointData, coordinates);
}

/**
 * Create a sea route with wave pattern
 */
function createSeaRoute(waypoints, waypointData, map, routeColor) {
    if (waypoints.length < 2) return;
    
    const coordinates = [];
    const start = waypoints[0];
    const end = waypoints[waypoints.length - 1];
    
    // Create a slightly curved path for sea routes
    const distance = start.distanceTo(end) / 1000; // km
    const numPoints = Math.max(Math.ceil(distance / 300), 8); // More points for longer distances
    
    // Generate curved path with small waves
    for (let i = 0; i <= numPoints; i++) {
        const t = i / numPoints;
        const lat = start.lat * (1 - t) + end.lat * t;
        const lng = start.lng * (1 - t) + end.lng * t;
        
        // Add small wave pattern
        const waveAmplitude = 0.05; // Small waves
        const waveFrequency = 10; // Higher frequency = more waves
        const wave = Math.sin(t * Math.PI * waveFrequency) * waveAmplitude;
        
        coordinates.push(L.latLng(lat + wave, lng));
    }
    
    // Create the path with sea route styling
    const path = L.polyline(coordinates, {
        color: routeColor,
        weight: 4,
        opacity: 0.8,
        lineJoin: 'round',
        dashArray: '5, 10',
        className: 'sea-route'
    }).addTo(map);
    
    // Add ship icons along the path
    L.polylineDecorator(path, {
        patterns: [
            {
                offset: 25,
                repeat: 300,
                symbol: L.Symbol.marker({
                    rotate: true,
                    markerOptions: {
                        icon: L.divIcon({
                            html: '<i class="fas fa-ship" style="color:' + routeColor + ';"></i>',
                            className: 'sea-icon',
                            iconSize: [20, 20]
                        })
                    }
                })
            }
        ]
    }).addTo(map);
    
    // Add markers for each waypoint
    addWaypointMarkers(waypointData, map);
    
    // Fit map to path
    map.fitBounds(path.getBounds(), {
        padding: [50, 50]
    });
    
    // Update shipment progress
    updateShipmentProgress(waypointData, coordinates);
}

/**
 * Create a rail route (straighter than road)
 */
function createRailRoute(waypoints, waypointData, map, routeColor) {
    if (waypoints.length < 2) return;
    
    const coordinates = [];
    
    // Process each segment between waypoints
    for (let i = 0; i < waypoints.length - 1; i++) {
        const start = waypoints[i];
        const end = waypoints[i + 1];
        
        // Calculate distance for this segment
        const segmentDistance = start.distanceTo(end) / 1000; // km
        
        // For longer segments, add intermediate points to simulate rail lines
        const numPoints = Math.max(Math.ceil(segmentDistance / 100), 4); // Fewer points for straighter lines
        
        // Generate points with very slight randomness to simulate rail lines (straighter than roads)
        for (let j = 0; j <= numPoints; j++) {
            const t = j / numPoints;
            
            // Linear interpolation between start and end
            let lat = start.lat * (1 - t) + end.lat * t;
            let lng = start.lng * (1 - t) + end.lng * t;
            
            // Add very small random deviation to simulate rail lines (much straighter than roads)
            if (j > 0 && j < numPoints) {
                const deviation = 0.005 * Math.sin(t * Math.PI); // Very small deviation
                lat += (Math.random() * 2 - 1) * deviation;
                lng += (Math.random() * 2 - 1) * deviation;
            }
            
            coordinates.push(L.latLng(lat, lng));
        }
    }
    
    // Create the path with rail route styling
    const path = L.polyline(coordinates, {
        color: routeColor,
        weight: 4,
        opacity: 0.8,
        lineJoin: 'round',
        dashArray: '15, 10',
        className: 'rail-route'
    }).addTo(map);
    
    // Add train icons along the path
    L.polylineDecorator(path, {
        patterns: [
            {
                offset: 25,
                repeat: 300,
                symbol: L.Symbol.marker({
                    rotate: true,
                    markerOptions: {
                        icon: L.divIcon({
                            html: '<i class="fas fa-train" style="color:' + routeColor + ';"></i>',
                            className: 'rail-icon',
                            iconSize: [20, 20]
                        })
                    }
                })
            }
        ]
    }).addTo(map);
    
    // Add markers for each waypoint
    addWaypointMarkers(waypointData, map);
    
    // Fit map to path
    map.fitBounds(path.getBounds(), {
        padding: [50, 50]
    });
    
    // Update shipment progress
    updateShipmentProgress(waypointData, coordinates);
}

/**
 * Create a fallback route when routing fails
 */
function createFallbackRoute(waypoints, waypointData, map) {
    console.log('Creating fallback route');
    
    // Create a smoother path between waypoints
    let smoothPath = [];
    
    if (waypoints.length === 2) {
        // If only origin and destination, create a curved line
        const origin = waypoints[0];
        const destination = waypoints[1];
        
        // Calculate midpoint with slight offset for curve
        const midLat = (origin.lat + destination.lat) / 2;
        const midLng = (origin.lng + destination.lng) / 2;
        
        // Add some curvature based on distance
        const distance = origin.distanceTo(destination) / 1000; // km
        const curveOffset = Math.min(distance * 0.05, 0.5); // Max 0.5 degrees offset
        
        // Create curved path
        smoothPath = [
            origin,
            L.latLng(midLat + curveOffset, midLng - curveOffset),
            destination
        ];
    } else if (waypoints.length > 2) {
        // For multiple waypoints, connect them with slight curves
        for (let i = 0; i < waypoints.length - 1; i++) {
            const start = waypoints[i];
            const end = waypoints[i + 1];
            
            smoothPath.push(start);
            
            // Add intermediate point for slight curve if not the last segment
            if (i < waypoints.length - 2) {
                const midLat = (start.lat + end.lat) / 2;
                const midLng = (start.lng + end.lng) / 2;
                const distance = start.distanceTo(end) / 1000; // km
                const curveOffset = Math.min(distance * 0.03, 0.3); // Smaller offset
                
                // Alternate the curve direction for more natural look
                const offsetSign = i % 2 === 0 ? 1 : -1;
                smoothPath.push(L.latLng(midLat + curveOffset * offsetSign, midLng - curveOffset * offsetSign));
            }
        }
        
        // Add the final point
        smoothPath.push(waypoints[waypoints.length - 1]);
    } else {
        // Just use the waypoints as is if there's only one or none
        smoothPath = waypoints;
    }
    
    // Create a regular polyline with CSS animation
    const path = L.polyline(smoothPath, {
        color: '#5c2be2',
        weight: 5,
        opacity: 0.8,
        lineJoin: 'round',
        className: 'ant-path',
        dashArray: '10, 20'
    }).addTo(map);
    
    // Add direction arrows
    L.polylineDecorator(path, {
        patterns: [
            {
                offset: 25,
                repeat: 150,
                symbol: L.Symbol.arrowHead({
                    pixelSize: 15,
                    polygon: false,
                    pathOptions: {
                        stroke: true,
                        color: '#fff',
                        weight: 2
                    }
                })
            }
        ]
    }).addTo(map);
    
    // Add markers for each waypoint
    addWaypointMarkers(waypointData, map);
    
    // Fit map to path
    map.fitBounds(path.getBounds(), {
        padding: [50, 50]
    });
    
    // Update shipment progress
    updateShipmentProgress(waypointData, smoothPath);
}

/**
 * Add custom styling to the route
 */
function enhanceRoute(routeCoordinates, waypointData, map, transportMode, routeColor) {
    // Create a polyline with the ant path animation
    const path = L.polyline(routeCoordinates, {
        color: routeColor,
        weight: 5,
        opacity: 0.8,
        lineJoin: 'round',
        className: 'road-route ant-path',
        dashArray: '10, 20'
    }).addTo(map);
    
    // Add direction arrows for road transport
    L.polylineDecorator(path, {
        patterns: [
            {
                offset: 25,
                repeat: 150,
                symbol: L.Symbol.arrowHead({
                    pixelSize: 15,
                    polygon: false,
                    pathOptions: {
                        stroke: true,
                        color: '#fff',
                        weight: 2
                    }
                })
            }
        ]
    }).addTo(map);
    
    // Try with AntPath if available
    if (typeof L.Polyline.AntPath === 'function') {
        try {
            // Create the ant path with animation
            const antPath = L.polyline.antPath(routeCoordinates, {
                delay: 800,
                dashArray: [10, 20],
                weight: 5,
                color: routeColor,
                pulseColor: "#00d45f",
                paused: false,
                reverse: false,
                hardwareAccelerated: true
            });
            
            // Add it to the map but make it less visible
            antPath.setStyle({ opacity: 0.5 }).addTo(map);
            console.log('Added AntPath for road transport');
        } catch (e) {
            console.error('Error creating AntPath:', e);
        }
    }
    
    // Add markers for each waypoint
    addWaypointMarkers(waypointData, map);
}

/**
 * Add markers for each waypoint
 */
function addWaypointMarkers(waypointData, map) {
    // Define marker icons
    const markerIcons = {
        origin: L.icon({
            iconUrl: '../assets/images/marker-origin.png',
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32]
        }),
        transit: L.icon({
            iconUrl: '../assets/images/marker-transit.png',
            iconSize: [24, 24],
            iconAnchor: [12, 24],
            popupAnchor: [0, -24]
        }),
        destination: L.icon({
            iconUrl: '../assets/images/marker-destination.png',
            iconSize: [32, 32],
            iconAnchor: [16, 32],
            popupAnchor: [0, -32]
        })
    };
    
    // Add markers for each waypoint
    waypointData.forEach((update, index) => {
        const lat = parseFloat(update.latitude);
        const lng = parseFloat(update.longitude);
        
        // Determine icon based on position
        let icon;
        if (index === 0) {
            icon = markerIcons.origin;
        } else if (index === waypointData.length - 1) {
            icon = markerIcons.destination;
        } else {
            icon = markerIcons.transit;
        }
        
        // Create marker
        const marker = L.marker([lat, lng], {
            icon: icon,
            title: update.location
        }).addTo(map);
        
        // Add popup with information
        const timestamp = new Date(update.timestamp);
        const formattedDate = timestamp.toLocaleDateString();
        const formattedTime = timestamp.toLocaleTimeString();
        
        marker.bindPopup(`
            <div class="map-info-window">
                <h3>${update.status}</h3>
                <p><strong>Location:</strong> ${update.location}</p>
                <p><strong>Time:</strong> ${formattedDate} at ${formattedTime}</p>
                ${update.notes ? `<p><strong>Notes:</strong> ${update.notes}</p>` : ''}
            </div>
        `);
    });
    
    // Add labels for origin and destination
    // Origin label
    L.marker([parseFloat(waypointData[0].latitude), parseFloat(waypointData[0].longitude)], {
        icon: L.divIcon({
            className: 'map-label',
            html: '<div>Origin</div>',
            iconSize: [80, 20],
            iconAnchor: [40, -10]
        })
    }).addTo(map);
    
    // Destination label
    const lastPoint = waypointData[waypointData.length - 1];
    L.marker([parseFloat(lastPoint.latitude), parseFloat(lastPoint.longitude)], {
        icon: L.divIcon({
            className: 'map-label',
            html: '<div>Current Location</div>',
            iconSize: [120, 20],
            iconAnchor: [60, -10]
        })
    }).addTo(map);
}

/**
 * Update shipment progress based on route
 */
function updateShipmentProgress(waypointData, routeCoordinates) {
    // Calculate progress based on waypoint position
    const progressElement = document.getElementById('shipment-progress-bar');
    if (progressElement) {
        // Calculate progress based on waypoint position
        const lastIndex = waypointData.length - 1;
        const progressPercentage = Math.round((lastIndex / Math.max(1, waypointData.length - 1)) * 100);
        progressElement.style.width = `${progressPercentage}%`;
        
        // Update progress text
        const progressTextElement = document.getElementById('progress-percentage');
        if (progressTextElement) {
            progressTextElement.textContent = `${progressPercentage}%`;
        }
    }
    
    // Calculate total distance
    let totalDistance = 0;
    if (routeCoordinates && routeCoordinates.length > 1) {
        for (let i = 0; i < routeCoordinates.length - 1; i++) {
            if (routeCoordinates[i] && routeCoordinates[i+1]) {
                totalDistance += routeCoordinates[i].distanceTo(routeCoordinates[i+1]);
            }
        }
    }
    
    // Add distance info
    const distanceInfoElement = document.getElementById('distance-info');
    if (distanceInfoElement) {
        // Convert meters to miles (approximate)
        const distanceMiles = Math.round(totalDistance / 1609.34);
        distanceInfoElement.innerHTML = `<i class="fas fa-route"></i> Total Distance: ~${distanceMiles} miles`;
    } else {
        // Create distance info if it doesn't exist
        const distanceInfo = L.control({position: 'bottomleft'});
        distanceInfo.onAdd = function() {
            const div = L.DomUtil.create('div', 'distance-info');
            // Convert meters to miles (approximate)
            const distanceMiles = Math.round(totalDistance / 1609.34);
            div.innerHTML = `<i class="fas fa-route"></i> Total Distance: ~${distanceMiles} miles`;
            div.id = 'distance-info';
            return div;
        };
        distanceInfo.addTo(map);
    }
}
