/**
 * Mobile Navigation Disturbance Effects
 * Creates visual effects when navigation items scroll through the centered logo
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run on mobile devices
    if (window.innerWidth > 768) return;

    const mobileNavContainer = document.querySelector('.mobile-nav-container');
    const logoContainer = document.querySelector('.mobile-logo-container');
    const navItems = document.querySelectorAll('.mobile-nav-item');

    // Debug logging
    console.log('Mobile Nav Effects - Elements found:', {
        container: !!mobileNavContainer,
        logo: !!logoContainer,
        itemCount: navItems.length
    });

    if (!mobileNavContainer || !logoContainer || navItems.length === 0) {
        console.warn('Mobile Nav Effects - Missing required elements');
        return;
    }

    // Configuration
    const DISTURBANCE_THRESHOLD = 40; // Distance from logo center to trigger effect
    const SCROLL_THROTTLE = 16; // ~60fps
    const LOGO_PULSE_DURATION = 300; // Duration of logo pulse effect

    let isScrolling = false;
    let scrollTimeout;

    /**
     * Calculate if a navigation item is passing through the logo area
     */
    function checkItemsPassingThrough() {
        if (!logoContainer || !mobileNavContainer) return;

        const logoRect = logoContainer.getBoundingClientRect();
        const logoCenterX = window.innerWidth / 2; // Logo is always centered

        let hasDisturbance = false;

        navItems.forEach(item => {
            const itemRect = item.getBoundingClientRect();
            const itemCenterX = itemRect.left + itemRect.width / 2;
            const distanceFromLogo = Math.abs(itemCenterX - logoCenterX);

            // Check if item is passing through logo area (horizontally aligned)
            if (distanceFromLogo < DISTURBANCE_THRESHOLD &&
                itemRect.bottom > logoRect.top &&
                itemRect.top < logoRect.bottom) {

                item.classList.add('passing-through');
                hasDisturbance = true;

                // Add additional effects based on proximity
                const proximityFactor = 1 - (distanceFromLogo / DISTURBANCE_THRESHOLD);
                const blur = proximityFactor * 1.5;
                const scale = 0.85 + (proximityFactor * 0.1);
                const rotation = proximityFactor * 8;

                item.style.filter = `blur(${blur}px)`;
                item.style.transform = `scale(${scale}) translateY(-${proximityFactor * 3}px) rotate(${rotation}deg)`;
                item.style.opacity = 0.7 - (proximityFactor * 0.3);

            } else {
                item.classList.remove('passing-through');
                item.style.filter = '';
                item.style.transform = '';
                item.style.opacity = '';
            }
        });

        // Apply disturbance effect to logo
        if (hasDisturbance) {
            logoContainer.classList.add('disturbed');

            // Add a pulse effect to the logo
            const logoIcon = logoContainer.querySelector('.logo-icon-bg');
            if (logoIcon) {
                logoIcon.style.animation = 'pulse 0.3s ease-in-out';
                setTimeout(() => {
                    if (logoIcon.style.animation === 'pulse 0.3s ease-in-out') {
                        logoIcon.style.animation = '';
                    }
                }, LOGO_PULSE_DURATION);
            }
        } else {
            logoContainer.classList.remove('disturbed');
        }
    }

    /**
     * Throttled scroll handler
     */
    function handleScroll() {
        if (!isScrolling) {
            requestAnimationFrame(checkItemsPassingThrough);
            isScrolling = true;
        }

        // Clear the timeout throughout the scroll
        clearTimeout(scrollTimeout);
        
        // Set a timeout to run after scrolling ends
        scrollTimeout = setTimeout(() => {
            isScrolling = false;
            checkItemsPassingThrough(); // Final check
        }, SCROLL_THROTTLE);
    }

    /**
     * Enhanced scroll behavior with momentum
     */
    function enhanceScrollBehavior() {
        let startX = 0;
        let scrollLeft = 0;
        let isDown = false;
        let velocity = 0;
        let lastX = 0;
        let lastTime = 0;

        mobileNavContainer.addEventListener('touchstart', (e) => {
            isDown = true;
            startX = e.touches[0].pageX - mobileNavContainer.offsetLeft;
            scrollLeft = mobileNavContainer.scrollLeft;
            lastX = e.touches[0].pageX;
            lastTime = Date.now();
            velocity = 0;
        });

        mobileNavContainer.addEventListener('touchmove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            
            const x = e.touches[0].pageX - mobileNavContainer.offsetLeft;
            const walk = (x - startX) * 1.5; // Scroll speed multiplier
            mobileNavContainer.scrollLeft = scrollLeft - walk;
            
            // Calculate velocity for momentum
            const currentTime = Date.now();
            const deltaTime = currentTime - lastTime;
            const deltaX = e.touches[0].pageX - lastX;
            
            if (deltaTime > 0) {
                velocity = deltaX / deltaTime;
            }
            
            lastX = e.touches[0].pageX;
            lastTime = currentTime;
            
            checkItemsPassingThrough();
        });

        mobileNavContainer.addEventListener('touchend', () => {
            isDown = false;
            
            // Apply momentum scrolling
            if (Math.abs(velocity) > 0.1) {
                const momentum = velocity * 100; // Momentum factor
                const targetScroll = mobileNavContainer.scrollLeft - momentum;
                
                mobileNavContainer.scrollTo({
                    left: Math.max(0, Math.min(targetScroll, mobileNavContainer.scrollWidth - mobileNavContainer.clientWidth)),
                    behavior: 'smooth'
                });
            }
        });
    }

    /**
     * Initialize navigation positioning
     */
    function initializeNavigation() {
        // Initial check for items passing through
        setTimeout(checkItemsPassingThrough, 100);
    }

    /**
     * Add visual feedback for active items
     */
    function enhanceActiveStates() {
        navItems.forEach(item => {
            item.addEventListener('touchstart', () => {
                item.style.transform = 'scale(0.95)';
            });
            
            item.addEventListener('touchend', () => {
                setTimeout(() => {
                    if (!item.classList.contains('passing-through')) {
                        item.style.transform = '';
                    }
                }, 150);
            });
        });
    }

    // Initialize all effects
    mobileNavContainer.addEventListener('scroll', handleScroll, { passive: true });
    enhanceScrollBehavior();
    enhanceActiveStates();

    // Delay initial setup to ensure all elements are ready
    setTimeout(() => {
        initializeNavigation();
        console.log('Mobile Nav Effects - Initialized successfully');
    }, 500);

    // Handle orientation changes
    window.addEventListener('orientationchange', () => {
        setTimeout(() => {
            checkItemsPassingThrough();
        }, 300);
    });

    // Handle window resize
    window.addEventListener('resize', () => {
        if (window.innerWidth <= 768) {
            setTimeout(() => {
                checkItemsPassingThrough();
            }, 100);
        }
    });

    // Retry initialization if elements are not ready
    let retryCount = 0;
    const maxRetries = 5;

    function retryInitialization() {
        if (retryCount >= maxRetries) return;

        const updatedNavItems = document.querySelectorAll('.mobile-nav-item');
        const updatedLogoContainer = document.querySelector('.mobile-logo-container');

        if (updatedNavItems.length > 0 && updatedLogoContainer) {
            console.log('Mobile Nav Effects - Retry successful');
            return;
        }

        retryCount++;
        setTimeout(retryInitialization, 1000);
    }

    // Start retry mechanism
    setTimeout(retryInitialization, 1000);
});
