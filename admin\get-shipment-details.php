<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if(!isLoggedIn()) {
    echo '<div class="error">Authentication required</div>';
    exit;
}

// Get shipment ID
$shipmentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($shipmentId <= 0) {
    echo '<div class="error">Invalid shipment ID</div>';
    exit;
}

// Get shipment details
$db->query("SELECT * FROM shipments WHERE id = :id");
$db->bind(':id', $shipmentId);
$shipment = $db->single();

if (!$shipment) {
    echo '<div class="error">Shipment not found</div>';
    exit;
}

// Get tracking updates
$db->query("SELECT * FROM tracking_updates WHERE shipment_id = :shipment_id ORDER BY timestamp DESC");
$db->bind(':shipment_id', $shipmentId);
$updates = $db->resultSet();
?>

<div class="shipment-details">
    <div class="details-section">
        <h3>
            <svg xmlns="http://www.w3.org/2000/svg" class="heroicon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"></rect><circle cx="9" cy="9" r="2"></circle><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"></path></svg>
            Package Picture
        </h3>
        <div class="package-picture">
            <?php if (!empty($shipment['package_picture'])): ?>
                <img src="<?php echo SITE_URL; ?>/uploads/packages/<?php echo $shipment['package_picture']; ?>" alt="Package Picture" class="img-fluid">
            <?php else: ?>
                <img src="<?php echo SITE_URL; ?>/assets/images/defaults/package-placeholder.svg" alt="Default Package" class="img-fluid default-package-image">
            <?php endif; ?>
        </div>
    </div>

    <div class="details-section">
        <h3>Shipment Information</h3>
        <div class="details-grid">
            <div class="detail-item">
                <div class="detail-label">Tracking Number</div>
                <div class="detail-value"><?php echo $shipment['tracking_number']; ?></div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Customer</div>
                <div class="detail-value"><?php echo $shipment['customer_name']; ?></div>
            </div>
            <?php if (!empty($shipment['shipment_name'])): ?>
            <div class="detail-item">
                <div class="detail-label">Shipment Name</div>
                <div class="detail-value"><?php echo $shipment['shipment_name']; ?></div>
            </div>
            <?php endif; ?>
            <?php if (!empty($shipment['shipment_description'])): ?>
            <div class="detail-item">
                <div class="detail-label">Shipment Description</div>
                <div class="detail-value"><?php echo $shipment['shipment_description']; ?></div>
            </div>
            <?php endif; ?>
            <div class="detail-item">
                <div class="detail-label">Origin</div>
                <div class="detail-value"><?php echo $shipment['origin']; ?></div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Destination</div>
                <div class="detail-value"><?php echo $shipment['destination']; ?></div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Status</div>
                <div class="detail-value">
                    <span class="badge <?php echo getStatusClass($shipment['status']); ?>">
                        <?php echo ucfirst(str_replace('_', ' ', $shipment['status'])); ?>
                    </span>
                </div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Created</div>
                <div class="detail-value"><?php echo formatDate($shipment['created_at']); ?></div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Estimated Delivery</div>
                <div class="detail-value"><?php echo !empty($shipment['estimated_delivery']) ? date('F j, Y', strtotime($shipment['estimated_delivery'])) : 'N/A'; ?></div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Delivered</div>
                <div class="detail-value"><?php echo !empty($shipment['delivered_at']) ? formatDate($shipment['delivered_at']) : 'Not delivered yet'; ?></div>
            </div>
            <?php if (!empty($shipment['shipping_service'])): ?>
            <div class="detail-item">
                <div class="detail-label">Shipping Service</div>
                <div class="detail-value"><?php echo $shipment['shipping_service']; ?></div>
            </div>
            <?php endif; ?>
            <?php if (!empty($shipment['shipping_cost'])): ?>
            <div class="detail-item">
                <div class="detail-label">Shipping Cost</div>
                <div class="detail-value">$<?php echo number_format($shipment['shipping_cost'], 2); ?></div>
            </div>
            <?php endif; ?>
            <?php if (!empty($shipment['package_name'])): ?>
            <div class="detail-item">
                <div class="detail-label">Package Name</div>
                <div class="detail-value"><?php echo $shipment['package_name']; ?></div>
            </div>
            <?php endif; ?>
            <?php if (!empty($shipment['package_description'])): ?>
            <div class="detail-item">
                <div class="detail-label">Package Description</div>
                <div class="detail-value"><?php echo $shipment['package_description']; ?></div>
            </div>
            <?php endif; ?>
            <?php if (!empty($shipment['package_weight'])): ?>
            <div class="detail-item">
                <div class="detail-label">Package Weight</div>
                <div class="detail-value"><?php echo $shipment['package_weight']; ?> kg</div>
            </div>
            <?php endif; ?>
            <?php if (!empty($shipment['package_dimensions'])): ?>
            <div class="detail-item">
                <div class="detail-label">Package Dimensions</div>
                <div class="detail-value"><?php echo $shipment['package_dimensions']; ?> cm</div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="details-section">
        <h3>Shopper Information</h3>
        <?php if (empty($shipment['shopper_name']) && empty($shipment['shopper_email']) && empty($shipment['shopper_phone']) && empty($shipment['shopper_address'])): ?>
            <div class="empty-state">
                <p>No shopper information available</p>
            </div>
        <?php else: ?>
            <div class="details-grid">
                <?php if (!empty($shipment['shopper_name'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Name</div>
                    <div class="detail-value"><?php echo $shipment['shopper_name']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($shipment['shopper_email'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Email</div>
                    <div class="detail-value"><?php echo $shipment['shopper_email']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($shipment['shopper_phone'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Phone</div>
                    <div class="detail-value"><?php echo $shipment['shopper_phone']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($shipment['shopper_address'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Address</div>
                    <div class="detail-value"><?php echo $shipment['shopper_address']; ?></div>
                </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="details-section">
        <h3>Receiver Information</h3>
        <?php if (empty($shipment['receiver_name']) && empty($shipment['receiver_email']) && empty($shipment['receiver_phone']) && empty($shipment['receiver_address'])): ?>
            <div class="empty-state">
                <p>No receiver information available</p>
            </div>
        <?php else: ?>
            <div class="details-grid">
                <?php if (!empty($shipment['receiver_name'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Name</div>
                    <div class="detail-value"><?php echo $shipment['receiver_name']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($shipment['receiver_email'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Email</div>
                    <div class="detail-value"><?php echo $shipment['receiver_email']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($shipment['receiver_phone'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Phone</div>
                    <div class="detail-value"><?php echo $shipment['receiver_phone']; ?></div>
                </div>
                <?php endif; ?>
                <?php if (!empty($shipment['receiver_address'])): ?>
                <div class="detail-item">
                    <div class="detail-label">Address</div>
                    <div class="detail-value"><?php echo $shipment['receiver_address']; ?></div>
                </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <div class="details-section">
        <h3>Tracking Updates</h3>
        <?php if (empty($updates)): ?>
            <div class="empty-state">
                <p>No tracking updates found</p>
            </div>
        <?php else: ?>
            <div class="tracking-timeline">
                <?php foreach ($updates as $update): ?>
                    <div class="timeline-item">
                        <div class="timeline-marker <?php echo getStatusClass($update['status']); ?>"></div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <div class="timeline-title"><?php echo $update['location']; ?></div>
                                <div class="timeline-time"><?php echo formatDate($update['timestamp']); ?></div>
                            </div>
                            <div class="timeline-status">
                                <span class="badge <?php echo getStatusClass($update['status']); ?>">
                                    <?php echo ucfirst(str_replace('_', ' ', $update['status'])); ?>
                                </span>
                            </div>
                            <?php if (!empty($update['notes'])): ?>
                                <div class="timeline-notes"><?php echo $update['notes']; ?></div>
                            <?php endif; ?>
                            <?php if (!empty($update['latitude']) && !empty($update['longitude'])): ?>
                                <div class="timeline-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <a href="https://www.google.com/maps?q=<?php echo $update['latitude']; ?>,<?php echo $update['longitude']; ?>" target="_blank">
                                        View on Map
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>
