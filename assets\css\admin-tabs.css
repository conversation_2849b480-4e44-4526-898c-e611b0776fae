/* Form Tabs Styling */
.form-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.tab-btn {
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    color: var(--text-color);
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    color: var(--primary-color);
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.tab-content h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.2rem;
}

/* Package Picture Styling */
.package-picture {
    margin-bottom: 20px;
    text-align: center;
}

.package-picture img {
    max-width: 100%;
    max-height: 300px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

#current_package_picture {
    margin-bottom: 10px;
}

#current_package_picture img {
    max-height: 150px;
    border-radius: 8px;
}

/* Form Text Styling */
.form-text {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 5px;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Dark Theme Overrides */
.dark-theme .form-tabs {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .tab-btn {
    color: var(--text-color);
}

.dark-theme .tab-btn:hover,
.dark-theme .tab-btn.active {
    color: var(--primary-color);
}

.dark-theme .form-text {
    color: #adb5bd;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .form-tabs {
        padding-bottom: 5px;
    }
    
    .tab-btn {
        padding: 8px 12px;
        font-size: 0.9rem;
    }
    
    .tab-content h3 {
        font-size: 1.1rem;
    }
}

/* Modal Size Adjustments */
.modal-lg {
    width: 90%;
    max-width: 900px;
}

@media (max-width: 992px) {
    .modal-lg {
        width: 95%;
    }
}

@media (max-width: 576px) {
    .modal-lg {
        width: 100%;
        margin: 0;
        border-radius: 0;
        height: 100vh;
        max-height: 100vh;
    }
}
