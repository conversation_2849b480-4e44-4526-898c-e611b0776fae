<?php
/**
 * Shipment Overview Module
 * 
 * Displays shipment statistics and overview information
 */

// If stats are not passed, fetch them
if (!isset($stats)) {
    // Get shipment statistics
    $db->query("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                COUNT(DISTINCT origin) as origin_count,
                COUNT(DISTINCT destination) as destination_count,
                COUNT(DISTINCT customer_name) as customer_count
                FROM shipments");
    $stats = $db->single();

    // Debug: Check what statuses exist in the database
    $db->query("SELECT DISTINCT status FROM shipments");
    $existingStatuses = $db->resultSet();

    // If query failed, initialize with default values
    if (!$stats) {
        $stats = [
            'total' => 0,
            'pending' => 0,
            'in_transit' => 0,
            'delivered' => 0,
            'delayed' => 0,
            'cancelled' => 0,
            'origin_count' => 0,
            'destination_count' => 0,
            'customer_count' => 0
        ];
    }
}

// If current month stats are not passed, fetch them
if (!isset($currentMonthStats)) {
    // Get shipment statistics for current month
    $currentMonth = date('Y-m');
    $db->query("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM shipments
                WHERE DATE_FORMAT(created_at, '%Y-%m') = :current_month");
    $db->bind(':current_month', $currentMonth);
    $currentMonthStats = $db->single();

    // If query failed, initialize with default values
    if (!$currentMonthStats) {
        $currentMonthStats = [
            'total' => 0,
            'pending' => 0,
            'in_transit' => 0,
            'delivered' => 0,
            'delayed' => 0,
            'cancelled' => 0
        ];
    }
}

// If monthly growth is not calculated, calculate it
if (!isset($monthlyGrowth)) {
    // Calculate month-over-month growth
    $lastMonth = date('Y-m', strtotime('-1 month'));
    $db->query("SELECT COUNT(*) as total FROM shipments WHERE DATE_FORMAT(created_at, '%Y-%m') = :last_month");
    $db->bind(':last_month', $lastMonth);
    $lastMonthResult = $db->single();
    $lastMonthTotal = $lastMonthResult['total'] ?? 0;

    $monthlyGrowth = 0;
    if ($lastMonthTotal > 0) {
        $monthlyGrowth = (($currentMonthStats['total'] - $lastMonthTotal) / $lastMonthTotal) * 100;
    }
}
?>

<!-- Stats Overview -->
<div class="stats-container">
    <div class="stats-header">
        <h2>Shipment Overview</h2>
        <div class="header-actions">
            <button class="refresh-stats" title="Refresh Statistics" data-module="shipment_overview"><i class="fas fa-sync-alt"></i></button>
            <a href="manage-shipments.php" class="view-all">Manage Shipments</a>
        </div>
    </div>

    <!-- Overall Stats -->
    <div class="stats-section">
        <h3 class="stats-subheader">Overall Statistics</h3>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div class="stat-info">
                    <h3>Total Shipments</h3>
                    <p class="stat-number"><?php echo $stats['total'] ?? 0; ?></p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                    <h3>Pending</h3>
                    <p class="stat-number"><?php echo $stats['pending'] ?? 0; ?></p>
                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['pending'])) ? round(($stats['pending'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon transit">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <div class="stat-info">
                    <h3>In Transit</h3>
                    <p class="stat-number"><?php echo $stats['in_transit'] ?? 0; ?></p>
                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['in_transit'])) ? round(($stats['in_transit'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon delivered">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-info">
                    <h3>Delivered</h3>
                    <p class="stat-number"><?php echo $stats['delivered'] ?? 0; ?></p>
                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['delivered'])) ? round(($stats['delivered'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon delayed">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="stat-info">
                    <h3>Delayed</h3>
                    <p class="stat-number"><?php echo $stats['delayed'] ?? 0; ?></p>
                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['delayed'])) ? round(($stats['delayed'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                </div>
            </div>

            <div class="stat-card">
                <div class="stat-icon cancelled">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="stat-info">
                    <h3>Cancelled</h3>
                    <p class="stat-number"><?php echo $stats['cancelled'] ?? 0; ?></p>
                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['cancelled'])) ? round(($stats['cancelled'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Month Stats -->
    <div class="stats-section">
        <h3 class="stats-subheader">Current Month (<?php echo date('F Y'); ?>)</h3>
        <div class="stats-summary">
            <div class="summary-row">
                <div class="summary-label">Total Shipments</div>
                <div class="summary-value"><?php echo $currentMonthStats['total'] ?? 0; ?></div>
            </div>
            <div class="summary-row">
                <div class="summary-label">Month-over-Month Growth</div>
                <div class="summary-value <?php echo $monthlyGrowth >= 0 ? 'positive' : 'negative'; ?>">
                    <?php echo round($monthlyGrowth, 1); ?>%
                    <i class="fas <?php echo $monthlyGrowth >= 0 ? 'fa-arrow-up' : 'fa-arrow-down'; ?>"></i>
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-label">Delivered</div>
                <div class="summary-value"><?php echo $currentMonthStats['delivered'] ?? 0; ?> (<?php echo (isset($currentMonthStats['total']) && $currentMonthStats['total'] > 0 && isset($currentMonthStats['delivered'])) ? round(($currentMonthStats['delivered'] / $currentMonthStats['total']) * 100, 1) : 0; ?>%)</div>
            </div>
            <div class="summary-row">
                <div class="summary-label">In Transit</div>
                <div class="summary-value"><?php echo $currentMonthStats['in_transit'] ?? 0; ?> (<?php echo (isset($currentMonthStats['total']) && $currentMonthStats['total'] > 0 && isset($currentMonthStats['in_transit'])) ? round(($currentMonthStats['in_transit'] / $currentMonthStats['total']) * 100, 1) : 0; ?>%)</div>
            </div>
            <div class="summary-row">
                <div class="summary-label">Delayed</div>
                <div class="summary-value"><?php echo $currentMonthStats['delayed'] ?? 0; ?> (<?php echo (isset($currentMonthStats['total']) && $currentMonthStats['total'] > 0 && isset($currentMonthStats['delayed'])) ? round(($currentMonthStats['delayed'] / $currentMonthStats['total']) * 100, 1) : 0; ?>%)</div>
            </div>
        </div>
    </div>

    <!-- Additional Stats -->
    <div class="stats-section">
        <h3 class="stats-subheader">Network Coverage</h3>
        <div class="stats-summary">
            <div class="summary-row">
                <div class="summary-label">Origin Locations</div>
                <div class="summary-value"><?php echo $stats['origin_count'] ?? 0; ?></div>
            </div>
            <div class="summary-row">
                <div class="summary-label">Destination Locations</div>
                <div class="summary-value"><?php echo $stats['destination_count'] ?? 0; ?></div>
            </div>
            <div class="summary-row">
                <div class="summary-label">Total Customers</div>
                <div class="summary-value"><?php echo $stats['customer_count'] ?? 0; ?></div>
            </div>
        </div>
    </div>
</div>
