// Initialize the tracking map
document.addEventListener('DOMContentLoaded', function() {
    console.log('Tracking map script loaded');

    // Check if tracking map element exists
    const mapElement = document.getElementById('tracking-map');
    if (!mapElement) {
        console.log('Map element not found');
        return;
    }

    // Check if tracking updates exist
    if (typeof trackingUpdates === 'undefined') {
        console.log('No tracking updates defined');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    console.log('Tracking updates:', trackingUpdates);

    // Function to check if any tracking update has coordinates
    function hasCoordinates(updates) {
        if (!updates || updates.length === 0) return false;

        for (let i = 0; i < updates.length; i++) {
            if (updates[i].latitude && updates[i].longitude) {
                return true;
            }
        }
        return false;
    }

    // Check if tracking updates have coordinates
    if (!hasCoordinates(trackingUpdates)) {
        console.log('No coordinates found in tracking updates');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    // Create map options
    const mapOptions = {
        zoom: 4,
        zoomControl: true,
        scrollWheelZoom: true,
        fullscreenControl: true,
        attributionControl: true,
    };

    // Map provider options
    const mapProviders = {
        osm: {
            name: 'OpenStreetMap',
            url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        },
        cartoDB: {
            name: 'CartoDB Voyager',
            url: 'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, &copy; <a href="https://carto.com/attributions">CARTO</a>'
        },
        esri: {
            name: 'ESRI WorldStreetMap',
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, DeLorme, NAVTEQ, USGS, Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012'
        }
    };

    // Default map style
    // Create the map with OpenStreetMap by default
    const map = L.map('tracking-map', mapOptions);

    // Add map provider selector
    const mapProviderControl = document.createElement('div');
    mapProviderControl.className = 'map-provider-control';
    mapProviderControl.innerHTML = `
        <select id="map-provider-select">
            <option value="osm" selected>OpenStreetMap</option>
            <option value="cartoDB">CartoDB Voyager</option>
            <option value="esri">ESRI WorldStreetMap</option>
        </select>
    `;
    document.getElementById('tracking-map').appendChild(mapProviderControl);

    // Current layer
    let currentLayer;

    // Function to change map provider
    function changeMapProvider(provider) {
        // Remove current layer if exists
        if (currentLayer) {
            map.removeLayer(currentLayer);
        }

        // Add new layer
        currentLayer = L.tileLayer(mapProviders[provider].url, {
            attribution: mapProviders[provider].attribution,
            maxZoom: 19
        }).addTo(map);

        // Update dark mode if needed
        updateMapTheme();
    }

    // Initialize with OpenStreetMap
    changeMapProvider('osm');

    // Add provider change listener
    document.getElementById('map-provider-select').addEventListener('change', function() {
        changeMapProvider(this.value);
    });

    // Create markers and path for each tracking update
    const markers = [];
    const pathPoints = [];

    // Custom marker icons
    const markerIcons = {
        // Standard status icons
        default: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-violet.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),
        latest: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-blue.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),
        delivered: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-green.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),
        transit: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-gold.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),
        delayed: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-orange.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),
        cancelled: L.icon({
            iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-red.png',
            shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/images/marker-shadow.png',
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34],
            shadowSize: [41, 41]
        }),

        // Special location icons with custom styling
        origin: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin origin-pin'>
                    <i class='fas fa-warehouse'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        destination: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin destination-pin'>
                    <i class='fas fa-flag-checkered'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        current: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin current-pin'>
                    <i class='fas fa-truck'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        transitPoint: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin transit-pin'>
                    <i class='fas fa-exchange-alt'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        delayedPoint: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin delayed-pin'>
                    <i class='fas fa-exclamation-triangle'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        })
    };

    // Process tracking updates
    trackingUpdates.forEach((update, index) => {
        // Skip if no coordinates
        if (!update.latitude || !update.longitude) {
            return;
        }

        const lat = parseFloat(update.latitude);
        const lng = parseFloat(update.longitude);

        // Add to path points
        pathPoints.push([lat, lng]);

        // Determine icon based on status and position
        let icon;
        const status = update.status.toLowerCase().replace('-', '_');
        const isFirst = index === trackingUpdates.length - 1; // First chronologically (origin)
        const isLast = index === 0; // Last chronologically (current/destination)

        // Determine if this is origin, destination, or transit point
        if (isFirst) {
            // Origin point (first chronologically)
            icon = markerIcons.origin;
        } else if (isLast) {
            // Current location (last chronologically)
            if (status === 'delivered') {
                icon = markerIcons.destination;
            } else if (status === 'delayed') {
                icon = markerIcons.delayedPoint;
            } else {
                icon = markerIcons.current;
            }
        } else {
            // Transit points
            switch(status) {
                case 'delivered':
                    icon = markerIcons.destination;
                    break;
                case 'in_transit':
                    icon = markerIcons.transitPoint;
                    break;
                case 'delayed':
                    icon = markerIcons.delayedPoint;
                    break;
                case 'cancelled':
                    icon = markerIcons.cancelled;
                    break;
                default:
                    icon = markerIcons.transitPoint;
            }
        }

        // Create marker
        const marker = L.marker([lat, lng], {
            icon: icon,
            title: update.location,
            zIndexOffset: trackingUpdates.length - index
        }).addTo(map);

        // Create popup content
        const popupContent = `
            <div class="map-info-window">
                <h3>${update.status}</h3>
                <p><strong>Location:</strong> ${update.location}</p>
                <p><strong>Time:</strong> ${formatDate(update.timestamp)}</p>
                ${update.notes ? `<p><strong>Notes:</strong> ${update.notes}</p>` : ''}
            </div>
        `;

        // Add popup to marker
        marker.bindPopup(popupContent);

        // Store marker
        markers.push(marker);
    });

    // Create route paths between points using OSRM routing service
    if (pathPoints.length > 1) {
        // Create a loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'route-loading-indicator';
        loadingIndicator.innerHTML = '<i class="fas fa-route"></i> Calculating routes...';
        mapElement.appendChild(loadingIndicator);

        // Track completed route segments
        let completedSegments = 0;
        const totalSegments = pathPoints.length - 1;

        // Create a route between each consecutive pair of points
        for (let i = 0; i < pathPoints.length - 1; i++) {
            const start = pathPoints[i];
            const end = pathPoints[i + 1];

            // Format coordinates for OSRM API (lng,lat format)
            const startCoord = `${start[1]},${start[0]}`;
            const endCoord = `${end[1]},${end[0]}`;

            // Use OSRM Demo server for routing (for production, use a commercial API with proper attribution)
            const routeUrl = `https://router.project-osrm.org/route/v1/driving/${startCoord};${endCoord}?overview=full&geometries=polyline`;

            fetch(routeUrl)
                .then(response => response.json())
                .then(data => {
                    if (data.code === 'Ok' && data.routes && data.routes.length > 0) {
                        // Decode the polyline
                        const routeGeometry = data.routes[0].geometry;
                        const routePoints = L.Polyline.fromEncoded(routeGeometry).getLatLngs();

                        // Create the route line with appropriate styling
                        const routeColor = getRouteColor(i, totalSegments);
                        const routeLine = L.polyline(routePoints, {
                            color: routeColor,
                            weight: 4,
                            opacity: 0.8,
                            lineJoin: 'round',
                            dashArray: i === totalSegments - 1 && markers[0].options.icon === markerIcons.delivered ? null : '10, 10',
                            lineCap: 'round'
                        }).addTo(map);

                        // Add direction arrows
                        const arrowDecorator = L.polylineDecorator(routeLine, {
                            patterns: [
                                {
                                    offset: 25,
                                    repeat: 100,
                                    symbol: L.Symbol.arrowHead({
                                        pixelSize: 12,
                                        polygon: false,
                                        pathOptions: {
                                            stroke: true,
                                            color: routeColor,
                                            weight: 3
                                        }
                                    })
                                }
                            ]
                        }).addTo(map);
                    } else {
                        // Fallback to straight line if routing fails
                        const fallbackLine = L.polyline([start, end], {
                            color: '#5c2be2',
                            weight: 3,
                            opacity: 0.6,
                            dashArray: '5, 10',
                            lineJoin: 'round'
                        }).addTo(map);
                    }

                    // Update progress
                    completedSegments++;
                    if (completedSegments === totalSegments) {
                        // Remove loading indicator when all routes are loaded
                        loadingIndicator.remove();
                    }
                })
                .catch(error => {
                    console.error('Routing error:', error);

                    // Fallback to straight line if routing fails
                    const fallbackLine = L.polyline([start, end], {
                        color: '#5c2be2',
                        weight: 3,
                        opacity: 0.6,
                        dashArray: '5, 10',
                        lineJoin: 'round'
                    }).addTo(map);

                    // Update progress
                    completedSegments++;
                    if (completedSegments === totalSegments) {
                        // Remove loading indicator when all routes are loaded
                        loadingIndicator.remove();
                    }
                });
        }
    }

    // Helper function to get route color based on segment index
    function getRouteColor(index, total) {
        if (total <= 1) return '#5c2be2'; // Default purple

        if (index === 0) {
            return '#ffc107'; // First segment (origin) - gold
        } else if (index === total - 1) {
            return '#28a745'; // Last segment (destination) - green
        } else {
            return '#5c2be2'; // Middle segments - purple
        }
    }

    // Fit map to bounds
    if (pathPoints.length > 0) {
        map.fitBounds(pathPoints);

        // If only one marker, zoom out a bit
        if (pathPoints.length === 1) {
            map.setZoom(10);
        }
    }

    // Open first popup by default
    if (markers.length > 0) {
        setTimeout(() => {
            markers[0].openPopup();
        }, 1000);
    }

    // Add dark mode listener
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            setTimeout(() => {
                updateMapTheme();
            }, 100);
        });
    }

    // Initial theme check
    updateMapTheme();

    // Function to update map theme based on site theme
    function updateMapTheme() {
        const isDarkTheme = document.body.classList.contains('dark-theme');
        const selectElement = document.getElementById('map-provider-select');
        const currentProvider = selectElement ? selectElement.value : 'osm';

        // Apply dark theme styles to map container
        const mapContainer = document.getElementById('tracking-map');
        if (isDarkTheme) {
            mapContainer.classList.add('dark-theme-map');
        } else {
            mapContainer.classList.remove('dark-theme-map');
        }

        // For future: could switch to dark mode tile providers if available
    }
});

// Format date for info window
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString();
}


