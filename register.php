<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is already logged in
if(isLoggedIn()) {
    // Redirect to dashboard
    redirect('user/index.php');
}

// Set page title
$pageTitle = 'Register';

// Include header
include_once 'includes/header.php';
?>

<!-- Registration Hero Section -->
<section class="hero gradient-hero textured">
    <div class="container">
        <div class="hero-content">
            <h1>Create an Account</h1>
            <p>Join our platform to track your shipments and manage your deliveries.</p>
        </div>
    </div>
</section>

<!-- Registration Form Section -->
<section class="registration-section">
    <div class="container">
        <div class="form-container glass-card">
            <h2>Register</h2>
            <form id="registration-form" method="POST" action="includes/handle-registration.php">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" placeholder="Choose a username" required>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" placeholder="Enter your email" required>
                </div>
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" placeholder="Create a password" required>
                </div>
                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm your password" required>
                </div>
                <div class="form-group">
                    <button type="submit" class="btn primary-btn">Register</button>
                </div>
                <div class="form-footer">
                    <p>Already have an account? <a href="#" id="login-link">Login</a></p>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle login link click
    document.getElementById('login-link').addEventListener('click', function(e) {
        e.preventDefault();
        // Trigger the login popup
        const loginButton = document.getElementById('login-button');
        if (loginButton) {
            loginButton.click();
        }
    });

    // Form validation
    const registrationForm = document.getElementById('registration-form');
    registrationForm.addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (password !== confirmPassword) {
            e.preventDefault();
            showNotification('Passwords do not match', 'error');
            return false;
        }
        
        if (password.length < 8) {
            e.preventDefault();
            showNotification('Password must be at least 8 characters long', 'error');
            return false;
        }
    });
});
</script>

<!-- Add CSS for registration page -->
<style>
.registration-section {
    padding: 60px 0;
    background-color: var(--bg-color);
}

.form-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 30px;
}

.form-container h2 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background-color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(92, 43, 226, 0.2);
}

.form-group button {
    width: 100%;
    padding: 12px;
    font-size: 1rem;
    font-weight: 600;
}

.form-footer {
    text-align: center;
    margin-top: 20px;
}

.form-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* Dark theme adjustments */
.dark-theme .form-container {
    background-color: rgba(30, 30, 40, 0.7);
}

.dark-theme .form-group input {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.dark-theme .form-footer a {
    color: var(--secondary-color);
}
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
