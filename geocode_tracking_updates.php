<?php
/**
 * Geocode Tracking Updates
 * 
 * This script finds all tracking updates without coordinates and attempts to geocode them.
 * It can be run manually or scheduled as a cron job.
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/geocoding.php';

// Check if this is being run from the command line or browser
$isCli = (php_sapi_name() === 'cli');

// Set content type for browser output
if (!$isCli) {
    header('Content-Type: text/html; charset=utf-8');
    echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Geocode Tracking Updates</title>
    <link rel='stylesheet' href='assets/css/styles.css'>
    <link rel='stylesheet' href='assets/css/admin.css'>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .error {
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .info {
            color: #0c5460;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .log {
            max-height: 400px;
            overflow-y: auto;
            background-color: #f8f9fa;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 20px 0;
            font-family: monospace;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #0069d9;
        }
    </style>
</head>
<body>
    <div class='container'>
        <h1>Geocode Tracking Updates</h1>
        <div class='log'>";
}

// Function to output messages
function output($message, $type = 'info') {
    global $isCli;
    
    if ($isCli) {
        echo "[" . date('Y-m-d H:i:s') . "] " . $message . PHP_EOL;
    } else {
        echo "<p class='{$type}'>" . htmlspecialchars($message) . "</p>";
    }
    
    // Flush output buffer to show progress in real-time
    if (!$isCli) {
        ob_flush();
        flush();
    }
}

try {
    // Initialize geocoding helper
    $geocoder = new GeocodingHelper();
    
    // Get tracking updates without coordinates
    $stmt = $conn->query("
        SELECT id, shipment_id, location 
        FROM tracking_updates 
        WHERE (latitude IS NULL OR longitude IS NULL) 
        AND location IS NOT NULL AND location != ''
        LIMIT 100
    ");
    
    $updates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $totalUpdates = count($updates);
    
    output("Found {$totalUpdates} tracking updates without coordinates.");
    
    if ($totalUpdates === 0) {
        output("No tracking updates need geocoding. All updates already have coordinates.", "success");
    } else {
        $geocodedCount = 0;
        $failedCount = 0;
        
        foreach ($updates as $update) {
            $location = $update['location'];
            output("Processing update #{$update['id']} for shipment #{$update['shipment_id']}: {$location}");
            
            // Try to geocode the location
            $result = $geocoder->geocode($location);
            
            if ($result) {
                // Update the database with the coordinates
                $updateStmt = $conn->prepare("
                    UPDATE tracking_updates 
                    SET latitude = ?, longitude = ? 
                    WHERE id = ?
                ");
                
                $updateStmt->execute([
                    $result['latitude'],
                    $result['longitude'],
                    $update['id']
                ]);
                
                output("✓ Successfully geocoded '{$location}' to {$result['latitude']}, {$result['longitude']}", "success");
                $geocodedCount++;
                
                // Add a small delay to avoid hitting rate limits
                usleep(500000); // 0.5 seconds
            } else {
                output("✗ Failed to geocode '{$location}'", "error");
                $failedCount++;
            }
        }
        
        // Output summary
        output("Geocoding completed: {$geocodedCount} successful, {$failedCount} failed.", $geocodedCount > 0 ? "success" : "info");
        
        // Check if there are more updates to process
        $remainingStmt = $conn->query("
            SELECT COUNT(*) as count
            FROM tracking_updates 
            WHERE (latitude IS NULL OR longitude IS NULL) 
            AND location IS NOT NULL AND location != ''
        ");
        
        $remaining = $remainingStmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($remaining > 0) {
            output("There are still {$remaining} updates that need geocoding. Run this script again to process more.", "info");
        } else {
            output("All tracking updates have been geocoded successfully!", "success");
        }
    }
} catch (PDOException $e) {
    output("Database error: " . $e->getMessage(), "error");
} catch (Exception $e) {
    output("Error: " . $e->getMessage(), "error");
}

// Close HTML if in browser mode
if (!$isCli) {
    echo "</div>
        <div>
            <a href='admin/index.php' class='btn'>Back to Admin Dashboard</a>
            <a href='tracking/index.php' class='btn'>Go to Tracking Page</a>
        </div>
    </div>
</body>
</html>";
}
