/* Admin Responsive Text Sizing */

/* 
 * This CSS implements fluid typography for admin pages
 * using the clamp() function to create responsive text sizes
 * that scale smoothly between minimum and maximum sizes
 * based on viewport width.
 */

/* Base font size for the body - scales between 14px and 16px */
body.admin-page {
    font-size: clamp(14px, 2.5vw, 16px);
}

/* Hero section text */
.admin-hero p {
    font-size: clamp(1rem, 3vw, 1.2rem);
    line-height: 1.5;
}

/* Headings */
.admin-page h1 {
    font-size: clamp(1.8rem, 5vw, 2.5rem);
    line-height: 1.2;
    margin-bottom: clamp(16px, 3vw, 24px);
}

.admin-page h2,
.admin-page .section-title {
    font-size: clamp(1.4rem, 4vw, 1.8rem);
    line-height: 1.3;
    margin-bottom: clamp(14px, 2.5vw, 20px);
}

.admin-page h3 {
    font-size: clamp(1.2rem, 3.5vw, 1.5rem);
    line-height: 1.3;
    margin-bottom: clamp(12px, 2vw, 18px);
}

.admin-page h4 {
    font-size: clamp(1.1rem, 3vw, 1.3rem);
    line-height: 1.4;
    margin-bottom: clamp(10px, 1.5vw, 16px);
}

/* Paragraphs and text */
.admin-page p {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: 1.6;
    margin-bottom: clamp(12px, 2vw, 16px);
}

/* Buttons */
.admin-page .btn {
    font-size: clamp(14px, 2.5vw, 16px);
    padding: clamp(10px, 2vw, 12px) clamp(16px, 4vw, 24px);
    min-height: 44px;
}

/* Form elements */
.admin-page input,
.admin-page select,
.admin-page textarea {
    font-size: clamp(14px, 2.5vw, 16px);
    padding: clamp(8px, 2vw, 12px);
}

/* Table text */
.admin-page .table th,
.admin-page .table td {
    font-size: clamp(12px, 2vw, 14px);
    padding: clamp(6px, 1.5vw, 10px) clamp(8px, 2vw, 12px);
}

/* Navigation */
.admin-page nav ul li a {
    font-size: clamp(14px, 2.5vw, 16px);
}

/* Cards */
.admin-page .card h3,
.admin-page .stat-card h3 {
    font-size: clamp(1.1rem, 3vw, 1.3rem);
}

/* Stat numbers */
.admin-page .stat-number {
    font-size: clamp(1.5rem, 5vw, 2.2rem);
}

.admin-page .stat-label {
    font-size: clamp(12px, 2vw, 14px);
}

.admin-page h3, 
.admin-page .stats-subheader,
.admin-page .metrics-subheader {
    font-size: clamp(1.1rem, calc(1.1rem + 0.3vw), 1.4rem);
}

/* Stats and metrics */
.admin-page .stat-number {
    font-size: clamp(1.3rem, calc(1.3rem + 0.5vw), 1.8rem);
}

.admin-page .stat-percentage {
    font-size: clamp(0.8rem, calc(0.8rem + 0.2vw), 1rem);
}

.admin-page .metric-value {
    font-size: clamp(1.1rem, calc(1.1rem + 0.3vw), 1.4rem);
}

.admin-page .summary-value {
    font-size: clamp(0.9rem, calc(0.9rem + 0.2vw), 1.1rem);
}

/* Action cards */
.admin-page .action-card h3 {
    font-size: clamp(1rem, calc(1rem + 0.2vw), 1.2rem);
}

.admin-page .action-card p {
    font-size: clamp(0.8rem, calc(0.8rem + 0.1vw), 0.9rem);
}

/* Table text */
.admin-page .data-table {
    font-size: clamp(0.85rem, calc(0.85rem + 0.15vw), 1rem);
}

.admin-page .data-table th {
    font-size: clamp(0.9rem, calc(0.9rem + 0.1vw), 1.05rem);
}

/* Form elements */
.admin-page input,
.admin-page select,
.admin-page textarea,
.admin-page button,
.admin-page .btn {
    font-size: clamp(0.9rem, calc(0.9rem + 0.1vw), 1rem);
}

/* Form labels */
.admin-page label {
    font-size: clamp(0.9rem, calc(0.9rem + 0.1vw), 1.05rem);
}

/* Help text and small text */
.admin-page .form-text,
.admin-page .help-text,
.admin-page small {
    font-size: clamp(0.75rem, calc(0.75rem + 0.1vw), 0.85rem);
}

/* Activity feed */
.admin-page .activity-title {
    font-size: clamp(0.9rem, calc(0.9rem + 0.1vw), 1.05rem);
}

.admin-page .activity-time {
    font-size: clamp(0.75rem, calc(0.75rem + 0.05vw), 0.85rem);
}

/* Buttons */
.admin-page .btn {
    padding: clamp(8px, calc(8px + 0.3vw), 12px) clamp(16px, calc(16px + 0.5vw), 24px);
}

/* Action buttons in tables */
.admin-page .action-btn {
    font-size: clamp(0.8rem, calc(0.8rem + 0.1vw), 0.9rem);
}

/* Ensure icons scale appropriately */
.admin-page .stat-icon i,
.admin-page .metric-icon i,
.admin-page .action-icon i {
    font-size: clamp(1.2rem, calc(1.2rem + 0.3vw), 1.5rem);
}

/* Chart legends */
.admin-page .chart-container .chartjs-legend {
    font-size: clamp(0.8rem, calc(0.8rem + 0.1vw), 0.9rem);
}
