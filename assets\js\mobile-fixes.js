/**
 * Mobile Fixes for Button Clicks and Navigation
 * Ensures all interactive elements work properly on mobile devices
 */

document.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth <= 768) {
        initializeMobileFixes();
    }
});

function initializeMobileFixes() {
    fixMobileNavigation();
    fixButtonClicks();
    fixModalTriggers();
    fixFormSubmissions();
}

/**
 * Fix mobile navigation button clicks
 */
function fixMobileNavigation() {
    // Fix login button in mobile nav
    const loginButtons = document.querySelectorAll('[onclick*="showLoginPopup"], .login-btn, #mobile-login-btn');
    
    loginButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Check if showLoginPopup function exists
            if (typeof showLoginPopup === 'function') {
                showLoginPopup();
            } else {
                // Fallback: redirect to login page or show alert
                console.log('Login popup function not found');
                // You could redirect to a login page here if needed
            }
        });
    });
    
    // Fix theme toggle in mobile nav
    const themeToggle = document.querySelector('#mobile-theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // Toggle theme
            if (typeof toggleTheme === 'function') {
                toggleTheme();
            } else {
                // Fallback theme toggle
                const body = document.body;
                const isDark = body.classList.contains('dark-theme');
                
                if (isDark) {
                    body.classList.remove('dark-theme');
                    localStorage.setItem('theme', 'light');
                    document.cookie = 'theme=light; path=/; max-age=31536000';
                } else {
                    body.classList.add('dark-theme');
                    localStorage.setItem('theme', 'dark');
                    document.cookie = 'theme=dark; path=/; max-age=31536000';
                }
                
                // Update icon
                const icon = this.querySelector('i[data-feather]');
                if (icon) {
                    icon.setAttribute('data-feather', isDark ? 'moon' : 'sun');
                    if (typeof feather !== 'undefined') {
                        feather.replace();
                    }
                }
            }
        });
    }
}

/**
 * Fix general button clicks
 */
function fixButtonClicks() {
    // Fix all buttons with onclick attributes
    const buttonsWithOnclick = document.querySelectorAll('[onclick]');
    
    buttonsWithOnclick.forEach(button => {
        const originalOnclick = button.getAttribute('onclick');
        
        // Add touch-friendly click handler
        button.addEventListener('click', function(e) {
            // For regular links, let them work normally
            if (button.tagName === 'A' && button.getAttribute('href') && 
                !button.getAttribute('href').startsWith('#') && 
                !originalOnclick.includes('preventDefault')) {
                return;
            }
            
            e.preventDefault();
            e.stopPropagation();
            
            try {
                // Execute the original onclick function
                const func = new Function(originalOnclick);
                func.call(button);
            } catch (error) {
                console.error('Error executing onclick function:', error);
                console.log('Original onclick:', originalOnclick);
            }
        });
        
        // Add touch feedback
        button.addEventListener('touchstart', function() {
            this.style.opacity = '0.7';
        });
        
        button.addEventListener('touchend', function() {
            this.style.opacity = '';
        });
        
        button.addEventListener('touchcancel', function() {
            this.style.opacity = '';
        });
    });
}

/**
 * Fix modal triggers
 */
function fixModalTriggers() {
    // Fix modal trigger buttons
    const modalTriggers = document.querySelectorAll('[data-toggle="modal"], [data-target*="modal"], .modal-trigger');
    
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const targetModal = this.getAttribute('data-target') || this.getAttribute('href');
            if (targetModal) {
                const modal = document.querySelector(targetModal);
                if (modal) {
                    modal.style.display = 'flex';
                    modal.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            }
        });
    });
    
    // Fix modal close buttons
    const modalCloseButtons = document.querySelectorAll('.modal-close, .close, [data-dismiss="modal"]');
    
    modalCloseButtons.forEach(closeBtn => {
        closeBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const modal = this.closest('.modal, .popup-overlay');
            if (modal) {
                modal.style.display = 'none';
                modal.classList.remove('show');
                document.body.style.overflow = '';
            }
        });
    });
}

/**
 * Fix form submissions
 */
function fixFormSubmissions() {
    // Ensure form submit buttons work
    const submitButtons = document.querySelectorAll('input[type="submit"], button[type="submit"], .submit-btn');
    
    submitButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const form = this.closest('form');
            if (form) {
                // Let the form handle the submission naturally
                return;
            }
        });
    });
}

/**
 * Add touch feedback to all interactive elements
 */
function addTouchFeedback() {
    const interactiveElements = document.querySelectorAll('button, .btn, a, input[type="submit"], input[type="button"]');
    
    interactiveElements.forEach(element => {
        element.addEventListener('touchstart', function() {
            this.classList.add('touch-active');
        });
        
        element.addEventListener('touchend', function() {
            this.classList.remove('touch-active');
        });
        
        element.addEventListener('touchcancel', function() {
            this.classList.remove('touch-active');
        });
    });
}

// Initialize touch feedback
document.addEventListener('DOMContentLoaded', function() {
    if (window.innerWidth <= 768) {
        addTouchFeedback();
    }
});

// Re-initialize on window resize
window.addEventListener('resize', function() {
    if (window.innerWidth <= 768) {
        setTimeout(initializeMobileFixes, 100);
    }
});

// Add CSS for touch feedback
const style = document.createElement('style');
style.textContent = `
    .touch-active {
        opacity: 0.7 !important;
        transform: scale(0.98) !important;
        transition: all 0.1s ease !important;
    }
    
    @media (max-width: 768px) {
        button, .btn, a[href] {
            -webkit-tap-highlight-color: rgba(0,0,0,0.1);
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
        }
        
        /* Ensure buttons are touch-friendly */
        button, .btn {
            min-height: 44px;
            min-width: 44px;
            cursor: pointer;
            touch-action: manipulation;
        }
    }
`;
document.head.appendChild(style);
