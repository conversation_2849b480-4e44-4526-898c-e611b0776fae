<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if shipments table exists
$db->query("SHOW TABLES LIKE 'shipments'");
$shipmentsTable = $db->resultSet();

if (empty($shipmentsTable)) {
    echo "Shipments table does not exist. Please run insert_sample_data.php first.<br>";
    echo "<a href='insert_sample_data.php'>Go to insert_sample_data.php</a>";
    exit;
}

// Check if we have delivered shipments with delivered_at dates
$db->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'delivered' AND delivered_at IS NULL");
$missingDeliveredAt = $db->single();

if ($missingDeliveredAt['count'] > 0) {
    // Update delivered shipments to have delivered_at dates
    $db->query("UPDATE shipments SET delivered_at = DATE_ADD(created_at, INTERVAL FLOOR(RAND() * 10) + 1 DAY) WHERE status = 'delivered' AND delivered_at IS NULL");
    $db->execute();
    echo "Updated " . $missingDeliveredAt['count'] . " delivered shipments with delivered_at dates.<br>";
}

// Check if we have in_transit shipments
$db->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'in_transit'");
$inTransitCount = $db->single();

if ($inTransitCount['count'] < 5) {
    // Update some pending shipments to in_transit
    $db->query("UPDATE shipments SET status = 'in_transit' WHERE status = 'pending' LIMIT 5");
    $db->execute();
    echo "Updated some pending shipments to in_transit.<br>";
}

// Check if we have delayed shipments
$db->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'delayed'");
$delayedCount = $db->single();

if ($delayedCount['count'] < 3) {
    // Update some pending shipments to delayed
    $db->query("UPDATE shipments SET status = 'delayed' WHERE status = 'pending' LIMIT 3");
    $db->execute();
    echo "Updated some pending shipments to delayed.<br>";
}

// Check if we have cancelled shipments
$db->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'cancelled'");
$cancelledCount = $db->single();

if ($cancelledCount['count'] < 2) {
    // Update some pending shipments to cancelled
    $db->query("UPDATE shipments SET status = 'cancelled' WHERE status = 'pending' LIMIT 2");
    $db->execute();
    echo "Updated some pending shipments to cancelled.<br>";
}

// Make sure we have shipments in the current month
$currentMonth = date('Y-m');
$db->query("SELECT COUNT(*) as count FROM shipments WHERE DATE_FORMAT(created_at, '%Y-%m') = :current_month");
$db->bind(':current_month', $currentMonth);
$currentMonthCount = $db->single();

if ($currentMonthCount['count'] < 10) {
    // Add some shipments for the current month
    $statuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
    $origins = ['New York, USA', 'Los Angeles, USA', 'Chicago, USA', 'London, UK', 'Paris, France'];
    $destinations = ['Miami, USA', 'Dallas, USA', 'Seattle, USA', 'Manchester, UK', 'Lyon, France'];
    $customers = ['John Smith', 'Jane Doe', 'Robert Johnson', 'Emily Brown', 'Michael Davis'];
    
    for ($i = 0; $i < 10; $i++) {
        $trackingNumber = generateTrackingNumber();
        $customerName = $customers[array_rand($customers)];
        $origin = $origins[array_rand($origins)];
        $destination = $destinations[array_rand($destinations)];
        $status = $statuses[array_rand($statuses)];
        
        // Random date within the current month
        $daysAgo = rand(0, date('j') - 1); // Random day in current month
        $createdAt = date('Y-m-d H:i:s', strtotime("-$daysAgo days"));
        
        // Set delivered_at if status is delivered
        $deliveredAt = null;
        if ($status === 'delivered') {
            $deliveryDays = rand(1, 5);
            $deliveredAt = date('Y-m-d H:i:s', strtotime($createdAt . " +$deliveryDays days"));
        }
        
        // Set estimated delivery date
        $estimatedDelivery = date('Y-m-d', strtotime($createdAt . " +7 days"));
        
        // Insert shipment
        $db->query("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, delivered_at, estimated_delivery) 
                    VALUES (:tracking_number, :customer_name, :origin, :destination, :status, :created_at, :delivered_at, :estimated_delivery)");
        $db->bind(':tracking_number', $trackingNumber);
        $db->bind(':customer_name', $customerName);
        $db->bind(':origin', $origin);
        $db->bind(':destination', $destination);
        $db->bind(':status', $status);
        $db->bind(':created_at', $createdAt);
        $db->bind(':delivered_at', $deliveredAt);
        $db->bind(':estimated_delivery', $estimatedDelivery);
        $db->execute();
    }
    
    echo "Added 10 shipments for the current month.<br>";
}

// Make sure we have some common routes with multiple shipments
$commonRoutes = [
    ['New York, USA', 'Miami, USA'],
    ['London, UK', 'Manchester, UK'],
    ['Paris, France', 'Lyon, France']
];

foreach ($commonRoutes as $route) {
    $origin = $route[0];
    $destination = $route[1];
    
    $db->query("SELECT COUNT(*) as count FROM shipments WHERE origin = :origin AND destination = :destination");
    $db->bind(':origin', $origin);
    $db->bind(':destination', $destination);
    $routeCount = $db->single();
    
    if ($routeCount['count'] < 5) {
        // Add more shipments for this route
        $statuses = ['pending', 'in_transit', 'delivered'];
        
        for ($i = 0; $i < 5; $i++) {
            $trackingNumber = generateTrackingNumber();
            $customerName = 'Customer ' . rand(1, 100);
            $status = $statuses[array_rand($statuses)];
            
            // Random date within the past 3 months
            $daysAgo = rand(0, 90);
            $createdAt = date('Y-m-d H:i:s', strtotime("-$daysAgo days"));
            
            // Set delivered_at if status is delivered
            $deliveredAt = null;
            if ($status === 'delivered') {
                $deliveryDays = rand(1, 7);
                $deliveredAt = date('Y-m-d H:i:s', strtotime($createdAt . " +$deliveryDays days"));
            }
            
            // Set estimated delivery date
            $estimatedDelivery = date('Y-m-d', strtotime($createdAt . " +7 days"));
            
            // Insert shipment
            $db->query("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, delivered_at, estimated_delivery) 
                        VALUES (:tracking_number, :customer_name, :origin, :destination, :status, :created_at, :delivered_at, :estimated_delivery)");
            $db->bind(':tracking_number', $trackingNumber);
            $db->bind(':customer_name', $customerName);
            $db->bind(':origin', $origin);
            $db->bind(':destination', $destination);
            $db->bind(':status', $status);
            $db->bind(':created_at', $createdAt);
            $db->bind(':delivered_at', $deliveredAt);
            $db->bind(':estimated_delivery', $estimatedDelivery);
            $db->execute();
        }
        
        echo "Added 5 shipments for route $origin to $destination.<br>";
    }
}

echo "Dashboard data fixes complete.<br>";
echo "<a href='admin/index.php'>Go to Admin Dashboard</a>";
