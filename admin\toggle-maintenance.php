<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if(!isLoggedIn()) {
    setMessage('Please login to access this feature', 'error');
    redirect('index.php');
    exit;
}

if(!isAdmin()) {
    setMessage('You do not have permission to toggle maintenance mode', 'error');
    redirect('index.php');
    exit;
}

try {
    // Get current maintenance mode status
    $currentStatus = getSetting('maintenance_mode', '0');
    
    // Toggle the status (if 1, set to 0; if 0, set to 1)
    $newStatus = $currentStatus == '1' ? '0' : '1';
    
    // Update the maintenance_mode setting
    $updateStmt = $conn->prepare("UPDATE system_settings SET setting_value = ?, updated_at = NOW() WHERE setting_key = 'maintenance_mode'");
    $result = $updateStmt->execute([$newStatus]);
    
    if($result) {
        if($newStatus == '1') {
            setMessage('Maintenance mode has been enabled', 'success');
        } else {
            setMessage('Maintenance mode has been disabled', 'success');
        }
    } else {
        setMessage('Failed to toggle maintenance mode', 'error');
    }
} catch (PDOException $e) {
    setMessage('Database error: ' . $e->getMessage(), 'error');
}

// Redirect back to admin dashboard
redirect('index.php');
?>
