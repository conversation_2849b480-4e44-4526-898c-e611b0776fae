<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/geocoding.php';

// Set content type to JSON
header('Content-Type: application/json');

// Check if address is provided
if (empty($_GET['address'])) {
    echo json_encode([
        'success' => false,
        'error' => 'Address is required'
    ]);
    exit;
}

// Get the address from the query string
$address = $_GET['address'];

// Initialize geocoding helper
$geocoder = new GeocodingHelper();

// Try to geocode the address
$result = $geocoder->geocode($address);

if ($result) {
    echo json_encode([
        'success' => true,
        'result' => $result
    ]);
} else {
    echo json_encode([
        'success' => false,
        'error' => 'Could not geocode the address'
    ]);
}
