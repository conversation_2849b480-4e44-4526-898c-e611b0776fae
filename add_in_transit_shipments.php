<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>Adding In-Transit Shipments</h1>";

try {
    // First, check how many in_transit shipments we have
    $checkStmt = $conn->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'in_transit'");
    $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>Current in_transit shipments: " . $result['count'] . "</p>";
    
    // Add 5 new in_transit shipments
    $origins = ['New York, USA', 'Los Angeles, USA', 'Chicago, USA', 'London, UK', 'Paris, France'];
    $destinations = ['Miami, USA', 'Dallas, USA', 'Seattle, USA', 'Manchester, UK', 'Lyon, France'];
    $customers = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
    
    $addedCount = 0;
    
    for ($i = 0; $i < 5; $i++) {
        $trackingNumber = generateTrackingNumber();
        $customerName = $customers[$i];
        $origin = $origins[$i];
        $destination = $destinations[$i];
        $status = 'in_transit'; // Using the correct format with underscore
        $createdAt = date('Y-m-d H:i:s', strtotime("-" . rand(1, 10) . " days"));
        $estimatedDelivery = date('Y-m-d', strtotime($createdAt . " +7 days"));
        
        $stmt = $conn->prepare("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, created_at, estimated_delivery)
                              VALUES (?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([$trackingNumber, $customerName, $origin, $destination, $status, $createdAt, $estimatedDelivery]);
        
        if ($result) {
            $addedCount++;
            $shipmentId = $conn->lastInsertId();
            
            // Add a tracking update for this shipment
            $updateStmt = $conn->prepare("INSERT INTO tracking_updates (shipment_id, location, status, notes, timestamp)
                                        VALUES (?, ?, ?, ?, ?)");
            $updateStmt->execute([
                $shipmentId,
                $origin,
                'in_transit',
                'Shipment in transit from ' . $origin . ' to ' . $destination,
                $createdAt
            ]);
            
            echo "<p>Added shipment #" . $shipmentId . " with tracking number " . $trackingNumber . "</p>";
        }
    }
    
    echo "<p>Successfully added " . $addedCount . " in_transit shipments.</p>";
    
    // Check again how many in_transit shipments we have
    $checkStmt = $conn->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'in_transit'");
    $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>New total of in_transit shipments: " . $result['count'] . "</p>";
    
    // Test the dashboard query
    $statsQuery = $conn->query("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as `delayed`,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM shipments");
    $stats = $statsQuery->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Dashboard Query Results:</h2>";
    echo "<ul>";
    foreach ($stats as $key => $value) {
        echo "<li><strong>" . htmlspecialchars($key) . ":</strong> " . $value . "</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='admin/index.php'>Go to Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
