<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('../index.php');
    exit;
}

// Set page title
$pageTitle = 'Geocoding Settings';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $enableGeocoding = isset($_POST['enable_geocoding']) ? '1' : '0';
    $geocodingProvider = sanitize($_POST['geocoding_provider']);
    $geocodingApiKey = sanitize($_POST['geocoding_api_key']);
    
    try {
        // Update settings
        $conn->beginTransaction();
        
        // Update enable_geocoding setting
        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_group, is_public) 
                               VALUES ('enable_geocoding', ?, 'geocoding', 1) 
                               ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$enableGeocoding, $enableGeocoding]);
        
        // Update geocoding_provider setting
        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_group, is_public) 
                               VALUES ('geocoding_provider', ?, 'geocoding', 1) 
                               ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$geocodingProvider, $geocodingProvider]);
        
        // Update geocoding_api_key setting
        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, setting_group, is_public) 
                               VALUES ('geocoding_api_key', ?, 'geocoding', 0) 
                               ON DUPLICATE KEY UPDATE setting_value = ?");
        $stmt->execute([$geocodingApiKey, $geocodingApiKey]);
        
        $conn->commit();
        
        setSuccessNotification('Geocoding settings updated successfully');
    } catch (PDOException $e) {
        $conn->rollBack();
        setErrorNotification('Error updating geocoding settings: ' . $e->getMessage());
    }
}

// Get current settings
$enableGeocoding = getSetting('enable_geocoding', '1');
$geocodingProvider = getSetting('geocoding_provider', 'nominatim');
$geocodingApiKey = getSetting('geocoding_api_key', '');

// Include header
include_once '../includes/header.php';
?>

<!-- Admin Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">
            <h1>Geocoding Settings</h1>
            <p>Configure geocoding settings for the tracking system</p>
        </div>
    </div>
</section>

<!-- Settings Form Section -->
<section class="admin-section">
    <div class="container">
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-map-marked-alt"></i> Geocoding Configuration</h2>
            </div>
            <div class="card-body">
                <form action="geocoding-settings.php" method="POST">
                    <div class="form-group">
                        <label for="enable_geocoding">Enable Geocoding</label>
                        <div class="toggle-switch">
                            <input type="checkbox" id="enable_geocoding" name="enable_geocoding" <?php echo $enableGeocoding === '1' ? 'checked' : ''; ?>>
                            <label for="enable_geocoding"></label>
                        </div>
                        <p class="form-help">When enabled, the system will automatically geocode locations to get coordinates for tracking maps.</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="geocoding_provider">Geocoding Provider</label>
                        <select id="geocoding_provider" name="geocoding_provider">
                            <option value="nominatim" <?php echo $geocodingProvider === 'nominatim' ? 'selected' : ''; ?>>Nominatim (OpenStreetMap)</option>
                            <option value="google" <?php echo $geocodingProvider === 'google' ? 'selected' : ''; ?>>Google Maps</option>
                            <option value="mapbox" <?php echo $geocodingProvider === 'mapbox' ? 'selected' : ''; ?>>Mapbox</option>
                            <option value="here" <?php echo $geocodingProvider === 'here' ? 'selected' : ''; ?>>HERE Maps</option>
                        </select>
                        <p class="form-help">Select the geocoding provider to use. Nominatim is free but has usage limits. Other providers require an API key.</p>
                    </div>
                    
                    <div class="form-group">
                        <label for="geocoding_api_key">API Key</label>
                        <input type="text" id="geocoding_api_key" name="geocoding_api_key" value="<?php echo htmlspecialchars($geocodingApiKey); ?>" placeholder="Enter your API key">
                        <p class="form-help">API key for the selected geocoding provider. Not required for Nominatim.</p>
                    </div>
                    
                    <div class="form-group provider-info" id="nominatim-info">
                        <div class="info-box">
                            <h3>Nominatim (OpenStreetMap)</h3>
                            <p>Nominatim is a free geocoding service provided by OpenStreetMap. It has usage limits of 1 request per second.</p>
                            <p>No API key is required, but please respect the <a href="https://operations.osmfoundation.org/policies/nominatim/" target="_blank">usage policy</a>.</p>
                        </div>
                    </div>
                    
                    <div class="form-group provider-info" id="google-info" style="display: none;">
                        <div class="info-box">
                            <h3>Google Maps</h3>
                            <p>Google Maps provides high-quality geocoding but requires an API key with billing enabled.</p>
                            <p>Get an API key from the <a href="https://console.cloud.google.com/google/maps-apis/overview" target="_blank">Google Cloud Console</a>.</p>
                        </div>
                    </div>
                    
                    <div class="form-group provider-info" id="mapbox-info" style="display: none;">
                        <div class="info-box">
                            <h3>Mapbox</h3>
                            <p>Mapbox offers geocoding services with a free tier and paid plans for higher usage.</p>
                            <p>Get an API key from the <a href="https://account.mapbox.com/access-tokens/" target="_blank">Mapbox account page</a>.</p>
                        </div>
                    </div>
                    
                    <div class="form-group provider-info" id="here-info" style="display: none;">
                        <div class="info-box">
                            <h3>HERE Maps</h3>
                            <p>HERE Maps provides geocoding services with a free tier for developers.</p>
                            <p>Get an API key from the <a href="https://developer.here.com/" target="_blank">HERE Developer Portal</a>.</p>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn primary-btn">Save Settings</button>
                        <a href="system-settings.php" class="btn outline-btn">Back to Settings</a>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="admin-card">
            <div class="card-header">
                <h2><i class="fas fa-tools"></i> Geocoding Tools</h2>
            </div>
            <div class="card-body">
                <div class="tools-grid">
                    <div class="tool-card">
                        <h3><i class="fas fa-sync"></i> Geocode Existing Updates</h3>
                        <p>Geocode tracking updates that don't have coordinates yet.</p>
                        <a href="../geocode_existing_updates.php" class="btn outline-btn">Run Tool</a>
                    </div>
                    
                    <div class="tool-card">
                        <h3><i class="fas fa-map-pin"></i> Test Geocoding</h3>
                        <p>Test geocoding with the current settings.</p>
                        <div class="test-geocoding">
                            <input type="text" id="test-address" placeholder="Enter an address to test">
                            <button id="test-geocode-btn" class="btn outline-btn">Test</button>
                        </div>
                        <div id="geocode-result" class="test-result"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Show/hide provider info based on selection
    const providerSelect = document.getElementById('geocoding_provider');
    const providerInfos = document.querySelectorAll('.provider-info');
    
    function updateProviderInfo() {
        const selectedProvider = providerSelect.value;
        
        // Hide all provider infos
        providerInfos.forEach(info => {
            info.style.display = 'none';
        });
        
        // Show selected provider info
        const selectedInfo = document.getElementById(selectedProvider + '-info');
        if (selectedInfo) {
            selectedInfo.style.display = 'block';
        }
    }
    
    // Initial update
    updateProviderInfo();
    
    // Update on change
    providerSelect.addEventListener('change', updateProviderInfo);
    
    // Test geocoding
    const testAddressInput = document.getElementById('test-address');
    const testGeocodeBtn = document.getElementById('test-geocode-btn');
    const geocodeResult = document.getElementById('geocode-result');
    
    testGeocodeBtn.addEventListener('click', function() {
        const address = testAddressInput.value.trim();
        
        if (!address) {
            geocodeResult.innerHTML = '<div class="error">Please enter an address to test</div>';
            return;
        }
        
        // Show loading
        geocodeResult.innerHTML = '<div class="loading"><i class="fas fa-spinner fa-spin"></i> Geocoding...</div>';
        
        // Make request to geocoding API
        fetch('../geocode.php?address=' + encodeURIComponent(address))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    geocodeResult.innerHTML = `
                        <div class="success">
                            <h4>Geocoding Successful</h4>
                            <p><strong>Provider:</strong> ${data.result.provider}</p>
                            <p><strong>Latitude:</strong> ${data.result.latitude}</p>
                            <p><strong>Longitude:</strong> ${data.result.longitude}</p>
                            <div class="map-preview">
                                <a href="https://www.openstreetmap.org/?mlat=${data.result.latitude}&mlon=${data.result.longitude}&zoom=15" target="_blank">
                                    View on Map <i class="fas fa-external-link-alt"></i>
                                </a>
                            </div>
                        </div>
                    `;
                } else {
                    geocodeResult.innerHTML = `
                        <div class="error">
                            <h4>Geocoding Failed</h4>
                            <p>${data.error || 'Unknown error'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                geocodeResult.innerHTML = `
                    <div class="error">
                        <h4>Request Error</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            });
    });
});
</script>

<style>
.admin-card {
    margin-bottom: 30px;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

.toggle-switch input:checked + label {
    background-color: var(--primary-color);
}

.toggle-switch input:checked + label:before {
    transform: translateX(26px);
}

.form-help {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-top: 5px;
}

.info-box {
    background-color: rgba(92, 43, 226, 0.05);
    border-left: 4px solid var(--primary-color);
    padding: 15px;
    border-radius: 4px;
    margin-top: 10px;
}

.info-box h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.info-box p {
    margin-bottom: 10px;
}

.info-box a {
    color: var(--primary-color);
    text-decoration: underline;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.tool-card {
    background-color: var(--glass-bg);
    border-radius: 8px;
    padding: 20px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.tool-card h3 {
    margin-top: 0;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.test-geocoding {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.test-geocoding input {
    flex: 1;
}

.test-result {
    margin-top: 15px;
}

.test-result .loading,
.test-result .success,
.test-result .error {
    padding: 15px;
    border-radius: 4px;
}

.test-result .loading {
    background-color: rgba(0, 123, 255, 0.1);
    color: #0d6efd;
}

.test-result .success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.test-result .error {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.test-result h4 {
    margin-top: 0;
    margin-bottom: 10px;
}

.map-preview {
    margin-top: 10px;
}

.map-preview a {
    display: inline-block;
    padding: 8px 12px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 4px;
    text-decoration: none;
    transition: background-color 0.3s;
}

.map-preview a:hover {
    background-color: var(--primary-dark);
}

@media (max-width: 768px) {
    .test-geocoding {
        flex-direction: column;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include_once '../includes/footer.php'; ?>
