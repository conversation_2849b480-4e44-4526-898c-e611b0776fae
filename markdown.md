# TransLogix Tracking System

## Project Description

The TransLogix Tracking System is a PHP-based tracking system designed for a transport and logistics company. It features a CMS for managing shipments and a public tracking interface with map visualization. This system aims to provide a comprehensive solution for tracking shipments, managing logistics, and providing customers with real-time updates on their deliveries.

## Features

### Public Website

*   **Responsive landing page with modern design:** The landing page is designed to be visually appealing and user-friendly, providing a seamless experience across different devices. It utilizes modern design principles and technologies to ensure a professional and engaging interface.
*   **Shipment tracking with interactive map:** The system integrates with a map service (OpenStreetMap with Leaflet) to provide a visual representation of the shipment's current location and route. This feature enhances the user experience by providing a clear and intuitive way to track shipments.
*   **Detailed tracking timeline:** The tracking timeline provides a chronological overview of the shipment's progress, including key milestones and updates. This feature allows users to easily track the shipment's journey and identify any potential delays or issues.
*   **Light and dark theme support:** The system supports both light and dark themes, allowing users to customize the interface to their preferences. This feature enhances accessibility and provides a more comfortable viewing experience.

### Admin CMS

*   **Secure login system:** The admin CMS is protected by a secure login system, ensuring that only authorized users can access sensitive data and functionality. This feature is crucial for maintaining the integrity and security of the system.
*   **Dashboard with shipment statistics:** The dashboard provides a visual overview of key shipment statistics, such as the number of shipments in transit, delivered, or delayed. This feature allows administrators to quickly assess the overall performance of the logistics operations.
*   **Shipment management (add, edit, delete):** The system allows administrators to easily manage shipments, including adding new shipments, editing existing shipments, and deleting shipments that are no longer needed. This feature provides a centralized interface for managing all shipment-related data.
*   **Tracking updates management:** The system allows administrators to add and manage tracking updates for each shipment. This feature ensures that customers have access to the most up-to-date information about their deliveries.
*   **User management:** The system allows administrators to manage user accounts, including creating new accounts, editing existing accounts, and assigning user roles and permissions. This feature provides control over who can access the admin CMS and what actions they can perform.
*   **System settings customization:** The system allows administrators to customize various system settings, such as the website's logo, theme, and tracking map provider. This feature provides flexibility and allows administrators to tailor the system to their specific needs.
*   **Alert system for important announcements:** The system includes an alert system that allows administrators to broadcast important announcements to all users. This feature is useful for communicating critical information, such as system maintenance or security updates.

## Requirements

*   **PHP 7.4 or higher:** The system requires PHP 7.4 or higher to ensure compatibility with modern PHP features and security updates. It is recommended to use the latest stable version of PHP for optimal performance and security. Specific extensions that may be required include:
    *   PDO (PHP Data Objects) for database access
    *   MySQLi for MySQL database interaction
    *   cURL for making HTTP requests
    *   GD for image manipulation
*   **MySQL 5.7 or higher:** The system requires MySQL 5.7 or higher for storing shipment data, user accounts, and system settings. It is recommended to use the latest stable version of MySQL for optimal performance and security. The database schema is defined in the `database.sql` file.
*   **Web server (Apache, Nginx, etc.):** The system requires a web server to serve the PHP files and assets. Apache and Nginx are the most popular web servers and are both compatible with the system. Configuration examples:
    *   **Apache:** Create a virtual host configuration file in `/etc/apache2/sites-available/` and enable it using `a2ensite`.
    *   **Nginx:** Create a server block configuration file in `/etc/nginx/sites-available/` and create a symbolic link to it in `/etc/nginx/sites-enabled/`.
*   **Internet connection (for loading map tiles from OpenStreetMap):** The system requires an internet connection to load map tiles from OpenStreetMap. This is necessary for displaying the shipment's location on the interactive map.

## Installation

1.  **Clone or download the repository:** Obtain the project files by cloning the repository from GitHub or downloading the ZIP archive.
    ```bash
    git clone [repository URL]
    ```
2.  **Access the installation script:** Open your web browser and navigate to the `install.php` file in the project directory (e.g., `http://your-domain.com/install.php`).
3.  **Configure the database connection:** The installation script will guide you through the process of configuring the database connection. You will need to provide the following information:
    *   Database host
    *   Database name
    *   Database username
    *   Database password
4.  **Run the installation:** Once you have provided the database connection details, the installation script will create the necessary database tables and import the sample data.
5.  **Access the application:**
    *   Public site: `http://your-domain.com/`
    *   Admin CMS: `http://your-domain.com/admin/`
    *   Default admin credentials:
        *   Username: admin
        *   Password: admin123

## Directory Structure

*   `/admin/`: Contains the files for the admin CMS, including user management, shipment management, and system settings.
    *   `index.php`: The main entry point for the admin CMS.
    *   `manage-shipments.php`: Allows administrators to manage shipments.
    *   `manage-users.php`: Allows administrators to manage user accounts.
    *   `settings.php`: Allows administrators to customize system settings.
*   `/assets/`: Contains the CSS, JavaScript, and image files for the system.
    *   `/css/`: Contains the CSS files for styling the website and admin CMS.
    *   `/js/`: Contains the JavaScript files for adding interactivity and functionality.
    *   `/img/`: Contains the image files used in the website and admin CMS.
*   `/includes/`: Contains the PHP include files, such as database connection details, functions, and header/footer templates.
    *   `config.php`: Contains the database connection details and other configuration options.
    *   `functions.php`: Contains commonly used functions.
    *   `header.php`: Contains the header template.
    *   `footer.php`: Contains the footer template.
*   `/tracking/`: Contains the files for the public tracking interface.
    *   `index.php`: The main entry point for the tracking interface.
*   `database.sql`: Contains the SQL script for setting up the database tables and sample data.
*   `install.php`: Contains the web-based installation script.
*   `README.md`: Contains the basic information about the project.

## Usage

### Public Tracking

1.  Enter a tracking number in the tracking form.
2.  View shipment details, map, and tracking timeline.
3.  Toggle between light and dark themes.

### Admin CMS

1.  Log in with admin credentials.
2.  Use the dashboard to view shipment statistics.
3.  Manage shipments and tracking updates.
4.  Add new users and configure system settings.

## Security Notes

*   Change the default admin password immediately after installation.
*   Keep your PHP and MySQL installations up to date.
*   Consider implementing HTTPS for secure data transmission.
*   Implement input validation and sanitization to prevent SQL injection and XSS attacks.

## Deployment Considerations

*   **Setting up a production environment:**
    *   Use a dedicated server or VPS for hosting the application.
    *   Configure the web server to use a non-default port (e.g., 8080 or 443).
    *   Disable error reporting in the `php.ini` file.
    *   Set the `display_errors` directive to `Off`.
    *   Enable opcode caching (e.g., using APCu or OPcache).
*   **Configuring HTTPS:**
    *   Obtain an SSL certificate from a trusted certificate authority (e.g., Let's Encrypt).
    *   Configure the web server to use the SSL certificate.
    *   Redirect all HTTP traffic to HTTPS.
*   **Optimizing performance:**
    *   Enable caching for static assets (CSS, JavaScript, images).
    *   Compress static assets using gzip or Brotli.
    *   Use a content delivery network (CDN) for serving static assets.
    *   Optimize database queries.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

*   Design inspired by Maersk.com
*   Uses OpenStreetMap with Leaflet for tracking visualization
*   Font Awesome for icons
*   PHP PDO for database operations

## Author Information

This project was created by Tembi Kelvin AKA defabrika.

Website: https://tembikelvin.github.io/