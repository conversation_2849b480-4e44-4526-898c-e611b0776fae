<?php
/**
 * Module Refresh Handler
 * 
 * This file handles AJAX requests to refresh dashboard modules
 */

require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if(!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => 'Authentication required']);
    exit;
}

// Check if module parameter is provided
if(!isset($_GET['module'])) {
    header('Content-Type: application/json');
    echo json_encode(['status' => 'error', 'message' => 'Module parameter is required']);
    exit;
}

$module = $_GET['module'];
$response = ['status' => 'success'];

// Handle different module refreshes
switch($module) {
    case 'shipment_overview':
        // Get shipment statistics
        $db->query("SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                    SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                    SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                    COUNT(DISTINCT origin) as origin_count,
                    COUNT(DISTINCT destination) as destination_count,
                    COUNT(DISTINCT customer_name) as customer_count
                    FROM shipments");
        $stats = $db->single();
        
        // Get current month stats
        $currentMonth = date('Y-m');
        $db->query("SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                    SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                    SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                    FROM shipments
                    WHERE DATE_FORMAT(created_at, '%Y-%m') = :current_month");
        $db->bind(':current_month', $currentMonth);
        $currentMonthStats = $db->single();
        
        // Calculate month-over-month growth
        $lastMonth = date('Y-m', strtotime('-1 month'));
        $db->query("SELECT COUNT(*) as total FROM shipments WHERE DATE_FORMAT(created_at, '%Y-%m') = :last_month");
        $db->bind(':last_month', $lastMonth);
        $lastMonthResult = $db->single();
        $lastMonthTotal = $lastMonthResult['total'] ?? 0;
        
        $monthlyGrowth = 0;
        if ($lastMonthTotal > 0) {
            $monthlyGrowth = (($currentMonthStats['total'] - $lastMonthTotal) / $lastMonthTotal) * 100;
        }
        
        $response['data'] = [
            'stats' => $stats,
            'currentMonthStats' => $currentMonthStats,
            'monthlyGrowth' => $monthlyGrowth
        ];
        break;
        
    case 'status_chart':
        // Get shipment statistics for status chart
        $db->query("SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                    SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                    SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                    FROM shipments");
        $stats = $db->single();
        
        $response['data'] = [
            'labels' => ['Pending', 'In Transit', 'Delivered', 'Delayed', 'Cancelled'],
            'datasets' => [
                [
                    'label' => 'Shipment Status',
                    'data' => [
                        $stats['pending'] ?? 0,
                        $stats['in_transit'] ?? 0,
                        $stats['delivered'] ?? 0,
                        $stats['delayed'] ?? 0,
                        $stats['cancelled'] ?? 0
                    ],
                    'backgroundColor' => [
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(0, 123, 255, 0.8)',
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(220, 53, 69, 0.8)'
                    ],
                    'borderColor' => [
                        'rgba(255, 193, 7, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 152, 0, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    'borderWidth' => 1
                ]
            ]
        ];
        break;
        
    case 'monthly_chart':
        // Get monthly shipment data for the current year
        $currentYear = date('Y');
        $db->query("SELECT
                    MONTH(created_at) as month,
                    COUNT(*) as count
                    FROM shipments
                    WHERE YEAR(created_at) = :year
                    GROUP BY MONTH(created_at)
                    ORDER BY month");
        $db->bind(':year', $currentYear);
        $monthlyData = $db->resultSet();
        
        // Format monthly data for chart
        $monthlyShipments = array_fill(0, 12, 0); // Initialize with zeros for all months
        foreach ($monthlyData as $data) {
            $monthIndex = (int)$data['month'] - 1; // Convert to 0-based index
            $monthlyShipments[$monthIndex] = (int)$data['count'];
        }
        
        $response['data'] = [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            'datasets' => [
                [
                    'label' => 'Shipments in ' . $currentYear,
                    'data' => $monthlyShipments,
                    'backgroundColor' => 'rgba(92, 43, 226, 0.2)',
                    'borderColor' => 'rgba(92, 43, 226, 1)',
                    'borderWidth' => 2,
                    'tension' => 0.4,
                    'fill' => true
                ]
            ]
        ];
        break;
        
    case 'destinations_chart':
        // Get top destinations
        $db->query("SELECT
                    destination,
                    COUNT(*) as count
                    FROM shipments
                    GROUP BY destination
                    ORDER BY count DESC
                    LIMIT 5");
        $topDestinations = $db->resultSet();
        
        $destinations = [];
        $counts = [];
        $colors = [];
        
        foreach($topDestinations as $index => $destination) {
            $destinations[] = $destination['destination'];
            $counts[] = (int)$destination['count'];
            // Generate a color based on index
            $hue = ($index * 50) % 360;
            $colors[] = 'rgba(' . rand(50, 200) . ', ' . rand(50, 200) . ', ' . rand(50, 200) . ', 0.8)';
        }
        
        $response['data'] = [
            'labels' => $destinations,
            'datasets' => [
                [
                    'label' => 'Shipment Count',
                    'data' => $counts,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 1
                ]
            ]
        ];
        break;
        
    case 'performance_metrics':
        // Calculate performance metrics
        $db->query("SELECT
                    (SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delivery_rate,
                    (SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delay_rate,
                    AVG(DATEDIFF(delivered_at, created_at)) as avg_delivery_time,
                    (SUM(CASE WHEN status = 'delivered' AND DATEDIFF(delivered_at, created_at) <= 3 THEN 1 ELSE 0 END) /
                     NULLIF(SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END), 0)) * 100 as on_time_rate,
                    MAX(DATEDIFF(delivered_at, created_at)) as max_delivery_time,
                    MIN(DATEDIFF(delivered_at, created_at)) as min_delivery_time
                    FROM shipments
                    WHERE delivered_at IS NOT NULL OR status = 'delivered'");
        $performanceMetrics = $db->single();
        
        // Calculate performance metrics for current month
        $currentMonth = date('Y-m');
        $db->query("SELECT
                    (SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delivery_rate,
                    (SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delay_rate,
                    AVG(DATEDIFF(delivered_at, created_at)) as avg_delivery_time,
                    (SUM(CASE WHEN status = 'delivered' AND DATEDIFF(delivered_at, created_at) <= 3 THEN 1 ELSE 0 END) /
                     NULLIF(SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END), 0)) * 100 as on_time_rate
                    FROM shipments
                    WHERE (delivered_at IS NOT NULL OR status = 'delivered')
                    AND DATE_FORMAT(created_at, '%Y-%m') = :current_month");
        $db->bind(':current_month', $currentMonth);
        $currentMonthPerformance = $db->single();
        
        // Get busiest routes
        $db->query("SELECT
                    CONCAT(origin, ' to ', destination) as route,
                    COUNT(*) as shipment_count,
                    AVG(DATEDIFF(delivered_at, created_at)) as avg_delivery_time
                    FROM shipments
                    WHERE status = 'delivered'
                    GROUP BY origin, destination
                    ORDER BY shipment_count DESC
                    LIMIT 3");
        $busiestRoutes = $db->resultSet();
        
        $response['data'] = [
            'performanceMetrics' => $performanceMetrics,
            'currentMonthPerformance' => $currentMonthPerformance,
            'busiestRoutes' => $busiestRoutes
        ];
        break;
        
    case 'recent_shipments':
        // Get recent shipments
        $db->query("SELECT * FROM shipments ORDER BY created_at DESC LIMIT 5");
        $recentShipments = $db->resultSet();
        
        $response['data'] = [
            'recentShipments' => $recentShipments
        ];
        break;
        
    case 'activity_feed':
        // Get recent activity
        $db->query("SELECT
                    'shipment' as type,
                    id,
                    tracking_number,
                    status,
                    created_at as timestamp,
                    CASE
                        WHEN status = 'pending' THEN 'New shipment created'
                        WHEN status = 'in_transit' THEN 'Shipment in transit'
                        WHEN status = 'delivered' THEN 'Shipment delivered'
                        WHEN status = 'delayed' THEN 'Shipment delayed'
                        WHEN status = 'cancelled' THEN 'Shipment cancelled'
                    END as activity_title
                    FROM shipments
                    ORDER BY created_at DESC
                    LIMIT 4");
        $recentActivity = $db->resultSet();
        
        $response['data'] = [
            'recentActivity' => $recentActivity
        ];
        break;
        
    default:
        $response = ['status' => 'error', 'message' => 'Invalid module'];
        break;
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
exit;
