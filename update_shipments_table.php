<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

try {
    global $conn;

    // Check if customer_id column exists in shipments table
    $stmt = $conn->query("SHOW COLUMNS FROM shipments LIKE 'customer_id'");
    $columnExists = $stmt->rowCount() > 0;

    if (!$columnExists) {
        // Add customer_id column to shipments table
        $conn->exec("ALTER TABLE shipments ADD COLUMN customer_id INT NULL");

        // Add foreign key constraint
        $conn->exec("ALTER TABLE shipments ADD CONSTRAINT fk_customer_id FOREIGN KEY (customer_id) REFERENCES users(id) ON DELETE SET NULL");

        setSuccessNotification('Shipments table updated successfully');
    } else {
        setInfoNotification('Customer ID column already exists in shipments table');
    }

    redirect('admin/index.php');
} catch (PDOException $e) {
    error_log("Error updating shipments table: " . $e->getMessage());
    setErrorNotification('Error updating shipments table: ' . $e->getMessage());
    redirect('admin/index.php');
}
?>
