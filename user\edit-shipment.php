<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Check if user has permission to update shipments
if (!canUpdateShipment()) {
    setErrorNotification('You do not have permission to edit shipments');
    header('Location: index.php');
    exit;
}

// Get shipment ID from URL
$shipmentId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($shipmentId <= 0) {
    setErrorNotification('Invalid shipment ID');
    header('Location: shipments.php');
    exit;
}

// Get shipment details
try {
    $stmt = $conn->prepare("SELECT * FROM shipments WHERE id = ?");
    $stmt->execute([$shipmentId]);
    $shipment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$shipment) {
        setErrorNotification('Shipment not found');
        header('Location: shipments.php');
        exit;
    }
} catch (PDOException $e) {
    setErrorNotification('Error retrieving shipment: ' . $e->getMessage());
    header('Location: shipments.php');
    exit;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_shipment'])) {
    // Basic shipment info
    $trackingNumber = sanitize($_POST['tracking_number']);
    $customerName = sanitize($_POST['customer_name']);
    $origin = sanitize($_POST['origin']);
    $destination = sanitize($_POST['destination']);
    $status = sanitize($_POST['status']);
    $estimatedDelivery = sanitize($_POST['estimated_delivery']);

    // Shopper info
    $shopperName = sanitize($_POST['shopper_name']);
    $shopperEmail = sanitize($_POST['shopper_email']);
    $shopperPhone = sanitize($_POST['shopper_phone']);
    $shopperAddress = sanitize($_POST['shopper_address']);

    // Receiver info
    $receiverName = sanitize($_POST['receiver_name']);
    $receiverEmail = sanitize($_POST['receiver_email']);
    $receiverPhone = sanitize($_POST['receiver_phone']);
    $receiverAddress = sanitize($_POST['receiver_address']);

    // Package info
    $packageWeight = !empty($_POST['package_weight']) ? (float)$_POST['package_weight'] : null;
    $packageDimensions = sanitize($_POST['package_dimensions']);
    $shippingService = sanitize($_POST['shipping_service']);
    $shippingCost = !empty($_POST['shipping_cost']) ? (float)$_POST['shipping_cost'] : null;

    // Package picture
    $packagePicture = $shipment['package_picture']; // Keep existing picture by default
    if (isset($_FILES['package_picture']) && $_FILES['package_picture']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = '../uploads/packages/';

        // Create directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $fileName = $trackingNumber . '_' . basename($_FILES['package_picture']['name']);
        $uploadFile = $uploadDir . $fileName;

        // Check if it's an image
        $imageFileType = strtolower(pathinfo($uploadFile, PATHINFO_EXTENSION));
        $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];

        if (in_array($imageFileType, $allowedTypes) && move_uploaded_file($_FILES['package_picture']['tmp_name'], $uploadFile)) {
            // Delete old picture if it exists
            if (!empty($shipment['package_picture']) && file_exists($uploadDir . $shipment['package_picture'])) {
                unlink($uploadDir . $shipment['package_picture']);
            }
            $packagePicture = $fileName;
        }
    }

    // Validate input
    if (empty($customerName) || empty($origin) || empty($destination) || empty($status)) {
        setErrorNotification('Customer name, origin, destination, and status are required fields', 10000);
    } else {
        try {
            // Update shipment with all fields
            $stmt = $conn->prepare("UPDATE shipments SET
                        tracking_number = ?, customer_name = ?, origin = ?, destination = ?, status = ?, estimated_delivery = ?,
                        shopper_name = ?, shopper_email = ?, shopper_phone = ?, shopper_address = ?,
                        receiver_name = ?, receiver_email = ?, receiver_phone = ?, receiver_address = ?,
                        package_weight = ?, package_dimensions = ?, shipping_service = ?, shipping_cost = ?, package_picture = ?
                        WHERE id = ?");

            $result = $stmt->execute([
                $trackingNumber, $customerName, $origin, $destination, $status, $estimatedDelivery,
                $shopperName, $shopperEmail, $shopperPhone, $shopperAddress,
                $receiverName, $receiverEmail, $receiverPhone, $receiverAddress,
                $packageWeight, $packageDimensions, $shippingService, $shippingCost, $packagePicture,
                $shipmentId
            ]);

            if ($result) {
                setSuccessNotification('Shipment updated successfully', 10000);
                header('Location: shipments.php');
                exit;
            } else {
                setErrorNotification('Error updating shipment. Please try again.', 10000);
            }
        } catch (PDOException $e) {
            setErrorNotification('Database error: ' . $e->getMessage(), 10000);
        }
    }
}

// Set page title
$pageTitle = 'Edit Shipment';

// Include header
include_once 'includes/header.php';
?>

<div class="container">
    <div class="page-header">
        <h1>Edit Shipment</h1>
        <p>Update shipment information</p>
    </div>

    <div class="shipment-form glass-card">
        <form action="edit-shipment.php?id=<?php echo $shipmentId; ?>" method="POST" enctype="multipart/form-data">
            <div class="form-tabs">
                <button type="button" class="tab-btn active" data-tab="basic-info">Basic Info</button>
                <button type="button" class="tab-btn" data-tab="shopper-info">Shopper Info</button>
                <button type="button" class="tab-btn" data-tab="receiver-info">Receiver Info</button>
                <button type="button" class="tab-btn" data-tab="package-info">Package Info</button>
            </div>

            <div class="tab-content active" id="basic-info">
                <h3>Basic Information</h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tracking_number">Tracking Number</label>
                        <input type="text" id="tracking_number" name="tracking_number" value="<?php echo htmlspecialchars($shipment['tracking_number']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="customer_name">Customer Name</label>
                        <input type="text" id="customer_name" name="customer_name" value="<?php echo htmlspecialchars($shipment['customer_name']); ?>" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="origin">Origin</label>
                        <input type="text" id="origin" name="origin" value="<?php echo htmlspecialchars($shipment['origin']); ?>" required>
                    </div>

                    <div class="form-group">
                        <label for="destination">Destination</label>
                        <input type="text" id="destination" name="destination" value="<?php echo htmlspecialchars($shipment['destination']); ?>" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select id="status" name="status" required>
                            <option value="pending" <?php echo $shipment['status'] === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="in_transit" <?php echo $shipment['status'] === 'in_transit' ? 'selected' : ''; ?>>In Transit</option>
                            <option value="delivered" <?php echo $shipment['status'] === 'delivered' ? 'selected' : ''; ?>>Delivered</option>
                            <option value="delayed" <?php echo $shipment['status'] === 'delayed' ? 'selected' : ''; ?>>Delayed</option>
                            <option value="cancelled" <?php echo $shipment['status'] === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="estimated_delivery">Estimated Delivery Date</label>
                        <input type="date" id="estimated_delivery" name="estimated_delivery" value="<?php echo !empty($shipment['estimated_delivery']) ? date('Y-m-d', strtotime($shipment['estimated_delivery'])) : ''; ?>">
                    </div>
                </div>
            </div>

            <div class="tab-content" id="shopper-info">
                <h3>Shopper Information</h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="shopper_name">Shopper Name</label>
                        <input type="text" id="shopper_name" name="shopper_name" value="<?php echo htmlspecialchars($shipment['shopper_name'] ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="shopper_email">Shopper Email</label>
                        <input type="email" id="shopper_email" name="shopper_email" value="<?php echo htmlspecialchars($shipment['shopper_email'] ?? ''); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="shopper_phone">Shopper Phone</label>
                        <input type="tel" id="shopper_phone" name="shopper_phone" value="<?php echo htmlspecialchars($shipment['shopper_phone'] ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="shopper_address">Shopper Address</label>
                        <textarea id="shopper_address" name="shopper_address" rows="3"><?php echo htmlspecialchars($shipment['shopper_address'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="receiver-info">
                <h3>Receiver Information</h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="receiver_name">Receiver Name</label>
                        <input type="text" id="receiver_name" name="receiver_name" value="<?php echo htmlspecialchars($shipment['receiver_name'] ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="receiver_email">Receiver Email</label>
                        <input type="email" id="receiver_email" name="receiver_email" value="<?php echo htmlspecialchars($shipment['receiver_email'] ?? ''); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="receiver_phone">Receiver Phone</label>
                        <input type="tel" id="receiver_phone" name="receiver_phone" value="<?php echo htmlspecialchars($shipment['receiver_phone'] ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="receiver_address">Receiver Address</label>
                        <textarea id="receiver_address" name="receiver_address" rows="3"><?php echo htmlspecialchars($shipment['receiver_address'] ?? ''); ?></textarea>
                    </div>
                </div>
            </div>

            <div class="tab-content" id="package-info">
                <h3>Package Information</h3>

                <div class="form-row">
                    <div class="form-group">
                        <label for="package_weight">Package Weight (kg)</label>
                        <input type="number" step="0.01" id="package_weight" name="package_weight" value="<?php echo htmlspecialchars($shipment['package_weight'] ?? ''); ?>">
                    </div>

                    <div class="form-group">
                        <label for="package_dimensions">Package Dimensions (LxWxH cm)</label>
                        <input type="text" id="package_dimensions" name="package_dimensions" placeholder="e.g. 30x20x15" value="<?php echo htmlspecialchars($shipment['package_dimensions'] ?? ''); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="shipping_service">Shipping Service</label>
                        <select id="shipping_service" name="shipping_service">
                            <option value="">Select a service</option>
                            <option value="standard" <?php echo ($shipment['shipping_service'] ?? '') === 'standard' ? 'selected' : ''; ?>>Standard Shipping</option>
                            <option value="express" <?php echo ($shipment['shipping_service'] ?? '') === 'express' ? 'selected' : ''; ?>>Express Shipping</option>
                            <option value="priority" <?php echo ($shipment['shipping_service'] ?? '') === 'priority' ? 'selected' : ''; ?>>Priority Shipping</option>
                            <option value="economy" <?php echo ($shipment['shipping_service'] ?? '') === 'economy' ? 'selected' : ''; ?>>Economy Shipping</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="shipping_cost">Shipping Cost</label>
                        <input type="number" step="0.01" id="shipping_cost" name="shipping_cost" value="<?php echo htmlspecialchars($shipment['shipping_cost'] ?? ''); ?>">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="package_picture">Package Picture</label>
                        <?php if (!empty($shipment['package_picture'])): ?>
                            <div class="current-image">
                                <img src="<?php echo SITE_URL; ?>/uploads/packages/<?php echo htmlspecialchars($shipment['package_picture']); ?>" alt="Current Package Picture" style="max-width: 200px; margin-bottom: 10px;">
                                <p>Current image: <?php echo htmlspecialchars($shipment['package_picture']); ?></p>
                            </div>
                        <?php endif; ?>
                        <input type="file" id="package_picture" name="package_picture" accept="image/*">
                        <small>Upload a new image to replace the current one (optional)</small>
                    </div>
                </div>
            </div>

            <div class="form-actions">
                <a href="shipments.php" class="btn outline-btn">Cancel</a>
                <button type="button" id="prev-btn" class="btn outline-btn" disabled>Previous</button>
                <button type="button" id="next-btn" class="btn secondary-btn">Next</button>
                <button type="submit" name="update_shipment" id="submit-btn" class="btn primary-btn" style="display: none;">Update Shipment</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab navigation
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const submitBtn = document.getElementById('submit-btn');

        let currentTabIndex = 0;

        function showTab(index) {
            // Hide all tabs
            tabContents.forEach(content => content.classList.remove('active'));
            tabBtns.forEach(btn => btn.classList.remove('active'));

            // Show the selected tab
            tabContents[index].classList.add('active');
            tabBtns[index].classList.add('active');

            // Update buttons
            prevBtn.disabled = index === 0;

            if (index === tabContents.length - 1) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'inline-block';
            } else {
                nextBtn.style.display = 'inline-block';
                submitBtn.style.display = 'none';
            }

            currentTabIndex = index;
        }

        // Tab button click
        tabBtns.forEach((btn, index) => {
            btn.addEventListener('click', () => {
                showTab(index);
            });
        });

        // Previous button click
        prevBtn.addEventListener('click', () => {
            if (currentTabIndex > 0) {
                showTab(currentTabIndex - 1);
            }
        });

        // Next button click
        nextBtn.addEventListener('click', () => {
            if (currentTabIndex < tabContents.length - 1) {
                // Validate current tab
                if (validateTab(currentTabIndex)) {
                    showTab(currentTabIndex + 1);
                }
            }
        });

        // Validate tab fields
        function validateTab(tabIndex) {
            const tab = tabContents[tabIndex];
            const requiredFields = tab.querySelectorAll('input[required], select[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');

                    // Add error message if it doesn't exist
                    let errorMsg = field.nextElementSibling;
                    if (!errorMsg || !errorMsg.classList.contains('error-message')) {
                        errorMsg = document.createElement('div');
                        errorMsg.classList.add('error-message');
                        errorMsg.textContent = 'This field is required';
                        field.parentNode.insertBefore(errorMsg, field.nextSibling);
                    }
                } else {
                    field.classList.remove('error');

                    // Remove error message if it exists
                    const errorMsg = field.nextElementSibling;
                    if (errorMsg && errorMsg.classList.contains('error-message')) {
                        errorMsg.remove();
                    }
                }
            });

            return isValid;
        }

        // Remove error class on input
        document.querySelectorAll('input, textarea, select').forEach(field => {
            field.addEventListener('input', function() {
                this.classList.remove('error');

                // Remove error message if it exists
                const errorMsg = this.nextElementSibling;
                if (errorMsg && errorMsg.classList.contains('error-message')) {
                    errorMsg.remove();
                }
            });
        });

        // Initialize the form
        showTab(0);
    });
</script>

<style>
    .page-header {
        margin-bottom: 30px;
        padding-top: 30px;
    }

    .page-header h1 {
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .glass-card {
        background-color: var(--glass-bg);
        border-radius: 16px;
        padding: 25px;
        margin-bottom: 30px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
    }

    .form-tabs {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 25px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        padding-bottom: 15px;
    }

    .tab-btn {
        background: none;
        border: none;
        padding: 10px 15px;
        cursor: pointer;
        font-weight: 500;
        color: var(--text-secondary);
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .tab-btn:hover {
        background-color: rgba(0, 0, 0, 0.05);
        color: var(--primary-color);
    }

    .tab-btn.active {
        background-color: var(--primary-color);
        color: white;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
        animation: fadeIn 0.3s;
    }

    .tab-content h3 {
        color: var(--primary-color);
        margin-bottom: 20px;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }

    .form-group input[type="file"] {
        padding: 10px;
    }

    .form-group small {
        display: block;
        margin-top: 5px;
        color: var(--text-secondary);
        font-size: 0.8rem;
    }

    .form-group input.error,
    .form-group select.error,
    .form-group textarea.error {
        border-color: #f44336;
    }

    .error-message {
        color: #f44336;
        font-size: 0.8rem;
        margin-top: 5px;
    }

    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);
        padding-top: 20px;
    }

    .current-image {
        margin-bottom: 15px;
        padding: 10px;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.05);
    }

    .current-image img {
        border-radius: 4px;
    }

    .current-image p {
        margin-top: 5px;
        font-size: 0.9rem;
        color: var(--text-secondary);
    }

    /* Dark theme adjustments */
    .dark-theme .glass-card {
        background-color: rgba(30, 30, 40, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-theme .form-tabs {
        border-bottom-color: rgba(255, 255, 255, 0.1);
    }

    .dark-theme .tab-btn:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .dark-theme .form-group input,
    .dark-theme .form-group select,
    .dark-theme .form-group textarea {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .dark-theme .form-actions {
        border-top-color: rgba(255, 255, 255, 0.1);
    }

    .dark-theme .current-image {
        background-color: rgba(255, 255, 255, 0.05);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .form-tabs {
            overflow-x: auto;
            white-space: nowrap;
            padding-bottom: 5px;
        }

        .tab-btn {
            flex: 1;
            text-align: center;
            padding: 10px;
            font-size: 0.9rem;
        }
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
