/* Additional styles for inner pages */

/* Login Popup Styles */
.login-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    width: 90%;
    max-width: 400px;
    background-color: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    border-radius: 16px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.login-popup.active {
    opacity: 1;
    visibility: visible;
    transform: translate(-50%, -50%) scale(1);
}

.login-popup-content {
    padding: 30px;
}

.login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.login-header h3 {
    color: var(--primary-color);
    margin: 0;
}

.close-login {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-login:hover {
    color: var(--primary-color);
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.login-form input {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    background-color: rgba(255, 255, 255, 0.1);
    border: var(--glass-border);
    color: var(--text-color);
    transition: all 0.3s ease;
}

.login-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(92, 43, 226, 0.2);
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 0.9rem;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    width: auto;
    margin-right: 5px;
}

.forgot-password {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.login-submit {
    width: 100%;
}

.login-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 0.9rem;
}

.login-footer a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.login-footer a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.login-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.login-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Dark Theme Styles for Login Popup */
.dark-theme .login-popup {
    background-color: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .login-form input {
    background-color: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Active navigation link */
nav ul li a.active {
    color: var(--secondary-color);
    font-weight: 700;
}

/* Services Page Styles */
.services-details {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.service-detail-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: left;
    transition: all 0.3s ease, background-color 0.3s ease, border 0.3s ease;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-color);
    margin-bottom: 30px;
}

.service-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.service-detail-card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.service-detail-card ul {
    margin: 20px 0;
    padding-left: 20px;
}

.service-detail-card ul li {
    margin-bottom: 8px;
    position: relative;
    padding-left: 15px;
}

.service-detail-card ul li::before {
    content: "•";
    color: var(--secondary-color);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.service-detail-card .btn {
    margin-top: 15px;
    display: inline-block;
}

/* Industries Page Styles */
.industries-details {
    padding: 80px 0;
    background-color: var(--bg-secondary);
}

.industries-grid.detailed {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.industry-detail-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: left;
    transition: all 0.3s ease, background-color 0.3s ease, border 0.3s ease;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-color);
    height: 100%;
}

.industry-detail-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.industry-detail-card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.industry-detail-card h4 {
    margin: 20px 0 10px;
    color: var(--secondary-color);
}

.industry-detail-card ul {
    margin: 15px 0;
    padding-left: 20px;
}

.industry-detail-card ul li {
    margin-bottom: 8px;
    position: relative;
    padding-left: 15px;
}

.industry-detail-card ul li::before {
    content: "•";
    color: var(--secondary-color);
    font-weight: bold;
    position: absolute;
    left: 0;
}

.industry-detail-card .btn {
    margin-top: 15px;
    display: inline-block;
}

/* About Page Styles */
.about-section {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
    align-items: center;
}

.about-image img {
    width: 100%;
    border-radius: 16px;
    box-shadow: var(--box-shadow);
}

.about-text h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.about-text p {
    margin-bottom: 20px;
}

.milestone-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 20px;
    margin-top: 30px;
}

.milestone {
    text-align: center;
    padding: 15px;
    background-color: var(--glass-bg);
    border-radius: 10px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.milestone h4 {
    color: var(--secondary-color);
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.mission-section {
    padding: 80px 0;
    background-color: var(--bg-secondary);
}

.mission-values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.mission-card, .values-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    height: 100%;
}

.mission-card:hover, .values-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3);
}

.mission-icon, .values-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
}

.mission-card h3, .values-card h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.values-card ul {
    text-align: left;
    margin-top: 20px;
}

.values-card ul li {
    margin-bottom: 15px;
}

.team-section {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.team-member {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3);
}

.member-image {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
    border: 3px solid var(--secondary-color);
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.team-member h3 {
    margin-bottom: 5px;
    color: var(--primary-color);
}

.member-title {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.global-section {
    padding: 80px 0;
    background-color: var(--bg-secondary);
}

.global-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.stat-item {
    text-align: center;
    padding: 30px;
    background-color: var(--glass-bg);
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.stat-item h3 {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 10px;
}

.global-map {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.global-map img {
    width: 100%;
    display: block;
}

/* Contact Page Styles */
.contact-section {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 40px;
}

.contact-info {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.contact-info h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
}

.info-item {
    display: flex;
    margin-bottom: 25px;
}

.info-item i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-right: 15px;
    margin-top: 5px;
}

.info-item h4 {
    margin-bottom: 5px;
    color: var(--primary-color);
}

.social-contact {
    margin-top: 30px;
}

.social-contact h4 {
    margin-bottom: 15px;
    color: var(--primary-color);
}

.contact-form-container {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.contact-form .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.contact-form .form-group {
    flex: 1;
}

.contact-form label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.contact-form input,
.contact-form textarea,
.contact-form select {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: var(--glass-border);
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.contact-form textarea {
    resize: vertical;
}

.contact-form button {
    margin-top: 20px;
}

.offices-section {
    padding: 80px 0;
    background-color: var(--bg-secondary);
}

.offices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.office-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    height: 100%;
}

.office-region {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.office-region i {
    font-size: 1.5rem;
    color: var(--secondary-color);
    margin-right: 15px;
}

.office-region h3 {
    color: var(--primary-color);
}

.office-locations li {
    margin-bottom: 20px;
    padding-left: 20px;
    border-left: 2px solid var(--secondary-color);
}

.office-locations h4 {
    margin-bottom: 5px;
    color: var(--primary-color);
}

.map-section {
    padding: 80px 0;
    background-color: var(--bg-color);
}

.map-container {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
}

.map-container img {
    width: 100%;
    display: block;
}

/* Responsive styles for inner pages */
@media (max-width: 992px) {
    .about-content,
    .contact-container {
        grid-template-columns: 1fr;
    }

    .about-image {
        order: -1;
    }
}

@media (max-width: 768px) {
    .service-detail-card,
    .industry-detail-card,
    .mission-card,
    .values-card,
    .team-member,
    .office-card {
        padding: 20px;
    }

    .contact-form .form-row {
        flex-direction: column;
        gap: 10px;
    }

    .milestone-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .milestone-grid {
        grid-template-columns: 1fr;
    }
}
