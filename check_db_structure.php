<?php
/**
 * Check Database Structure
 * 
 * This script checks the structure of the shipments table
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Check Database Structure';

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-database"></i> Check Database Structure</h1>
            <div class="admin-actions">
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Shipments Table Structure</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Get the database connection
                        global $conn;
                        
                        // Get the table structure
                        $stmt = $conn->query("DESCRIBE shipments");
                        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        if (count($columns) > 0) {
                            echo "<table class='table'>";
                            echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                            echo "<tbody>";
                            
                            foreach ($columns as $column) {
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                                echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                                echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                                echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                                echo "<td>" . (isset($column['Default']) ? htmlspecialchars($column['Default']) : 'NULL') . "</td>";
                                echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
                                echo "</tr>";
                            }
                            
                            echo "</tbody></table>";
                        } else {
                            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> No columns found in the shipments table.</div>";
                        }
                    } catch (PDOException $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Database error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Add Shipment Form Fields</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Get the add shipment form code
                        $formFile = file_get_contents('admin/add-shipment.php');
                        
                        // Extract form fields
                        preg_match_all('/<input[^>]*name=["\']([^"\']*)["\'][^>]*>/', $formFile, $inputMatches);
                        preg_match_all('/<textarea[^>]*name=["\']([^"\']*)["\'][^>]*>/', $formFile, $textareaMatches);
                        preg_match_all('/<select[^>]*name=["\']([^"\']*)["\'][^>]*>/', $formFile, $selectMatches);
                        
                        $formFields = array_merge($inputMatches[1], $textareaMatches[1], $selectMatches[1]);
                        $formFields = array_unique($formFields);
                        
                        if (count($formFields) > 0) {
                            echo "<h3>Form Fields:</h3>";
                            echo "<ul>";
                            
                            foreach ($formFields as $field) {
                                echo "<li>" . htmlspecialchars($field) . "</li>";
                            }
                            
                            echo "</ul>";
                        } else {
                            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> No form fields found in the add shipment form.</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Process Shipment Code</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Get the process shipment code
                        $processFile = file_get_contents('admin/process-shipment.php');
                        
                        // Extract SQL insert statement
                        if (preg_match('/INSERT INTO\s+shipments\s+\(([^)]+)\)\s+VALUES\s+\(([^)]+)\)/i', $processFile, $matches)) {
                            $columns = explode(',', $matches[1]);
                            $values = explode(',', $matches[2]);
                            
                            echo "<h3>SQL Insert Columns:</h3>";
                            echo "<ul>";
                            
                            foreach ($columns as $column) {
                                echo "<li>" . htmlspecialchars(trim($column)) . "</li>";
                            }
                            
                            echo "</ul>";
                        } else {
                            echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> No SQL insert statement found in the process shipment code.</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="admin-actions mt-4">
            <a href="admin/manage-shipments.php" class="btn primary-btn"><i class="fas fa-box"></i> Manage Shipments</a>
            <a href="admin/index.php" class="btn secondary-btn"><i class="fas fa-arrow-left"></i> Return to Dashboard</a>
        </div>
    </div>
</section>

<style>
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-left: 4px solid #ffc107;
    color: #856404;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left: 4px solid #17a2b8;
    color: #17a2b8;
}

code, pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    padding: 10px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
}

.mt-4 {
    margin-top: 20px;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}

.table th {
    background-color: rgba(0, 0, 0, 0.05);
}
</style>

<?php include_once 'includes/footer.php'; ?>
