<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

echo "<h1>Fixing Shipment Status Format</h1>";

try {
    // Check if there are any shipments with the old format (with hyphen)
    $checkStmt = $conn->query("SELECT COUNT(*) as count FROM shipments WHERE status = 'in-transit'");
    $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "<p>Found {$result['count']} shipments with the old 'in-transit' format.</p>";
        
        // Update the status format
        $updateStmt = $conn->prepare("UPDATE shipments SET status = 'in_transit' WHERE status = 'in-transit'");
        $updateStmt->execute();
        
        echo "<p>Successfully updated {$updateStmt->rowCount()} shipments to use 'in_transit' format.</p>";
    } else {
        echo "<p>No shipments found with the old 'in-transit' format. No updates needed.</p>";
    }
    
    // Check if there are any other status values that need to be updated
    $statuses = ['pending', 'in_transit', 'delivered', 'delayed', 'cancelled'];
    $validStatusList = "'" . implode("', '", $statuses) . "'";
    
    $invalidStmt = $conn->query("SELECT id, tracking_number, status FROM shipments WHERE status NOT IN ($validStatusList)");
    $invalidShipments = $invalidStmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invalidShipments) > 0) {
        echo "<p>Found " . count($invalidShipments) . " shipments with invalid status values:</p>";
        echo "<ul>";
        
        foreach ($invalidShipments as $shipment) {
            echo "<li>Shipment #{$shipment['id']} ({$shipment['tracking_number']}): '{$shipment['status']}'</li>";
            
            // Try to normalize the status
            $normalizedStatus = str_replace('-', '_', $shipment['status']);
            
            if (in_array($normalizedStatus, $statuses)) {
                $updateInvalidStmt = $conn->prepare("UPDATE shipments SET status = ? WHERE id = ?");
                $updateInvalidStmt->execute([$normalizedStatus, $shipment['id']]);
                echo " - Updated to '$normalizedStatus'";
            } else {
                echo " - Could not automatically fix. Setting to 'pending'";
                $updateInvalidStmt = $conn->prepare("UPDATE shipments SET status = 'pending' WHERE id = ?");
                $updateInvalidStmt->execute([$shipment['id']]);
            }
        }
        
        echo "</ul>";
    } else {
        echo "<p>No shipments found with invalid status values.</p>";
    }
    
    echo "<p>Status format check completed successfully.</p>";
    echo "<p><a href='admin/index.php'>Return to Dashboard</a></p>";
    
} catch (PDOException $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
