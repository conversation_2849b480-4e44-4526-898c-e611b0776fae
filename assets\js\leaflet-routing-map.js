// Enhanced Tracking Map with Leaflet Routing Machine and Ant Path

// Enhanced Tracking Map with Leaflet Routing Machine and Ant Path
// This file uses the custom router implementation from leaflet-custom-routing.js

// Function to create air routes (curved arcs)
function createAirRoute(waypoints, callback) {
    console.log('Creating air route with waypoints:', waypoints);
    const routes = [];
    if (waypoints && waypoints.length >= 2) {
        const coordinates = [];
        // Make sure we have valid latLng objects
        const start = waypoints[0].latLng || waypoints[0];
        const end = waypoints[waypoints.length - 1].latLng || waypoints[waypoints.length - 1];

        // Create a curved arc for flight path
        const distance = start.distanceTo(end) / 1000; // km
        const midPoint = L.latLng(
            (start.lat + end.lat) / 2,
            (start.lng + end.lng) / 2
        );

        // Higher arc for longer distances
        const arcHeight = Math.min(distance * 0.15, 500) / 1000; // Max 500km height
        const numPoints = Math.max(Math.ceil(distance / 500), 10); // More points for longer distances

        // Generate arc points
        for (let i = 0; i <= numPoints; i++) {
            const t = i / numPoints;
            const lat = start.lat * (1 - t) * (1 - t) + midPoint.lat * 2 * (1 - t) * t + end.lat * t * t;
            const lng = start.lng * (1 - t) * (1 - t) + midPoint.lng * 2 * (1 - t) * t + end.lng * t * t;

            // Add altitude component (arc)
            const alt = Math.sin(Math.PI * t) * arcHeight;
            const adjustedLat = lat + alt;

            coordinates.push(L.latLng(adjustedLat, lng));
        }

        routes.push({
            coordinates: coordinates,
            waypoints: waypoints,
            name: 'Air Route',
            summary: {
                totalDistance: distance * 1000,
                totalTime: distance * 60 * 4 // Rough estimate: 4 minutes per km for flights
            }
        });
    }
    callback(null, routes);
}

// Function to create sea routes
function createSeaRoute(waypoints, callback) {
    const routes = [];
    if (waypoints.length >= 2) {
        const coordinates = [];
        const start = waypoints[0].latLng;
        const end = waypoints[waypoints.length - 1].latLng;

        // Create a slightly curved path for sea routes
        const distance = start.distanceTo(end) / 1000; // km
        const numPoints = Math.max(Math.ceil(distance / 300), 8); // More points for longer distances

        // Generate curved path with small waves
        for (let i = 0; i <= numPoints; i++) {
            const t = i / numPoints;
            const lat = start.lat * (1 - t) + end.lat * t;
            const lng = start.lng * (1 - t) + end.lng * t;

            // Add small wave pattern
            const waveAmplitude = 0.05; // Small waves
            const waveFrequency = 10; // Higher frequency = more waves
            const wave = Math.sin(t * Math.PI * waveFrequency) * waveAmplitude;

            coordinates.push(L.latLng(lat + wave, lng));
        }

        routes.push({
            coordinates: coordinates,
            waypoints: waypoints,
            name: 'Sea Route',
            summary: {
                totalDistance: distance * 1000,
                totalTime: distance * 60 * 10 // Rough estimate: 10 minutes per km for sea transport
            }
        });
    }
    callback(null, routes);
}

// Function to create road routes
function createRoadRoute(waypoints, callback) {
    const routes = [];
    if (waypoints.length >= 2) {
        // For multiple waypoints, create a route that follows roads
        const coordinates = [];

        // Process each segment between waypoints
        for (let i = 0; i < waypoints.length - 1; i++) {
            const start = waypoints[i].latLng;
            const end = waypoints[i + 1].latLng;

            // Calculate distance for this segment
            const segmentDistance = start.distanceTo(end) / 1000; // km

            // For longer segments, add intermediate points to simulate road following
            const numPoints = Math.max(Math.ceil(segmentDistance / 50), 5); // More points for longer distances

            // Generate points with slight randomness to simulate roads
            for (let j = 0; j <= numPoints; j++) {
                const t = j / numPoints;

                // Linear interpolation between start and end
                let lat = start.lat * (1 - t) + end.lat * t;
                let lng = start.lng * (1 - t) + end.lng * t;

                // Add small random deviation to simulate roads
                // More deviation in the middle, less at endpoints
                if (j > 0 && j < numPoints) {
                    const deviation = 0.01 * Math.sin(t * Math.PI); // Max deviation
                    lat += (Math.random() * 2 - 1) * deviation;
                    lng += (Math.random() * 2 - 1) * deviation;
                }

                coordinates.push(L.latLng(lat, lng));
            }
        }

        // Calculate total distance
        let totalDistance = 0;
        for (let i = 0; i < coordinates.length - 1; i++) {
            totalDistance += coordinates[i].distanceTo(coordinates[i + 1]);
        }

        routes.push({
            coordinates: coordinates,
            waypoints: waypoints,
            name: 'Road Route',
            summary: {
                totalDistance: totalDistance,
                totalTime: totalDistance / 1000 * 60 * 1.5 // Rough estimate: 1.5 minutes per km for road transport
            }
        });
    }
    callback(null, routes);
}



// Function to create rail routes
function createRailRoute(waypoints, callback) {
    const routes = [];
    if (waypoints.length >= 2) {
        // For multiple waypoints, create a route that follows rail lines
        const coordinates = [];

        // Process each segment between waypoints
        for (let i = 0; i < waypoints.length - 1; i++) {
            const start = waypoints[i].latLng;
            const end = waypoints[i + 1].latLng;

            // Calculate distance for this segment
            const segmentDistance = start.distanceTo(end) / 1000; // km

            // For longer segments, add intermediate points to simulate rail lines
            const numPoints = Math.max(Math.ceil(segmentDistance / 100), 4); // Fewer points for straighter lines

            // Generate points with very slight randomness to simulate rail lines (straighter than roads)
            for (let j = 0; j <= numPoints; j++) {
                const t = j / numPoints;

                // Linear interpolation between start and end
                let lat = start.lat * (1 - t) + end.lat * t;
                let lng = start.lng * (1 - t) + end.lng * t;

                // Add very small random deviation to simulate rail lines (much straighter than roads)
                if (j > 0 && j < numPoints) {
                    const deviation = 0.005 * Math.sin(t * Math.PI); // Very small deviation
                    lat += (Math.random() * 2 - 1) * deviation;
                    lng += (Math.random() * 2 - 1) * deviation;
                }

                coordinates.push(L.latLng(lat, lng));
            }
        }

        // Calculate total distance
        let totalDistance = 0;
        for (let i = 0; i < coordinates.length - 1; i++) {
            totalDistance += coordinates[i].distanceTo(coordinates[i + 1]);
        }

        routes.push({
            coordinates: coordinates,
            waypoints: waypoints,
            name: 'Rail Route',
            summary: {
                totalDistance: totalDistance,
                totalTime: totalDistance / 1000 * 60 * 2 // Rough estimate: 2 minutes per km for rail transport
            }
        });
    }
    callback(null, routes);
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Leaflet Routing Machine map script loaded');

    // Check if tracking map element exists
    const mapElement = document.getElementById('tracking-map');
    if (!mapElement) {
        console.error('Map element not found');
        return;
    }

    // Log map element dimensions and visibility
    console.log('Map element found:', mapElement);
    console.log('Map element dimensions:', mapElement.offsetWidth, 'x', mapElement.offsetHeight);
    console.log('Map element style:', window.getComputedStyle(mapElement));

    // Force map element to be visible with proper dimensions
    mapElement.style.height = '500px';
    mapElement.style.width = '100%';
    mapElement.style.position = 'relative';
    mapElement.style.zIndex = '1';
    mapElement.style.backgroundColor = '#f8f9fa';

    // Check if tracking updates exist
    if (typeof trackingUpdates === 'undefined') {
        console.log('No tracking updates defined');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    console.log('Tracking updates:', trackingUpdates);

    // Function to check if any tracking update has coordinates
    function hasCoordinates(updates) {
        if (!updates || updates.length === 0) return false;

        for (let i = 0; i < updates.length; i++) {
            if (updates[i].latitude && updates[i].longitude) {
                return true;
            }
        }
        return false;
    }

    // Check if tracking updates have coordinates
    if (!hasCoordinates(trackingUpdates)) {
        console.log('No coordinates found in tracking updates');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    // Initialize the map with explicit options
    console.log('Initializing map...');
    const map = L.map('tracking-map', {
        zoomControl: false, // We'll add it in a different position
        center: [39.8283, -98.5795],
        zoom: 4,
        minZoom: 2,
        maxZoom: 18,
        zoomSnap: 0.5,
        zoomDelta: 0.5,
        trackResize: true,
        renderer: L.canvas()
    });

    // Log map initialization
    console.log('Map initialized:', map);

    // Add zoom control to the top-right
    L.control.zoom({
        position: 'topright'
    }).addTo(map);

    // Use OpenStreetMap tiles directly (more reliable)
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
    }).addTo(map);

    // Add a console log to check if tiles are loading
    console.log('Map tiles should be loading now');

    // Custom marker icons with Font Awesome
    const markerIcons = {
        origin: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin origin-pin'>
                    <i class='fas fa-warehouse'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        destination: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin destination-pin'>
                    <i class='fas fa-flag-checkered'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        current: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin current-pin'>
                    <i class='fas fa-truck'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        transit: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin transit-pin'>
                    <i class='fas fa-exchange-alt'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        }),
        delayed: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-pin delayed-pin'>
                    <i class='fas fa-exclamation-triangle'></i>
                  </div>`,
            iconSize: [30, 42],
            iconAnchor: [15, 42],
            popupAnchor: [0, -35]
        })
    };

    // Sort tracking updates by timestamp (oldest first for proper path creation)
    trackingUpdates.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Extract waypoints from tracking updates
    const waypoints = [];
    const markers = [];
    const waypointData = [];

    // Process tracking updates to extract waypoints
    trackingUpdates.forEach((update) => {
        // Skip if no coordinates
        if (!update.latitude || !update.longitude) {
            return;
        }

        const lat = parseFloat(update.latitude);
        const lng = parseFloat(update.longitude);

        // Add to waypoints for routing
        waypoints.push(L.latLng(lat, lng));
        waypointData.push(update);
    });

    // If we have at least origin and destination
    if (waypoints.length >= 2) {
        // Create a loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'map-loading';
        loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Calculating route...';
        mapElement.appendChild(loadingIndicator);

        // Set a timeout for route calculation
        const routeTimeout = setTimeout(function() {
            console.log('Route calculation timed out');
            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }

            // Fall back to direct line
            createFallbackRoute(waypoints, waypointData, map);

            // Show error notification
            if (typeof showNotification === 'function') {
                showNotification('warning', 'Route calculation timed out. Showing approximate path instead.', 5000);
            } else {
                console.warn('Route calculation timed out. Showing approximate path instead.');
            }
        }, 8000); // 8 second timeout

        // Determine transportation mode based on shipment data
        let transportMode = 'driving';
        if (typeof shipmentData !== 'undefined' && shipmentData.transport_mode) {
            const mode = shipmentData.transport_mode.toLowerCase();
            if (mode.includes('air') || mode.includes('flight')) {
                transportMode = 'air';
            } else if (mode.includes('sea') || mode.includes('ocean') || mode.includes('ship')) {
                transportMode = 'sea';
            } else if (mode.includes('rail') || mode.includes('train')) {
                transportMode = 'rail';
            }
        } else {
            // Try to guess from the status or location names
            const firstUpdate = waypointData[0];
            const lastUpdate = waypointData[waypointData.length - 1];

            // Removed transport mode detection logic - defaulting to 'driving' internally
            transportMode = 'driving'; // Force driving mode internally for API profile selection
        }

        // console.log('Detected transport mode:', transportMode); // Removed log
        // Configure routing based on transport mode

        // Initialize Leaflet Routing Machine with the appropriate configuration
        let router;

        // Always use the custom router with 'driving' mode configuration
        router = createCustomRouter({ mode: 'driving' });
        // Add log to confirm router object creation
        console.log('Custom router instance created:', router);
// console.log('Using router for transport mode:', transportMode); // Removed log as it's always driving now
        // Create routing control with the appropriate router
        const routingControl = L.Routing.control({
            waypoints: waypoints,
            router: router, // Explicitly pass the created router instance
            routeWhileDragging: false,
            showAlternatives: false,
            fitSelectedRoutes: true,
            show: false, // Don't show the routing interface
            lineOptions: {
                styles: [
                    {color: 'transparent', opacity: 0, weight: 0} // Make the default line invisible
                ],
                addWaypoints: false
            },
            createMarker: function() {
                return null; // Don't create default markers
            },
            router: router,
            // Disable the itinerary panel
            collapsible: true,
            collapsed: true,
            autoRoute: true,
            useZoomParameter: true,
            // Disable the instruction panel
            showInstructions: false,
            // Disable the itinerary panel
            itineraryClassName: 'hidden-itinerary'
        }).addTo(map);

        // Handle route calculation
        routingControl.on('routesfound', function(e) {
            console.log('Routes found:', e.routes);

            // Clear the timeout since we found a route
            clearTimeout(routeTimeout);

            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }

            // Check if we have a valid route
            if (e.routes && e.routes.length > 0) {
                console.log('Valid route found with coordinates:', e.routes[0].coordinates.length);
            } else {
                console.warn('No valid routes found');
            }

            // Get the calculated route
            const route = e.routes[0];
            const routeCoordinates = route.coordinates;

            // Create an animated route path based on transport mode
            console.log('Creating route path with coordinates:', routeCoordinates.length);

            // Check if we have valid coordinates
            if (!routeCoordinates || routeCoordinates.length < 2) {
                console.error('Invalid route coordinates');
                return;
            }

            // Apply the appropriate style based on transport mode
            const pathOptions = {
                weight: 5,
                opacity: 0.8,
                lineJoin: 'round'
            };

            // Set style based on transport mode
            if (transportMode === 'air') {
                // Air route style
                Object.assign(pathOptions, {
                    color: '#3366CC',
                    dashArray: '10, 10',
                    className: 'air-route'
                });
            } else if (transportMode === 'sea') {
                // Sea route style
                Object.assign(pathOptions, {
                    color: '#0077be',
                    dashArray: '5, 10',
                    className: 'sea-route'
                });
            } else if (transportMode === 'rail') {
                // Rail route style
                Object.assign(pathOptions, {
                    color: '#333333',
                    dashArray: '15, 10',
                    className: 'rail-route'
                });
            } else {
                // Road route style (default)
                Object.assign(pathOptions, {
                    color: '#5c2be2',
                    dashArray: '10, 20',
                    className: 'road-route ant-path'
                });
            }

            // Log the first and last coordinates for debugging
            console.log('Route start:', routeCoordinates[0]);
            console.log('Route end:', routeCoordinates[routeCoordinates.length - 1]);

            // Create the path with the appropriate style
            const path = L.polyline(routeCoordinates, pathOptions).addTo(map);

            // Add appropriate decorations based on transport mode
            if (transportMode === 'air') {
                // Add airplane icons along the path
                L.polylineDecorator(path, {
                    patterns: [
                        {
                            offset: 25,
                            repeat: 300,
                            symbol: L.Symbol.marker({
                                rotate: true,
                                markerOptions: {
                                    icon: L.divIcon({
                                        html: '<i class="fas fa-plane" style="color:#3366CC;"></i>',
                                        className: 'air-icon',
                                        iconSize: [20, 20]
                                    })
                                }
                            })
                        }
                    ]
                }).addTo(map);
            } else if (transportMode === 'sea') {
                // Add ship icons along the path
                L.polylineDecorator(path, {
                    patterns: [
                        {
                            offset: 25,
                            repeat: 300,
                            symbol: L.Symbol.marker({
                                rotate: true,
                                markerOptions: {
                                    icon: L.divIcon({
                                        html: '<i class="fas fa-ship" style="color:#0077be;"></i>',
                                        className: 'sea-icon',
                                        iconSize: [20, 20]
                                    })
                                }
                            })
                        }
                    ]
                }).addTo(map);
            } else if (transportMode === 'rail') {
                // Add train icons along the path
                L.polylineDecorator(path, {
                    patterns: [
                        {
                            offset: 25,
                            repeat: 300,
                            symbol: L.Symbol.marker({
                                rotate: true,
                                markerOptions: {
                                    icon: L.divIcon({
                                        html: '<i class="fas fa-train" style="color:#333333;"></i>',
                                        className: 'rail-icon',
                                        iconSize: [20, 20]
                                    })
                                }
                            })
                        }
                    ]
                }).addTo(map);
            } else {
                // Add direction arrows for road transport
                L.polylineDecorator(path, {
                    patterns: [
                        {
                            offset: 25,
                            repeat: 150,
                            symbol: L.Symbol.arrowHead({
                                pixelSize: 15,
                                polygon: false,
                                pathOptions: {
                                    stroke: true,
                                    color: '#fff',
                                    weight: 2
                                }
                            })
                        }
                    ]
                }).addTo(map);

                // Also try with AntPath if available (for road transport only)
                if (typeof L.Polyline.AntPath === 'function') {
                    try {
                        // Create the ant path with animation
                        const antPath = L.polyline.antPath(routeCoordinates, {
                            delay: 800,
                            dashArray: [10, 20],
                            weight: 5,
                            color: "#5c2be2",
                            pulseColor: "#00d45f",
                            paused: false,
                            reverse: false,
                            hardwareAccelerated: true
                        });

                        // Add it to the map but make it less visible
                        antPath.setStyle({ opacity: 0.5 }).addTo(map);
                        console.log('Added AntPath for road transport');
                    } catch (e) {
                        console.error('Error creating AntPath:', e);
                    }
                }
            }

            // Add markers for each waypoint
            waypointData.forEach((update, index) => {
                const lat = parseFloat(update.latitude);
                const lng = parseFloat(update.longitude);

                // Determine icon based on status and position
                let icon;
                const status = update.status.toLowerCase().replace('-', '_');
                const isFirst = index === 0; // First chronologically (origin)
                const isLast = index === waypointData.length - 1; // Last chronologically (current/destination)

                // Determine if this is origin, destination, or transit point
                if (isFirst) {
                    // Origin point (first chronologically)
                    icon = markerIcons.origin;
                } else if (isLast) {
                    // Current location (last chronologically)
                    if (status.includes('deliver')) {
                        icon = markerIcons.destination;
                    } else if (status.includes('delay')) {
                        icon = markerIcons.delayed;
                    } else {
                        icon = markerIcons.current;
                    }
                } else {
                    // Transit points
                    if (status.includes('delay')) {
                        icon = markerIcons.delayed;
                    } else {
                        icon = markerIcons.transit;
                    }
                }

                // Create marker
                const marker = L.marker([lat, lng], {
                    icon: icon,
                    title: update.location
                }).addTo(map);

                // Format timestamp
                const timestamp = new Date(update.timestamp);
                const formattedDate = timestamp.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric'
                });
                const formattedTime = timestamp.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                });

                // Create popup content
                const popupContent = `
                    <div class="map-info-window">
                        <h3>${update.status}</h3>
                        <p><strong>Location:</strong> ${update.location}</p>
                        <p><strong>Time:</strong> ${formattedDate} at ${formattedTime}</p>
                        ${update.notes ? `<p><strong>Notes:</strong> ${update.notes}</p>` : ''}
                    </div>
                `;

                // Add popup to marker
                marker.bindPopup(popupContent);

                // Store marker
                markers.push(marker);
            });

            // Add labels for origin and destination
            // Origin label
            L.marker([parseFloat(waypointData[0].latitude), parseFloat(waypointData[0].longitude)], {
                icon: L.divIcon({
                    className: 'map-label',
                    html: '<div>Origin</div>',
                    iconSize: [80, 20],
                    iconAnchor: [40, -10]
                })
            }).addTo(map);

            // Destination label
            const lastPoint = waypointData[waypointData.length - 1];
            L.marker([parseFloat(lastPoint.latitude), parseFloat(lastPoint.longitude)], {
                icon: L.divIcon({
                    className: 'map-label',
                    html: '<div>Current Location</div>',
                    iconSize: [120, 20],
                    iconAnchor: [60, -10]
                })
            }).addTo(map);

            // Add distance indicator
            const distanceInfo = L.control({position: 'bottomleft'});
            distanceInfo.onAdd = function() {
                const div = L.DomUtil.create('div', 'distance-info');
                // Convert meters to miles (approximate)
                const distanceMiles = Math.round(route.summary.totalDistance / 1609.34);
                div.innerHTML = `<i class="fas fa-route"></i> Total Distance: ~${distanceMiles} miles`;
                return div;
            };
            distanceInfo.addTo(map);

            // Calculate and update progress
            updateShipmentProgress(route, waypointData);
        });

        // Function to create a fallback route when routing fails or times out
        function createFallbackRoute(waypoints, waypointData, map) {
            console.log('Creating fallback route');

            // Clear any existing timeout
            clearTimeout(routeTimeout);

            // Create a smoother path between waypoints
            let smoothPath = [];

            if (waypoints.length === 2) {
                // If only origin and destination, create a curved line
                const origin = waypoints[0];
                const destination = waypoints[1];

                // Calculate midpoint with slight offset for curve
                const midLat = (origin.lat + destination.lat) / 2;
                const midLng = (origin.lng + destination.lng) / 2;

                // Add some curvature based on distance
                const distance = origin.distanceTo(destination) / 1000; // km
                const curveOffset = Math.min(distance * 0.05, 0.5); // Max 0.5 degrees offset

                // Create curved path
                smoothPath = [
                    origin,
                    L.latLng(midLat + curveOffset, midLng - curveOffset),
                    destination
                ];
            } else if (waypoints.length > 2) {
                // For multiple waypoints, connect them with slight curves
                for (let i = 0; i < waypoints.length - 1; i++) {
                    const start = waypoints[i];
                    const end = waypoints[i + 1];

                    smoothPath.push(start);

                    // Add intermediate point for slight curve if not the last segment
                    if (i < waypoints.length - 2) {
                        const midLat = (start.lat + end.lat) / 2;
                        const midLng = (start.lng + end.lng) / 2;
                        const distance = start.distanceTo(end) / 1000; // km
                        const curveOffset = Math.min(distance * 0.03, 0.3); // Smaller offset

                        // Alternate the curve direction for more natural look
                        const offsetSign = i % 2 === 0 ? 1 : -1;
                        smoothPath.push(L.latLng(midLat + curveOffset * offsetSign, midLng - curveOffset * offsetSign));
                    }
                }

                // Add the final point
                smoothPath.push(waypoints[waypoints.length - 1]);
            } else {
                // Just use the waypoints as is if there's only one or none
                smoothPath = waypoints;
            }

            // Create an animated path with CSS animation (more reliable)
            console.log('Creating fallback path with coordinates:', smoothPath);

            // Create a regular polyline with CSS animation
            const path = L.polyline(smoothPath, {
                color: '#5c2be2',
                weight: 5,
                opacity: 0.8,
                lineJoin: 'round',
                className: 'ant-path',
                dashArray: '10, 20'
            }).addTo(map);

            // Add direction arrows
            L.polylineDecorator(path, {
                patterns: [
                    {
                        offset: 25,
                        repeat: 150,
                        symbol: L.Symbol.arrowHead({
                            pixelSize: 15,
                            polygon: false,
                            pathOptions: {
                                stroke: true,
                                color: '#fff',
                                weight: 2
                            }
                        })
                    }
                ]
            }).addTo(map);

            // Also try with AntPath if available (as a backup)
            if (typeof L.Polyline.AntPath === 'function') {
                try {
                    // Create the ant path with animation
                    const antPath = L.polyline.antPath(smoothPath, {
                        delay: 800,
                        dashArray: [10, 20],
                        weight: 5,
                        color: "#5c2be2",
                        pulseColor: "#00d45f",
                        paused: false,
                        reverse: false,
                        hardwareAccelerated: true
                    });

                    // Add it to the map but make it less visible
                    antPath.setStyle({ opacity: 0.5 }).addTo(map);
                    console.log('Added AntPath as backup for fallback route');
                } catch (e) {
                    console.error('Error creating AntPath for fallback route:', e);
                }
            }

            // Add markers for each waypoint
            waypointData.forEach((update, index) => {
                const lat = parseFloat(update.latitude);
                const lng = parseFloat(update.longitude);

                // Determine icon based on position
                let icon;
                if (index === 0) {
                    icon = markerIcons.origin;
                } else if (index === waypointData.length - 1) {
                    icon = markerIcons.destination;
                } else {
                    icon = markerIcons.transit;
                }

                // Create marker
                const marker = L.marker([lat, lng], {
                    icon: icon,
                    title: update.location
                }).addTo(map);

                // Add popup with information
                const timestamp = new Date(update.timestamp);
                const formattedDate = timestamp.toLocaleDateString();
                const formattedTime = timestamp.toLocaleTimeString();

                marker.bindPopup(`
                    <div class="map-info-window">
                        <h3>${update.status}</h3>
                        <p><strong>Location:</strong> ${update.location}</p>
                        <p><strong>Time:</strong> ${formattedDate} at ${formattedTime}</p>
                        ${update.notes ? `<p><strong>Notes:</strong> ${update.notes}</p>` : ''}
                    </div>
                `);
            });

            // Add labels for origin and destination
            // Origin label
            L.marker([parseFloat(waypointData[0].latitude), parseFloat(waypointData[0].longitude)], {
                icon: L.divIcon({
                    className: 'map-label',
                    html: '<div>Origin</div>',
                    iconSize: [80, 20],
                    iconAnchor: [40, -10]
                })
            }).addTo(map);

            // Destination label
            const lastPoint = waypointData[waypointData.length - 1];
            L.marker([parseFloat(lastPoint.latitude), parseFloat(lastPoint.longitude)], {
                icon: L.divIcon({
                    className: 'map-label',
                    html: '<div>Current Location</div>',
                    iconSize: [120, 20],
                    iconAnchor: [60, -10]
                })
            }).addTo(map);

            // Fit map to waypoints
            map.fitBounds(L.latLngBounds(waypoints), {
                padding: [50, 50]
            });

            // Calculate approximate distance
            let totalDistance = 0;
            for (let i = 0; i < waypoints.length - 1; i++) {
                totalDistance += waypoints[i].distanceTo(waypoints[i+1]);
            }

            // Add distance info
            const distanceInfo = L.control({position: 'bottomleft'});
            distanceInfo.onAdd = function() {
                const div = L.DomUtil.create('div', 'distance-info');
                // Convert meters to miles (approximate)
                const distanceMiles = Math.round(totalDistance / 1609.34);
                div.innerHTML = `<i class="fas fa-route"></i> Total Distance: ~${distanceMiles} miles`;
                return div;
            };
            distanceInfo.addTo(map);

            // Update progress bar
            const progressElement = document.getElementById('shipment-progress-bar');
            if (progressElement) {
                // Calculate progress based on waypoint position
                const lastIndex = waypointData.length - 1;
                const progressPercentage = Math.round((lastIndex / (waypoints.length - 1)) * 100);
                progressElement.style.width = `${progressPercentage}%`;

                // Update progress text
                const progressTextElement = document.getElementById('progress-percentage');
                if (progressTextElement) {
                    progressTextElement.textContent = `${progressPercentage}%`;
                }
            }
        }

        // Handle routing errors
        routingControl.on('routingerror', function(e) {
            console.error('Routing error:', e.error);

            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }

            // Clear any existing timeout
            clearTimeout(routeTimeout);

            // Create fallback route
            createFallbackRoute(waypoints, waypointData, map);

            // Show error notification
            if (typeof showNotification === 'function') {
                showNotification('error', 'Could not calculate the exact route. Showing approximate path instead.', 5000);
            }
        });
    } else if (waypoints.length === 1) {
        // If only one point, center on it and add a marker
        const point = waypoints[0];
        const update = waypointData[0];

        map.setView(point, 10);

        // Add marker
        const marker = L.marker(point, {
            icon: markerIcons.current,
            title: update.location
        }).addTo(map);

        // Add popup with information
        const timestamp = new Date(update.timestamp);
        const formattedDate = timestamp.toLocaleDateString();
        const formattedTime = timestamp.toLocaleTimeString();

        marker.bindPopup(`
            <div class="map-info-window">
                <h3>${update.status}</h3>
                <p><strong>Location:</strong> ${update.location}</p>
                <p><strong>Time:</strong> ${formattedDate} at ${formattedTime}</p>
                ${update.notes ? `<p><strong>Notes:</strong> ${update.notes}</p>` : ''}
            </div>
        `);
    }

    // Function to update shipment progress based on route
    function updateShipmentProgress(route, waypointData) {
        const progressElement = document.getElementById('shipment-progress-bar');
        if (!progressElement) return;

        // Calculate progress percentage based on distance traveled
        const totalDistance = route.summary.totalDistance;

        // Get current location (last waypoint)
        const currentLocation = waypointData[waypointData.length - 1];
        const currentStatus = currentLocation.status.toLowerCase();

        // If delivered, set to 100%
        if (currentStatus.includes('deliver')) {
            progressElement.style.width = '100%';

            // Update progress text
            const progressTextElement = document.getElementById('progress-percentage');
            if (progressTextElement) {
                progressTextElement.textContent = '100%';
            }
            return;
        }

        // Calculate progress based on route
        // Find the closest point on the route to the current location
        const currentPoint = L.latLng(
            parseFloat(currentLocation.latitude),
            parseFloat(currentLocation.longitude)
        );

        // Find the closest point on the route
        let minDistance = Infinity;
        let closestPointIndex = 0;

        route.coordinates.forEach((coord, index) => {
            const distance = currentPoint.distanceTo(L.latLng(coord[0], coord[1]));
            if (distance < minDistance) {
                minDistance = distance;
                closestPointIndex = index;
            }
        });

        // Calculate distance traveled along the route
        let traveledDistance = 0;
        for (let i = 0; i < closestPointIndex; i++) {
            const from = L.latLng(route.coordinates[i][0], route.coordinates[i][1]);
            const to = L.latLng(route.coordinates[i+1][0], route.coordinates[i+1][1]);
            traveledDistance += from.distanceTo(to);
        }

        // Calculate progress percentage
        const progressPercentage = Math.min(Math.round((traveledDistance / totalDistance) * 100), 100);
        progressElement.style.width = `${progressPercentage}%`;

        // Update progress text
        const progressTextElement = document.getElementById('progress-percentage');
        if (progressTextElement) {
            progressTextElement.textContent = `${progressPercentage}%`;
        }
    }

    // Add delivery countdown if estimated delivery date is available
    const countdownElement = document.getElementById('delivery-countdown');
    if (countdownElement && typeof shipmentData !== 'undefined' && shipmentData.estimated_delivery) {
        const updateCountdown = function() {
            const now = new Date();
            const deliveryDate = new Date(shipmentData.estimated_delivery);
            const timeLeft = deliveryDate - now;

            if (timeLeft <= 0) {
                countdownElement.innerHTML = '<p>Delivery time has passed</p>';
                return;
            }

            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));

            countdownElement.innerHTML = `
                <div class="countdown-timer">
                    <div class="countdown-item">
                        <div class="countdown-value">${days}</div>
                        <div class="countdown-label">Days</div>
                    </div>
                    <div class="countdown-item">
                        <div class="countdown-value">${hours}</div>
                        <div class="countdown-label">Hours</div>
                    </div>
                    <div class="countdown-item">
                        <div class="countdown-value">${minutes}</div>
                        <div class="countdown-label">Minutes</div>
                    </div>
                </div>
            `;
        };

        // Update immediately and then every minute
        updateCountdown();
        setInterval(updateCountdown, 60000);
    }

    console.log('Leaflet Routing Machine map initialized successfully');
});
