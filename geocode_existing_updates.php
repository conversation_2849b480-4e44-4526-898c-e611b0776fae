<?php
/**
 * Utility script to geocode existing tracking updates that lack coordinates
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';
require_once 'includes/geocoding.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Initialize geocoding helper
$geocoder = new GeocodingHelper();

// Get tracking updates without coordinates
try {
    $stmt = $conn->query("
        SELECT id, shipment_id, location 
        FROM tracking_updates 
        WHERE (latitude IS NULL OR longitude IS NULL) 
        AND location IS NOT NULL AND location != ''
    ");
    $updates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $totalUpdates = count($updates);
    $geocodedCount = 0;
    $failedCount = 0;
    
    echo "<h1>Geocoding Existing Tracking Updates</h1>";
    echo "<p>Found {$totalUpdates} tracking updates without coordinates.</p>";
    
    if ($totalUpdates > 0) {
        echo "<div style='max-height: 400px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        
        // Process each update
        foreach ($updates as $update) {
            echo "<p>Processing update #{$update['id']} for shipment #{$update['shipment_id']}: {$update['location']}... ";
            
            // Try to geocode the location
            $result = $geocoder->geocode($update['location']);
            
            if ($result) {
                // Update the tracking update with coordinates
                $updateStmt = $conn->prepare("
                    UPDATE tracking_updates 
                    SET latitude = ?, longitude = ? 
                    WHERE id = ?
                ");
                
                $updateResult = $updateStmt->execute([
                    $result['latitude'],
                    $result['longitude'],
                    $update['id']
                ]);
                
                if ($updateResult) {
                    echo "<span style='color: green;'>Success!</span> Coordinates: {$result['latitude']}, {$result['longitude']}</p>";
                    $geocodedCount++;
                } else {
                    echo "<span style='color: red;'>Failed to update database.</span></p>";
                    $failedCount++;
                }
            } else {
                echo "<span style='color: red;'>Failed to geocode.</span></p>";
                $failedCount++;
            }
            
            // Flush output buffer to show progress
            flush();
            
            // Add a small delay to avoid overwhelming the geocoding service
            usleep(500000); // 0.5 seconds
        }
        
        echo "</div>";
        
        echo "<h2>Summary</h2>";
        echo "<p>Successfully geocoded: {$geocodedCount} updates</p>";
        echo "<p>Failed to geocode: {$failedCount} updates</p>";
    }
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='admin/index.php' class='btn'>Return to Admin Dashboard</a>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<h1>Error</h1>";
    echo "<p>An error occurred: " . $e->getMessage() . "</p>";
    echo "<a href='admin/index.php' class='btn'>Return to Admin Dashboard</a>";
}
?>
