/* Enhanced Tracking Map Styles */

/* Map container */
.tracking-map-container {
    margin-bottom: 40px;
}

/* Map loading indicator */
.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.8);
    padding: 10px 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    font-weight: 500;
    color: var(--primary-color);
}

.map-loading i {
    margin-right: 8px;
}

.dark-theme .map-loading {
    background-color: rgba(30, 30, 30, 0.8);
    color: #fff;
}

.tracking-map-container h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.8rem;
}

.map-section-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

@media (max-width: 992px) {
    .map-section-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    #tracking-map {
        height: 400px !important;
    }
}

#tracking-map {
    height: 500px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    position: relative;
}

/* Custom marker styles - Simple dot design */
.marker-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -8px 0 0 -8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    border: 2px solid white;
}

/* Marker colors for different types */
.origin-dot {
    background: #28a745; /* Green for origin */
}

.destination-dot {
    background: #dc3545; /* Red for destination */
}

.current-dot {
    background: #007bff; /* Blue for current location */
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    /* Pulsing animation for current location */
    animation: pulse 2s infinite;
}

.transit-dot {
    background: #5c2be2; /* Purple for transit points */
}

.delayed-dot {
    background: #fd7e14; /* Orange for delayed points */
}

/* Pulsing animation for current location dot */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

/* Map marker legend */
.map-legend {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    font-size: 12px;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark-theme .map-legend {
    background-color: rgba(30, 30, 30, 0.6);
    color: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.3);
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 3px;
    font-size: 11px;
}

.legend-item:last-child {
    margin-bottom: 0;
}

.legend-color {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
}

.legend-color.origin {
    background: #28a745;
}

.legend-color.destination {
    background: #dc3545;
}

.legend-color.current {
    background: #007bff;
}

.legend-color.transit {
    background: #5c2be2;
}

.legend-color.delayed {
    background: #fd7e14;
}

/* Dark theme adjustments for legend colors */
.dark-theme .legend-color {
    border-color: rgba(0, 0, 0, 0.5);
    box-shadow: 0 0 3px rgba(255, 255, 255, 0.2);
}

/* Map info window */
.map-info-window {
    padding: 10px;
    font-family: var(--body-font);
}

.map-info-window h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.map-info-window p {
    margin-bottom: 6px;
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-color);
}

/* Enhanced popup styling */
.enhanced-popup .leaflet-popup-content-wrapper {
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
    padding: 0;
    overflow: hidden;
}

.dark-theme .enhanced-popup .leaflet-popup-content-wrapper {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.3);
}

.enhanced-popup .leaflet-popup-content {
    margin: 0;
    width: 280px !important;
}

.enhanced-popup .leaflet-popup-tip {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.dark-theme .enhanced-popup .leaflet-popup-tip {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.3);
}

/* Popup header */
.popup-header {
    background: rgba(92, 43, 226, 0.1);
    padding: 10px;
    border-bottom: 1px solid rgba(92, 43, 226, 0.2);
}

.dark-theme .popup-header {
    background: rgba(92, 43, 226, 0.2);
    border-bottom: 1px solid rgba(92, 43, 226, 0.3);
}

/* Popup content */
.popup-content {
    padding: 10px;
}

/* Popup badges */
.popup-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.marker-type, .transport-mode {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-theme .marker-type, .dark-theme .transport-mode {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.marker-type.origin-type {
    color: #28a745;
}

.marker-type.destination-type {
    color: #dc3545;
}

.marker-type.current-type {
    color: #007bff;
}

.marker-type.transit-type {
    color: #5c2be2;
}

.transport-mode i {
    margin-right: 4px;
}

.transport-mode.air-mode {
    color: #00bcd4;
}

.transport-mode.sea-mode {
    color: #3f51b5;
}

.transport-mode.rail-mode {
    color: #9c27b0;
}

.transport-mode.road-mode {
    color: #007bff;
}

/* Current badge */
.current-badge {
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.65rem;
    font-weight: 500;
    margin-left: 5px;
}

/* Popup footer */
.popup-footer {
    margin-top: 5px;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    font-size: 0.75rem;
    color: #666;
    text-align: center;
    font-style: italic;
}

.dark-theme .popup-footer {
    border-top-color: rgba(255, 255, 255, 0.1);
    color: #aaa;
}

/* Coordinates link */
.coordinate-link {
    cursor: pointer;
    color: var(--primary-color);
    text-decoration: underline;
    text-decoration-style: dotted;
}

.map-link {
    margin-left: 5px;
    color: var(--primary-color);
}

/* Map labels */
.map-label div {
    background-color: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    padding: 3px 8px;
    font-size: 11px;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    white-space: nowrap;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.dark-theme .map-label div {
    background-color: rgba(30, 30, 30, 0.6);
    color: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.3);
}

/* Distance and time info */
.distance-info, .time-info {
    background-color: rgba(255, 255, 255, 0.6);
    padding: 8px 12px;
    border-radius: 8px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.distance-info i, .time-info i {
    color: var(--primary-color);
    margin-right: 5px;
}

.dark-theme .distance-info, .dark-theme .time-info {
    background-color: rgba(30, 30, 30, 0.6);
    color: #f8f9fa;
    border: 1px solid rgba(0, 0, 0, 0.3);
}

/* Current location info card */
.location-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
}

.location-card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.location-details {
    margin-top: 20px;
}

.location-item {
    display: flex;
    margin-bottom: 15px;
}

.location-item:last-child {
    margin-bottom: 0;
}

.location-item i {
    color: var(--primary-color);
    font-size: 1.2rem;
    margin-right: 15px;
    margin-top: 3px;
    width: 20px;
    text-align: center;
}

.location-item h4 {
    margin-bottom: 5px;
    color: var(--text-color);
    font-size: 1rem;
}

.location-item p {
    color: var(--text-secondary);
}

.no-location-data {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    text-align: center;
}

.no-location-data i {
    color: var(--primary-color);
    margin-right: 5px;
}

/* Ant path animation */
.ant-path {
    animation: dash 30s linear infinite;
    stroke-dasharray: 10, 20;
    stroke-linecap: round;
}

@keyframes dash {
    0% {
        stroke-dashoffset: 1000;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

/* Additional animation for ant path */
.ant-path-animated {
    stroke-dasharray: 10, 20;
    stroke-linecap: round;
    animation: antDash 30s linear infinite;
}

@keyframes antDash {
    to {
        stroke-dashoffset: -1000;
    }
}

/* Transportation mode specific styles */
.air-route {
    stroke-dasharray: 10, 10;
    stroke-linecap: round;
    animation: fly 60s linear infinite;
}

@keyframes fly {
    0% {
        stroke-dashoffset: 1000;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

.sea-route {
    stroke-dasharray: 5, 10;
    stroke-linecap: round;
    animation: sail 90s linear infinite;
}

@keyframes sail {
    0% {
        stroke-dashoffset: 1000;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

.rail-route {
    stroke-dasharray: 15, 10;
    stroke-linecap: round;
    animation: rail 45s linear infinite;
}

@keyframes rail {
    0% {
        stroke-dashoffset: 1000;
    }
    100% {
        stroke-dashoffset: 0;
    }
}

.road-route {
    stroke-dasharray: 10, 20;
    stroke-linecap: round;
    animation: dash 30s linear infinite;
}

/* Transportation mode icons */
.air-icon, .sea-icon, .rail-icon {
    font-size: 16px;
    color: white;
    text-align: center;
}

.air-icon i {
    transform: rotate(45deg);
}

.sea-icon i {
    transform: rotate(0deg);
}

.rail-icon i {
    transform: rotate(0deg);
}

/* Make sure the map container has a proper height and is visible */
#tracking-map {
    height: 500px !important;
    width: 100% !important;
    z-index: 5;
    background-color: #f8f9fa;
}

/* Ensure the map tiles are visible */
.leaflet-tile-container img {
    width: 256px !important;
    height: 256px !important;
}

/* Map type control */
.map-type-control {
    background-color: rgba(255, 255, 255, 0.6);
    padding: 5px;
    border-radius: 8px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.map-type-control select {
    border: none;
    background-color: transparent;
    font-size: 12px;
    padding: 5px;
    cursor: pointer;
    color: var(--text-color);
    font-weight: 500;
    min-width: 100px;
    outline: none;
}

.dark-theme .map-type-control {
    background-color: rgba(30, 30, 30, 0.6);
    border: 1px solid rgba(0, 0, 0, 0.3);
}

.dark-theme .map-type-control select {
    color: #f8f9fa;
}

/* Make sure the leaflet container is visible */
.leaflet-container {
    background-color: #f8f9fa !important;
}

/* Ensure SVG elements for the ant path are visible */
.leaflet-overlay-pane svg path.leaflet-ant-path {
    stroke-width: 5px !important;
    stroke: #5c2be2 !important;
    fill: none !important;
    stroke-dasharray: 10, 20 !important;
    animation: dash 30s linear infinite !important;
    stroke-linecap: round !important;
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: none !important;
}

/* Fix for path styling to prevent colored regions */
.leaflet-overlay-pane svg path {
    fill-opacity: 0 !important;
    fill: none !important;
}

/* Additional fix for paths that have been processed by our script */
.map-path-fixed {
    fill: none !important;
    fill-opacity: 0 !important;
}

/* Fallback route styling */
.fallback-route {
    stroke-dasharray: 10, 10;
    stroke-width: 4px;
    stroke: #5c2be2;
    fill: none !important;
    fill-opacity: 0 !important;
}

/* Routing path styling */
.routing-path {
    stroke-width: 5px;
    stroke: #5c2be2;
    fill: none !important;
    fill-opacity: 0 !important;
}

/* Improve arrow styling */
.leaflet-overlay-pane svg path.leaflet-interactive {
    stroke-width: 2px;
    stroke: #ffffff;
    fill: #5c2be2;
    fill-opacity: 0.8;
}

/* Style for fallback route arrows */
.fallback-route-arrow path.leaflet-interactive {
    fill: #ff6b6b !important;
}

/* Make arrows more visible */
.leaflet-marker-icon.leaflet-div-icon {
    background: transparent;
    border: none;
}

/* Ensure arrows are on top of other elements */
.leaflet-overlay-pane {
    z-index: 450 !important;
}

/* Hide the itinerary panel */
.hidden-itinerary {
    display: none !important;
}

/* Hide any routing machine controls that might appear */
.leaflet-routing-container {
    display: none !important;
}

/* Dark theme adjustments */
.dark-theme .map-label div {
    background-color: rgba(30, 30, 30, 0.8);
    color: #f8f9fa;
}

/* Dark theme styles for distance and time info moved to combined selector above */

.dark-theme .no-location-data {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Shipment Details Section */
.shipment-details-section {
    margin: 40px 0;
}

.shipment-details-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
}

@media (max-width: 992px) {
    .shipment-details-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .shipment-details-grid {
        grid-template-columns: 1fr;
    }
}

.shipment-details-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.shipment-details-card h2 {
    margin-bottom: 20px;
    color: var(--primary-color);
    font-size: 1.4rem;
    display: flex;
    align-items: center;
}

.shipment-details-card h2 i {
    margin-right: 10px;
}

.details-content {
    flex: 1;
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 15px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 5px;
    display: flex;
    align-items: center;
}

.detail-label i {
    margin-right: 8px;
    color: var(--primary-color);
}

.detail-value {
    color: var(--text-primary);
}

.contact-info .detail-value div {
    margin-bottom: 5px;
}

.contact-info .detail-value div:last-child {
    margin-bottom: 0;
}

.contact-info .detail-value i {
    width: 20px;
    text-align: center;
    margin-right: 8px;
    color: var(--primary-color);
}

.package-picture {
    margin-bottom: 20px;
    text-align: center;
}

.package-picture img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.default-package-image {
    background-color: #f8f9fa;
    padding: 10px;
    border: 1px dashed #dee2e6;
    transition: all 0.3s ease;
}

.default-package-image:hover {
    transform: scale(1.02);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.dark-theme .default-package-image {
    background-color: #2d3748;
    border-color: #4a5568;
}

.empty-state {
    padding: 20px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    text-align: center;
    color: var(--text-secondary);
}

.dark-theme .empty-state {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Shipment progress bar */
.shipment-progress {
    margin-top: 20px;
    position: relative;
}

.progress-bar-container {
    height: 8px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 4px;
    transition: width 1s ease;
}

.progress-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

/* Share tracking button */
.share-tracking {
    margin-top: 15px;
    display: flex;
    justify-content: center;
}

.share-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    font-size: 0.9rem;
    background-color: var(--glass-bg);
    color: var(--primary-color);
    border: var(--glass-border);
    transition: all 0.3s ease;
}

.share-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(92, 43, 226, 0.3);
}

.share-btn i {
    font-size: 1rem;
}

.dark-theme .share-btn {
    background-color: rgba(255, 255, 255, 0.1);
    color: #f8f9fa;
}

/* Estimated delivery countdown */
.delivery-countdown {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(92, 43, 226, 0.1);
    border-radius: 8px;
    text-align: center;
}

.countdown-timer {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.countdown-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.countdown-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary-color);
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.countdown-label {
    font-size: 0.7rem;
    margin-top: 5px;
    color: var(--text-secondary);
}

.dark-theme .countdown-value {
    background-color: rgba(30, 30, 30, 0.8);
}

.dark-theme .delivery-countdown {
    background-color: rgba(92, 43, 226, 0.2);
}

/* Weather info at destination */
.weather-info {
    margin-top: 20px;
    padding: 15px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

.weather-info h4 {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.weather-info h4 i {
    margin-right: 8px;
    color: var(--primary-color);
}

.weather-details {
    display: flex;
    align-items: center;
}

.weather-icon {
    font-size: 2rem;
    margin-right: 15px;
    color: var(--primary-color);
}

.weather-data p {
    margin-bottom: 5px;
}

.dark-theme .weather-info {
    background-color: rgba(255, 255, 255, 0.05);
}
