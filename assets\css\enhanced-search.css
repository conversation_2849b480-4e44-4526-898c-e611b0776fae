/* Enhanced Search Form and Visual Improvements
   This file contains styling improvements for the index, about us, industry, and services pages
   with a focus on search forms, visual hierarchy, gradients, and textures */

/* ===== ENHANCED SEARCH FORM STYLING ===== */

/* Tracking Box Enhancement */
.tracking-box {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
    padding: 40px;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Add subtle texture to tracking box */
.tracking-box::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(var(--primary-color) 1px, transparent 1px);
    background-size: 20px 20px;
    opacity: 0.03;
    pointer-events: none;
    z-index: -1;
}

/* Add gradient border effect */
.tracking-box::after {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color), var(--primary-light), var(--secondary-light));
    border-radius: 22px;
    z-index: -2;
    opacity: 0.5;
}

.tracking-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(31, 38, 135, 0.15);
}

/* Tracking Form Enhancement */
.tracking-form {
    margin-bottom: 25px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tracking-form .form-group {
    margin-bottom: 0;
}

.tracking-form label {
    font-weight: 600;
    margin-bottom: 30px;
    display: block;
    color: var(--primary-color);
    font-size: 0.95rem;
    letter-spacing: 0.5px;
    position: relative;
}

.tracking-form input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    color: var(--text-color);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tracking-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(92, 43, 226, 0.1);
    background: rgba(255, 255, 255, 0.15);
}

.tracking-form button {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    align-self: flex-start;
    margin-top: 10px;
}

.tracking-form button::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--secondary-color), var(--secondary-dark));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tracking-form button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(92, 43, 226, 0.2);
}

.tracking-form button:hover::before {
    opacity: 1;
}

.tracking-form button i,
.tracking-form button span {
    position: relative;
    z-index: 1;
}

/* Route Search Enhancement */
.route-search {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
}

.route-search::before {
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg,
        transparent,
        rgba(92, 43, 226, 0.3),
        rgba(0, 212, 95, 0.3),
        transparent
    );
}

.route-search h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-weight: 600;
    text-align: center;
    position: relative;
    display: inline-block;
}

.route-search h3::after {
    content: "";
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
}

.route-form {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
}

.route-form input {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 15px;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    color: var(--text-color);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
}

.route-form input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(92, 43, 226, 0.1);
    background: rgba(255, 255, 255, 0.15);
}

.route-form button {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 10px;
    padding: 15px 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    align-self: flex-start;
}

.route-form button::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, var(--secondary-color), var(--secondary-dark));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.route-form button:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(92, 43, 226, 0.2);
}

.route-form button:hover::before {
    opacity: 1;
}

.route-form button i,
.route-form button span {
    position: relative;
    z-index: 1;
}

/* Location Suggestions Enhancement */
.location-suggestions {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 0 0 10px 10px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
    max-height: 200px;
    overflow-y: auto;
}

.suggestion-item {
    padding: 12px 15px;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: rgba(92, 43, 226, 0.1);
}

/* Examples and Popular Destinations */
.tracking-examples,
.popular-destinations {
    text-align: center;
    margin-top: 15px;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.tracking-examples p,
.popular-destinations p {
    margin-bottom: 0;
}

.tracking-examples a,
.popular-destinations a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    padding: 0 2px;
}

.tracking-examples a::after,
.popular-destinations a::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.tracking-examples a:hover,
.popular-destinations a:hover {
    color: var(--secondary-color);
}

.tracking-examples a:hover::after,
.popular-destinations a:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* ===== ENHANCED VISUAL HIERARCHY ===== */

/* Section Dividers */
.section-divider {
    height: 100px;
    position: relative;
    overflow: hidden;
    margin-top: -50px;
    margin-bottom: -50px;
    z-index: 1;
}

.section-divider svg {
    position: absolute;
    width: 100%;
    height: 100px;
    fill: var(--bg-color);
}

.section-divider.dark svg {
    fill: var(--bg-secondary);
}

/* Enhanced Section Titles */
.section-title {
    position: relative;
    margin-bottom: 30px;
    padding-bottom: 15px;
    text-align: center;
    color: var(--primary-color);
}

.section-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
}

.section-subtitle {
    text-align: center;
    margin-bottom: 40px;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    color: var(--text-secondary);
}

/* ===== DARK THEME ADJUSTMENTS ===== */

.dark-theme .tracking-box {
    background: rgba(30, 30, 30, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .tracking-form input,
.dark-theme .route-form input {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--text-color);
}

.dark-theme .tracking-form input:focus,
.dark-theme .route-form input:focus {
    background: rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.dark-theme .location-suggestions {
    background: rgba(30, 30, 30, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .suggestion-item:hover {
    background: rgba(92, 43, 226, 0.2);
}

.dark-theme .route-search {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */

@media (max-width: 768px) {
    .tracking-box {
        padding: 25px;
    }

    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .tracking-form button,
    .route-form button {
        width: 100%;
        align-self: center;
    }

    /* Fix for label overlapping with input on mobile */
    .tracking-form label {
        margin-bottom: 12px;
        display: block;
        font-size: 0.9rem;
        position: relative;
    }

    .tracking-form .form-group {
        margin-bottom: 15px;
    }
}

@media (max-width: 576px) {
    .tracking-box {
        padding: 20px;
    }

    .tracking-form input,
    .route-form input {
        padding: 12px;
        margin-top: 5px;
    }

    /* Additional fixes for very small screens */
    .tracking-form label {
        margin-bottom: 15px;
        font-size: 0.85rem;
    }

    .tracking-form .form-group {
        margin-bottom: 18px;
    }
}
