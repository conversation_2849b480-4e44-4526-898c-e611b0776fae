/* CSS-based placeholder images */

/* Service Images */
.service-image img[src="assets/images/ocean-freight.jpg"],
.service-image img[src="assets/images/services/ocean-freight.jpg"] {
    background: linear-gradient(135deg, #1a2a6c, #2a4dad, #4169e1);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.service-image img[src="assets/images/ocean-freight.jpg"]::before,
.service-image img[src="assets/images/services/ocean-freight.jpg"]::before {
    content: "\f54c"; /* Ship icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.service-image img[src="assets/images/air-freight.jpg"],
.service-image img[src="assets/images/services/air-freight.jpg"] {
    background: linear-gradient(135deg, #4a00e0, #8e2de2);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.service-image img[src="assets/images/air-freight.jpg"]::before,
.service-image img[src="assets/images/services/air-freight.jpg"]::before {
    content: "\f072"; /* Plane icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.service-image img[src="assets/images/land-transport.jpg"],
.service-image img[src="assets/images/services/land-transport.jpg"] {
    background: linear-gradient(135deg, #ff7e5f, #feb47b);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.service-image img[src="assets/images/land-transport.jpg"]::before,
.service-image img[src="assets/images/services/land-transport.jpg"]::before {
    content: "\f0d1"; /* Truck icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.service-image img[src="assets/images/warehousing.jpg"],
.service-image img[src="assets/images/services/warehousing.jpg"] {
    background: linear-gradient(135deg, #396afc, #2948ff);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.service-image img[src="assets/images/warehousing.jpg"]::before,
.service-image img[src="assets/images/services/warehousing.jpg"]::before {
    content: "\f494"; /* Warehouse icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.service-image img[src="assets/images/customs.jpg"],
.service-image img[src="assets/images/services/customs.jpg"] {
    background: linear-gradient(135deg, #11998e, #38ef7d);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.service-image img[src="assets/images/customs.jpg"]::before,
.service-image img[src="assets/images/services/customs.jpg"]::before {
    content: "\f0ae"; /* Clipboard icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.service-image img[src="assets/images/supply-chain.jpg"],
.service-image img[src="assets/images/services/supply-chain.jpg"] {
    background: linear-gradient(135deg, #654ea3, #eaafc8);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.service-image img[src="assets/images/supply-chain.jpg"]::before,
.service-image img[src="assets/images/services/supply-chain.jpg"]::before {
    content: "\f0eb"; /* Globe icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

/* Industry Images */
.industry-image img[src="assets/images/retail-logistics.jpg"],
.industry-image img[src="assets/images/industries/retail-logistics.jpg"] {
    background: linear-gradient(135deg, #00b09b, #96c93d);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.industry-image img[src="assets/images/retail-logistics.jpg"]::before,
.industry-image img[src="assets/images/industries/retail-logistics.jpg"]::before {
    content: "\f290"; /* Shopping cart icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.industry-image img[src="assets/images/fashion-logistics.jpg"],
.industry-image img[src="assets/images/industries/fashion-logistics.jpg"] {
    background: linear-gradient(135deg, #f857a6, #ff5858);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.industry-image img[src="assets/images/fashion-logistics.jpg"]::before,
.industry-image img[src="assets/images/industries/fashion-logistics.jpg"]::before {
    content: "\f553"; /* T-shirt icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.industry-image img[src="assets/images/automotive-logistics.jpg"],
.industry-image img[src="assets/images/industries/automotive-logistics.jpg"] {
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.industry-image img[src="assets/images/automotive-logistics.jpg"]::before,
.industry-image img[src="assets/images/industries/automotive-logistics.jpg"]::before {
    content: "\f1b9"; /* Car icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.industry-image img[src="assets/images/healthcare-logistics.jpg"],
.industry-image img[src="assets/images/industries/healthcare-logistics.jpg"] {
    background: linear-gradient(135deg, #00b4db, #0083b0);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.industry-image img[src="assets/images/healthcare-logistics.jpg"]::before,
.industry-image img[src="assets/images/industries/healthcare-logistics.jpg"]::before {
    content: "\f0fa"; /* Medkit icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.industry-image img[src="assets/images/chemical-logistics.jpg"],
.industry-image img[src="assets/images/industries/chemical-logistics.jpg"] {
    background: linear-gradient(135deg, #f12711, #f5af19);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.industry-image img[src="assets/images/chemical-logistics.jpg"]::before,
.industry-image img[src="assets/images/industries/chemical-logistics.jpg"]::before {
    content: "\f0c3"; /* Flask icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

.industry-image img[src="assets/images/technology-logistics.jpg"],
.industry-image img[src="assets/images/industries/technology-logistics.jpg"] {
    background: linear-gradient(135deg, #4776e6, #8e54e9);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.industry-image img[src="assets/images/technology-logistics.jpg"]::before,
.industry-image img[src="assets/images/industries/technology-logistics.jpg"]::before {
    content: "\f2db"; /* Microchip icon */
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 5rem;
    color: rgba(255, 255, 255, 0.3);
}

/* About Page Images */
.about-image img[src="assets/images/about-story.jpg"],
.about-image img[src="assets/images/about/about-story.jpg"] {
    background: linear-gradient(135deg, #5c258d, #4389a2);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

.presence-map img[src="assets/images/world-map.jpg"],
.presence-map img[src="assets/images/about/world-map.jpg"] {
    background: linear-gradient(135deg, #3a6186, #89253e);
    position: relative;
    min-height: 300px;
    display: block;
    border-radius: 16px;
}

/* Team Images */
.member-image img[src="assets/images/team-1.jpg"],
.member-image img[src="assets/images/team/team-1.jpg"] {
    background: linear-gradient(135deg, #5614b0, #dbd65c);
    position: relative;
    min-height: 200px;
    display: block;
    border-radius: 50%;
}

.member-image img[src="assets/images/team-2.jpg"],
.member-image img[src="assets/images/team/team-2.jpg"] {
    background: linear-gradient(135deg, #1d976c, #93f9b9);
    position: relative;
    min-height: 200px;
    display: block;
    border-radius: 50%;
}

.member-image img[src="assets/images/team-3.jpg"],
.member-image img[src="assets/images/team/team-3.jpg"] {
    background: linear-gradient(135deg, #ff5f6d, #ffc371);
    position: relative;
    min-height: 200px;
    display: block;
    border-radius: 50%;
}

.member-image img[src="assets/images/team-4.jpg"],
.member-image img[src="assets/images/team/team-4.jpg"] {
    background: linear-gradient(135deg, #36d1dc, #5b86e5);
    position: relative;
    min-height: 200px;
    display: block;
    border-radius: 50%;
}

/* Fix for image display */
.service-image img,
.industry-image img,
.about-image img,
.presence-map img,
.member-image img {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-image img:hover,
.industry-image img:hover,
.about-image img:hover,
.presence-map img:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-xl);
}

.member-image img {
    border-radius: 50%;
    aspect-ratio: 1/1;
    object-fit: cover;
}

/* Dark theme adjustments */
.dark-theme .service-image img::before,
.dark-theme .industry-image img::before {
    color: rgba(255, 255, 255, 0.2);
}
