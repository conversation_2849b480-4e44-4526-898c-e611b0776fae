<?php
/**
 * Charts Module
 * 
 * Displays all dashboard charts
 */

// If stats are not passed, fetch them
if (!isset($stats)) {
    // Get shipment statistics
    $db->query("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM shipments");
    $stats = $db->single();

    // Debug: Check what statuses exist in the database
    $db->query("SELECT DISTINCT status FROM shipments");
    $existingStatuses = $db->resultSet();

    // If query failed, initialize with default values
    if (!$stats) {
        $stats = [
            'total' => 0,
            'pending' => 0,
            'in_transit' => 0,
            'delivered' => 0,
            'delayed' => 0,
            'cancelled' => 0
        ];
    }
}

// If monthly data is not passed, fetch it
if (!isset($monthlyShipments)) {
    // Get monthly shipment data for the current year
    $currentYear = date('Y');
    $db->query("SELECT
                MONTH(created_at) as month,
                COUNT(*) as count
                FROM shipments
                WHERE YEAR(created_at) = :year
                GROUP BY MONTH(created_at)
                ORDER BY month");
    $db->bind(':year', $currentYear);
    $monthlyData = $db->resultSet();

    // If query failed, initialize with empty array
    if (!$monthlyData) {
        $monthlyData = [];
    }

    // Format monthly data for chart
    $monthlyShipments = array_fill(0, 12, 0); // Initialize with zeros for all months
    foreach ($monthlyData as $data) {
        $monthIndex = (int)$data['month'] - 1; // Convert to 0-based index
        $monthlyShipments[$monthIndex] = (int)$data['count'];
    }
}

// If top destinations are not passed, fetch them
if (!isset($topDestinations)) {
    // Get top destinations
    $db->query("SELECT
                destination,
                COUNT(*) as count
                FROM shipments
                GROUP BY destination
                ORDER BY count DESC
                LIMIT 5");
    $topDestinations = $db->resultSet();

    // If query failed, initialize with empty array
    if (!$topDestinations) {
        $topDestinations = [];
    }
}
?>

<!-- Shipment Status Chart -->
<div class="chart-container">
    <div class="chart-header">
        <h2>Shipment Status Distribution</h2>
        <div class="header-actions">
            <button class="refresh-stats" title="Refresh Chart" data-module="status_chart"><i class="fas fa-sync-alt"></i></button>
        </div>
    </div>

    <?php if (isset($existingStatuses) && !empty($existingStatuses)): ?>
    <!-- Debug Info -->
    <div class="debug-info" style="margin-bottom: 15px; font-size: 0.8rem; color: #666;">
        <p><strong>Debug:</strong> Stats data:
            Total: <?php echo $stats['total'] ?? 0; ?>,
            Pending: <?php echo $stats['pending'] ?? 0; ?>,
            In Transit: <?php echo $stats['in_transit'] ?? 0; ?>,
            Delivered: <?php echo $stats['delivered'] ?? 0; ?>,
            Delayed: <?php echo $stats['delayed'] ?? 0; ?>,
            Cancelled: <?php echo $stats['cancelled'] ?? 0; ?>
        </p>
        <p><strong>Existing Statuses:</strong>
            <?php foreach($existingStatuses as $status): ?>
                <?php echo $status['status']; ?>,
            <?php endforeach; ?>
        </p>
    </div>
    <?php endif; ?>

    <div class="chart-wrapper">
        <canvas id="statusChart"></canvas>
    </div>
</div>

<!-- Monthly Shipments Chart -->
<div class="chart-container">
    <div class="chart-header">
        <h2>Monthly Shipment Volume</h2>
        <div class="header-actions">
            <button class="refresh-stats" title="Refresh Chart" data-module="monthly_chart"><i class="fas fa-sync-alt"></i></button>
            <a href="manage-shipments.php" class="view-all">View All Shipments</a>
        </div>
    </div>
    <div class="chart-wrapper">
        <canvas id="monthlyChart"></canvas>
    </div>
</div>

<!-- Top Destinations Chart -->
<div class="chart-container">
    <div class="chart-header">
        <h2>Top Destinations</h2>
        <div class="header-actions">
            <button class="refresh-stats" title="Refresh Chart" data-module="destinations_chart"><i class="fas fa-sync-alt"></i></button>
            <a href="manage-shipments.php" class="view-all">View All Shipments</a>
        </div>
    </div>
    <div class="chart-wrapper">
        <canvas id="destinationsChart"></canvas>
    </div>
</div>

<!-- Chart Initialization Script -->
<script>
// This script will be executed when the module is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts if they haven't been initialized yet
    if (typeof window.chartsInitialized === 'undefined') {
        initializeCharts();
        window.chartsInitialized = true;
    }
});

function initializeCharts() {
    // Status Chart
    initializeStatusChart();
    
    // Monthly Chart
    initializeMonthlyChart();
    
    // Destinations Chart
    initializeDestinationsChart();
}

function initializeStatusChart() {
    const statusCtx = document.getElementById('statusChart');
    if (statusCtx) {
        const statusCtx2d = statusCtx.getContext('2d');
        const statusData = {
            labels: ['Pending', 'In Transit', 'Delivered', 'Delayed', 'Cancelled'],
            datasets: [{
                label: 'Shipment Status',
                data: [
                    <?php echo $stats['pending'] ?? 0; ?>,
                    <?php echo $stats['in_transit'] ?? 0; ?>,
                    <?php echo $stats['delivered'] ?? 0; ?>,
                    <?php echo $stats['delayed'] ?? 0; ?>,
                    <?php echo $stats['cancelled'] ?? 0; ?>
                ],
                backgroundColor: [
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(0, 123, 255, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 152, 0, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 193, 7, 1)',
                    'rgba(0, 123, 255, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 152, 0, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        };

        try {
            window.statusChart = new Chart(statusCtx2d, {
                type: 'doughnut',
                data: statusData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        }
                    }
                }
            });
            console.log('Status chart created successfully');
        } catch (error) {
            console.error('Error creating status chart:', error);
        }
    } else {
        console.error('Status chart canvas element not found');
    }
}

function initializeMonthlyChart() {
    const monthlyCtx = document.getElementById('monthlyChart');
    if (monthlyCtx) {
        const monthlyCtx2d = monthlyCtx.getContext('2d');
        const monthlyData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Shipments in <?php echo date('Y'); ?>',
                data: <?php echo json_encode($monthlyShipments); ?>,
                backgroundColor: 'rgba(92, 43, 226, 0.2)',
                borderColor: 'rgba(92, 43, 226, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        };

        try {
            window.monthlyChart = new Chart(monthlyCtx2d, {
                type: 'line',
                data: monthlyData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
            console.log('Monthly chart created successfully');
        } catch (error) {
            console.error('Error creating monthly chart:', error);
        }
    } else {
        console.error('Monthly chart canvas element not found');
    }
}

function initializeDestinationsChart() {
    const destinationsCtx = document.getElementById('destinationsChart');
    if (destinationsCtx) {
        const destinationsCtx2d = destinationsCtx.getContext('2d');

        <?php
        $destinations = [];
        $counts = [];
        $colors = [];
        foreach($topDestinations as $index => $destination) {
            $destinations[] = "'" . addslashes($destination['destination']) . "'";
            $counts[] = $destination['count'];
            // Generate a color based on index
            $hue = ($index * 50) % 360;
            $colors[] = "'rgba(" . rand(50, 200) . ", " . rand(50, 200) . ", " . rand(50, 200) . ", 0.8)'";
        }
        ?>

        const destinationsData = {
            labels: [<?php echo !empty($destinations) ? implode(', ', $destinations) : ''; ?>],
            datasets: [{
                label: 'Shipment Count',
                data: [<?php echo !empty($counts) ? implode(', ', $counts) : ''; ?>],
                backgroundColor: [<?php echo !empty($colors) ? implode(', ', $colors) : ''; ?>],
                borderColor: [<?php echo !empty($colors) ? implode(', ', $colors) : ''; ?>],
                borderWidth: 1
            }]
        };

        try {
            window.destinationsChart = new Chart(destinationsCtx2d, {
                type: 'bar',
                data: destinationsData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
            console.log('Destinations chart created successfully');
        } catch (error) {
            console.error('Error creating destinations chart:', error);
        }
    } else {
        console.error('Destinations chart canvas element not found');
    }
}
</script>
