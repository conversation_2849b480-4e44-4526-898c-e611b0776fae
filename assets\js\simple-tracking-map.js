// Simple Tracking Map Script with Leaflet Routing Machine
document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple tracking map script loaded');

    // Check if tracking map element exists
    const mapElement = document.getElementById('tracking-map');
    if (!mapElement) {
        console.log('Map element not found');
        return;
    }

    // Check if tracking updates exist
    if (typeof trackingUpdates === 'undefined') {
        console.log('No tracking updates defined');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    console.log('Tracking updates:', trackingUpdates);

    // Enable debug mode for Leaflet Routing Machine
    L.Routing.Formatter.prototype.formatDistance = function(d) {
        const meters = Math.round(d);
        console.log('Route distance in meters:', meters);
        if (meters >= 10000) {
            return (meters / 1000).toFixed(1) + ' km';
        } else {
            return meters + ' m';
        }
    };

    // Simple polyline decoder implementation as fallback
    if (typeof window.polyline === 'undefined') {
        window.polyline = {
            decode: function(str, precision) {
                var index = 0,
                    lat = 0,
                    lng = 0,
                    coordinates = [],
                    shift = 0,
                    result = 0,
                    byte = null,
                    latitude_change,
                    longitude_change,
                    factor = Math.pow(10, precision || 5);

                // Coordinates have variable length when encoded, so just keep
                // track of whether we've hit the end of the string. In each
                // loop iteration, a single coordinate is decoded.
                while (index < str.length) {

                    // Reset shift, result, and byte
                    byte = null;
                    shift = 0;
                    result = 0;

                    do {
                        byte = str.charCodeAt(index++) - 63;
                        result |= (byte & 0x1f) << shift;
                        shift += 5;
                    } while (byte >= 0x20);

                    latitude_change = ((result & 1) ? ~(result >> 1) : (result >> 1));

                    shift = result = 0;

                    do {
                        byte = str.charCodeAt(index++) - 63;
                        result |= (byte & 0x1f) << shift;
                        shift += 5;
                    } while (byte >= 0x20);

                    longitude_change = ((result & 1) ? ~(result >> 1) : (result >> 1));

                    lat += latitude_change;
                    lng += longitude_change;

                    coordinates.push([lat / factor, lng / factor]);
                }

                return coordinates;
            }
        };
        console.log('Added fallback polyline decoder');
    }

    // Function to check if any tracking update has coordinates
    function hasCoordinates(updates) {
        if (!updates || updates.length === 0) return false;

        for (let i = 0; i < updates.length; i++) {
            if (updates[i].latitude && updates[i].longitude) {
                return true;
            }
        }
        return false;
    }

    // Check if tracking updates have coordinates
    if (!hasCoordinates(trackingUpdates)) {
        console.log('No coordinates found in tracking updates');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';

        // Even with no coordinates, try to update progress based on status
        const progressElement = document.getElementById('shipment-progress-bar');
        const progressTextElement = document.getElementById('progress-percentage');

        if (progressElement && progressTextElement && trackingUpdates.length > 0) {
            // Get the current status (last update)
            const sortedUpdates = [...trackingUpdates].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

            // Handle different possible formats of status
            let currentStatus = '';
            try {
                const statusRaw = sortedUpdates[sortedUpdates.length - 1].status;
                if (typeof statusRaw === 'string') {
                    currentStatus = statusRaw.toLowerCase().replace(/[-\s]/g, '_');
                } else if (typeof statusRaw === 'object' && statusRaw !== null) {
                    // If status is an object, try to get a string property
                    currentStatus = (statusRaw.name || statusRaw.value || statusRaw.status || '').toLowerCase().replace(/[-\s]/g, '_');
                }
                console.log('Current shipment status:', currentStatus);
            } catch (e) {
                console.error('Error getting shipment status:', e);
                currentStatus = 'unknown';
            }

            // Define status weights for progress calculation
            const statusWeights = {
                'processing': 10,
                'pending': 10,
                'picked_up': 20,
                'in_transit': 40,
                'arrived_at_facility': 60,
                'out_for_delivery': 80,
                'delivered': 100,
                'delayed': 60,
                'cancelled': 100
            };

            // Get progress from status
            const progressPercentage = statusWeights[currentStatus] || 0;

            // Update progress bar
            progressElement.style.width = `${progressPercentage}%`;
            progressTextElement.textContent = `${progressPercentage}%`;
            console.log(`Shipment progress (status only): ${progressPercentage}%`);
        }

        return;
    }

    // Initialize the map
    const map = L.map('tracking-map', {
        zoomControl: false // We'll add it in a different position
    }).setView([39.8283, -98.5795], 4);

    // Add zoom control to the top-right corner
    L.control.zoom({
        position: 'topright'
    }).addTo(map);

    // Define available map types
    const mapTypes = {
        'streets': {
            name: 'Streets',
            url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19
        },
        'satellite': {
            name: 'Satellite',
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 19
        },
        'terrain': {
            name: 'Terrain',
            url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png',
            attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        },
        'dark': {
            name: 'Dark',
            url: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}{r}.png',
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> &copy; <a href="http://cartodb.com/attributions">CartoDB</a>',
            maxZoom: 19
        },
        'light': {
            name: 'Light',
            url: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}{r}.png',
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> &copy; <a href="http://cartodb.com/attributions">CartoDB</a>',
            maxZoom: 19
        },
        'voyager': {
            name: 'Voyager',
            url: 'https://{s}.basemaps.cartocdn.com/rastertiles/voyager/{z}/{x}/{y}{r}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://carto.com/attributions">CARTO</a>',
            maxZoom: 19
        },
        'topo': {
            name: 'Topographic',
            url: 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png',
            attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a>',
            maxZoom: 17
        }
    };

    // Create a layer for the current map type
    let currentLayer = null;

    // Function to change map type
    function changeMapType(type) {
        // Remove current layer if it exists
        if (currentLayer) {
            map.removeLayer(currentLayer);
        }

        // Add the new layer
        const mapType = mapTypes[type] || mapTypes['streets'];
        currentLayer = L.tileLayer(mapType.url, {
            attribution: mapType.attribution,
            maxZoom: mapType.maxZoom,
            language: 'en' // Ensure map labels are in English
        }).addTo(map);
    }

    // Add map legend
    const legend = L.control({position: 'bottomright'});
    legend.onAdd = function() {
        const div = L.DomUtil.create('div', 'map-legend');
        div.innerHTML = `
            <div class="legend-item">
                <div class="legend-color origin"></div>
                <span>Origin</span>
            </div>
            <div class="legend-item">
                <div class="legend-color destination"></div>
                <span>Destination</span>
            </div>
            <div class="legend-item">
                <div class="legend-color current"></div>
                <span>Current Location</span>
            </div>
            <div class="legend-item">
                <div class="legend-color transit"></div>
                <span>Transit Point</span>
            </div>
            <div class="legend-item">
                <div class="legend-color delayed"></div>
                <span>Delayed</span>
            </div>
        `;
        return div;
    };
    legend.addTo(map);

    // Create map type control
    const mapTypeControl = L.control({position: 'topleft'});
    mapTypeControl.onAdd = function() {
        const div = L.DomUtil.create('div', 'map-type-control');
        div.innerHTML = `
            <select id="map-type-select">
                ${Object.keys(mapTypes).map(key => `<option value="${key}">${mapTypes[key].name}</option>`).join('')}
            </select>
        `;
        return div;
    };
    mapTypeControl.addTo(map);

    // Add event listener to the map type selector
    setTimeout(() => {
        const mapTypeSelect = document.getElementById('map-type-select');
        if (mapTypeSelect) {
            mapTypeSelect.addEventListener('change', function() {
                changeMapType(this.value);
            });
        }
    }, 100);

    // Initialize with Streets map
    changeMapType('streets');

    // Custom marker icons - Simple dot style
    const markerIcons = {
        origin: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot origin-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        }),
        destination: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot destination-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        }),
        current: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot current-dot'></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10],
            popupAnchor: [0, -12]
        }),
        transit: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot transit-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        }),
        delayed: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot delayed-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        })
    };

    // Process tracking updates
    const waypoints = [];
    const markers = [];

    // Sort tracking updates by timestamp (oldest first for proper routing)
    trackingUpdates.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Process tracking updates to create waypoints and markers
    trackingUpdates.forEach((update, index) => {
        // Skip if no coordinates
        if (!update.latitude || !update.longitude) {
            return;
        }

        const lat = parseFloat(update.latitude);
        const lng = parseFloat(update.longitude);

        // Add to waypoints for routing
        const waypoint = L.latLng(lat, lng);

        // Determine transportation mode based on update data
        let transportMode = 'road';
        if (update.transport_mode) {
            transportMode = update.transport_mode.toLowerCase();
        } else if (update.status) {
            const statusText = update.status.toLowerCase();
            if (statusText.includes('air') || statusText.includes('flight')) {
                transportMode = 'air';
            } else if (statusText.includes('sea') || statusText.includes('ship') || statusText.includes('vessel')) {
                transportMode = 'sea';
            } else if (statusText.includes('rail') || statusText.includes('train')) {
                transportMode = 'rail';
            }
        }

        // If primary mode is set globally, use it unless explicitly specified
        if (window.primaryMode && !update.transport_mode &&
            !transportMode.includes('air') &&
            !transportMode.includes('sea') &&
            !transportMode.includes('rail')) {
            transportMode = window.primaryMode;
        }

        // Store the transport mode with the waypoint
        waypoint.transportMode = transportMode;
        waypoints.push(waypoint);

        // Determine icon based on status and position
        let icon;
        const status = update.status.toLowerCase().replace(/[-\s]/g, '_');
        const isFirst = index === 0; // First chronologically (origin)
        const isLast = index === trackingUpdates.length - 1; // Last chronologically (destination)
        const isCurrent = update.is_current === true || isLast; // Marked as current by PHP or is the last update

        // Determine marker type for display
        let markerType = '';
        if (isCurrent) {
            markerType = 'Current Location';
        } else if (isFirst) {
            markerType = 'Origin';
        } else if (isLast) {
            markerType = 'Destination';
        } else {
            markerType = 'Transit Point';
        }

        // Set icon based on transport mode and marker type
        if (isCurrent) {
            // Current location
            if (status === 'delivered') {
                icon = markerIcons.destination;
            } else if (status === 'delayed') {
                icon = markerIcons.delayed;
            } else {
                // Use appropriate icon based on transport mode for current location
                // For simplicity, we'll use the same current-dot for all transport modes
                // but with different colors
                if (transportMode === 'air') {
                    icon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div class='marker-dot current-dot' style='background: #00bcd4;'></div>`, // Light blue for air
                        iconSize: [20, 20],
                        iconAnchor: [10, 10],
                        popupAnchor: [0, -12]
                    });
                } else if (transportMode === 'sea') {
                    icon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div class='marker-dot current-dot' style='background: #3f51b5;'></div>`, // Indigo for sea
                        iconSize: [20, 20],
                        iconAnchor: [10, 10],
                        popupAnchor: [0, -12]
                    });
                } else if (transportMode === 'rail') {
                    icon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div class='marker-dot current-dot' style='background: #9c27b0;'></div>`, // Purple for rail
                        iconSize: [20, 20],
                        iconAnchor: [10, 10],
                        popupAnchor: [0, -12]
                    });
                } else {
                    icon = markerIcons.current; // Default blue dot
                }
            }
        } else if (isFirst) {
            // Origin point
            icon = markerIcons.origin;
        } else {
            // Transit points or destination
            if (status === 'delayed') {
                icon = markerIcons.delayed;
            } else if (status === 'arrived_at_facility') {
                // Use appropriate icon based on transport mode for transit points
                // For simplicity, we'll use the same transit-dot for all transport modes
                // but with different colors
                if (transportMode === 'air') {
                    icon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div class='marker-dot transit-dot' style='background: #00bcd4;'></div>`, // Light blue for air
                        iconSize: [16, 16],
                        iconAnchor: [8, 8],
                        popupAnchor: [0, -10]
                    });
                } else if (transportMode === 'sea') {
                    icon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div class='marker-dot transit-dot' style='background: #3f51b5;'></div>`, // Indigo for sea
                        iconSize: [16, 16],
                        iconAnchor: [8, 8],
                        popupAnchor: [0, -10]
                    });
                } else if (transportMode === 'rail') {
                    icon = L.divIcon({
                        className: 'custom-div-icon',
                        html: `<div class='marker-dot transit-dot' style='background: #9c27b0;'></div>`, // Purple for rail
                        iconSize: [16, 16],
                        iconAnchor: [8, 8],
                        popupAnchor: [0, -10]
                    });
                } else {
                    icon = markerIcons.transit; // Default purple dot
                }
            } else if (status === 'out_for_delivery') {
                icon = markerIcons.current;
            } else {
                icon = markerIcons.transit;
            }
        }

        // Create marker
        const marker = L.marker([lat, lng], {
            icon: icon,
            title: update.location,
            zIndexOffset: isCurrent ? 1000 : 0 // Make current location marker appear on top
        }).addTo(map);

        // Format date for display
        const formatDate = (dateString) => {
            const date = new Date(dateString);
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        };

        // Get transportation mode display name and icon
        let transportModeDisplay = transportMode.charAt(0).toUpperCase() + transportMode.slice(1);
        let transportIcon = '';

        switch(transportMode) {
            case 'air':
                transportIcon = '<i class="fas fa-plane"></i>';
                break;
            case 'sea':
                transportIcon = '<i class="fas fa-ship"></i>';
                break;
            case 'rail':
                transportIcon = '<i class="fas fa-train"></i>';
                break;
            default:
                transportIcon = '<i class="fas fa-truck"></i>';
                break;
        }

        // Create enhanced popup content with more detailed information
        const popupContent = `
            <div class="map-info-window ${isCurrent ? 'current-location' : ''}">
                <div class="popup-header">
                    <h3>${update.status} ${isCurrent ? '<span class="current-badge">Current</span>' : ''}</h3>
                    <div class="popup-badges">
                        <span class="marker-type ${isCurrent ? 'current-type' : isFirst ? 'origin-type' : isLast ? 'destination-type' : 'transit-type'}">
                            ${markerType}
                        </span>
                        <span class="transport-mode ${transportMode}-mode">
                            ${transportIcon} ${transportModeDisplay}
                        </span>
                    </div>
                </div>

                <div class="popup-content">
                    <p><i class="fas fa-map-marker-alt"></i> <strong>Location:</strong> ${update.location}</p>
                    <p><i class="fas fa-clock"></i> <strong>Time:</strong> ${formatDate(update.timestamp)}</p>
                    ${update.notes ? `<p><i class="fas fa-info-circle"></i> <strong>Notes:</strong> ${update.notes}</p>` : ''}

                    ${update.latitude && update.longitude ?
                        `<p class="coordinates"><i class="fas fa-globe"></i> <strong>Coordinates:</strong>
                            <span title="Click to open in Google Maps" data-lat="${update.latitude}" data-lng="${update.longitude}" class="coordinate-link">
                                ${parseFloat(update.latitude).toFixed(4)}, ${parseFloat(update.longitude).toFixed(4)}
                            </span>
                            <a href="https://www.google.com/maps?q=${update.latitude},${update.longitude}" target="_blank" class="map-link" title="Open in Google Maps">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </p>` : ''
                    }
                </div>

                ${isCurrent ? `
                <div class="popup-footer">
                    <span class="update-info">Latest update</span>
                </div>` : ''}
            </div>
        `;

        // Add popup to marker with enhanced styling
        const popup = L.popup({
            maxWidth: 300,
            className: 'enhanced-popup'
        }).setContent(popupContent);

        marker.bindPopup(popup);

        // Add event listener for coordinate link clicks
        marker.on('popupopen', function() {
            const coordinateLink = document.querySelector('.coordinate-link');
            if (coordinateLink) {
                coordinateLink.addEventListener('click', function() {
                    const lat = this.getAttribute('data-lat');
                    const lng = this.getAttribute('data-lng');
                    window.open(`https://www.google.com/maps?q=${lat},${lng}`, '_blank');
                });
            }
        });

        // Store marker
        markers.push(marker);

        // Add label for origin and current location
        if (isFirst || isCurrent) {
            const labelText = isFirst ? 'Origin' : 'Current Location';
            L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'map-label',
                    html: `<div>${labelText}</div>`,
                    iconSize: [120, 20],
                    iconAnchor: [60, -10]
                })
            }).addTo(map);
        }
    });

    // Create routing if we have multiple waypoints
    if (waypoints.length > 1) {
        // Add loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'map-loading';
        loadingIndicator.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Calculating route...';
        mapElement.appendChild(loadingIndicator);

        // Log waypoints for debugging
        console.log('Routing waypoints:', waypoints.map(wp => [wp.lat, wp.lng]));

        // Create a custom router that uses the OSRM API directly
        const customRouter = {
            route: function(waypoints, callback, context) {
                // Format waypoints for OSRM API
                const formattedWaypoints = waypoints.map(wp => {
                    return wp.latLng.lng + ',' + wp.latLng.lat;
                }).join(';');

                // Construct OSRM API URL
                const url = 'https://router.project-osrm.org/route/v1/driving/' +
                    formattedWaypoints +
                    '?overview=full&geometries=polyline&steps=true';

                console.log('Requesting route from OSRM API:', url);

                // Set a timeout for the fetch request
                const fetchTimeout = 10000; // 10 seconds

                // Create an AbortController to handle timeout
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), fetchTimeout);

                // Make the request with timeout
                fetch(url, { signal: controller.signal })
                    .then(response => {
                        clearTimeout(timeoutId); // Clear the timeout
                        return response.json();
                    })
                    .then(data => {
                        if (data.code !== 'Ok') {
                            callback.call(context || this, new Error(data.message || 'OSRM request failed'), null);
                            return;
                        }

                        // Process the response
                        const route = data.routes[0];

                        // Handle polyline decoding with fallbacks
                        let coordinates = [];
                        try {
                            // Try using global polyline library
                            if (typeof polyline !== 'undefined' && polyline.decode) {
                                coordinates = polyline.decode(route.geometry).map(point => L.latLng(point[0], point[1]));
                            }
                            // Try using Leaflet's polyline utility if available
                            else if (typeof L.PolylineUtil !== 'undefined' && L.PolylineUtil.decode) {
                                coordinates = L.PolylineUtil.decode(route.geometry).map(point => L.latLng(point[0], point[1]));
                            }
                            // If we can't decode the polyline, use the raw coordinates from the API
                            else if (route.legs && route.legs.length > 0) {
                                // Extract coordinates from route legs
                                route.legs.forEach(leg => {
                                    if (leg.steps) {
                                        leg.steps.forEach(step => {
                                            if (step.geometry) {
                                                // Try to manually decode simple geometries
                                                try {
                                                    const points = JSON.parse(step.geometry);
                                                    points.forEach(point => {
                                                        coordinates.push(L.latLng(point[0], point[1]));
                                                    });
                                                } catch (e) {
                                                    console.error('Could not parse geometry:', e);
                                                }
                                            }
                                        });
                                    }
                                });
                            }
                        } catch (e) {
                            console.error('Error decoding polyline:', e);
                        }

                        // If we still don't have coordinates, create a straight line between waypoints
                        if (coordinates.length === 0) {
                            console.warn('Could not decode route geometry, falling back to straight lines');
                            coordinates = waypoints.map(wp => wp.latLng);
                        }

                        // Create a route object in the format expected by Leaflet Routing Machine
                        const routeObj = {
                            name: 'OSRM Route',
                            coordinates: coordinates,
                            // Create proper waypoint objects with indices
                            waypoints: waypoints.map((wp, i) => {
                                return {
                                    latLng: wp.latLng,
                                    name: wp.name || `Waypoint ${i+1}`,
                                    options: wp.options || {}
                                };
                            }),
                            inputWaypoints: waypoints,  // Store original waypoints
                            instructions: [],
                            summary: {
                                totalDistance: route.distance,
                                totalTime: route.duration
                            }
                        };

                        // Store route distance for progress calculation
                        window.routeTotalDistance = route.distance;

                        // Add waypoint indices
                        routeObj.waypointIndices = [];

                        // If we have waypoints from the API response
                        if (route.waypoints && route.waypoints.length) {
                            // Use the indices from the API
                            route.waypoints.forEach(wp => {
                                if (typeof wp.waypoint_index === 'number') {
                                    routeObj.waypointIndices.push(wp.waypoint_index);
                                }
                            });
                        } else {
                            // Otherwise, use first point and calculate indices for other waypoints
                            routeObj.waypointIndices = [0];

                            // Add indices for each remaining waypoint
                            for (let i = 1; i < waypoints.length; i++) {
                                try {
                                    // Find the closest point in the route to this waypoint
                                    const wp = waypoints[i];
                                    let closestIdx = 0;
                                    let closestDist = Infinity;

                                    // Use a safe distance calculation
                                    for (let j = 0; j < coordinates.length; j++) {
                                        try {
                                            // Calculate distance safely
                                            const coord = coordinates[j];
                                            const wpLat = wp.latLng.lat;
                                            const wpLng = wp.latLng.lng;
                                            const coordLat = coord.lat;
                                            const coordLng = coord.lng;

                                            // Simple Euclidean distance (not perfect but safer than distanceTo)
                                            const latDiff = wpLat - coordLat;
                                            const lngDiff = wpLng - coordLng;
                                            const dist = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);

                                            if (dist < closestDist) {
                                                closestDist = dist;
                                                closestIdx = j;
                                            }
                                        } catch (e) {
                                            console.error('Error in distance calculation:', e);
                                        }
                                    }

                                    routeObj.waypointIndices.push(closestIdx);
                                } catch (e) {
                                    console.error('Error finding waypoint index:', e);
                                    // Fallback to a reasonable index
                                    const fallbackIdx = Math.floor(coordinates.length * i / waypoints.length);
                                    routeObj.waypointIndices.push(fallbackIdx);
                                }
                            }
                        }

                        // Add instructions
                        if (route.legs) {
                            route.legs.forEach(leg => {
                                if (leg.steps) {
                                    leg.steps.forEach(step => {
                                        routeObj.instructions.push({
                                            type: step.maneuver.type,
                                            text: step.maneuver.instruction || 'Continue',
                                            distance: step.distance,
                                            time: step.duration,
                                            index: step.maneuver.location ? coordinates.findIndex(c => {
                                                return c.lat === step.maneuver.location[1] && c.lng === step.maneuver.location[0];
                                            }) : 0
                                        });
                                    });
                                }
                            });
                        }

                        callback.call(context || this, null, [routeObj]);
                    })
                    .catch(error => {
                        clearTimeout(timeoutId); // Clear the timeout

                        // Check if it's an abort error (timeout)
                        if (error.name === 'AbortError') {
                            console.error('Routing request timed out after ' + (fetchTimeout/1000) + ' seconds');
                            callback.call(context || this, new Error('Routing request timed out'), null);
                        } else {
                            console.error('Error fetching route:', error);
                            callback.call(context || this, error, null);
                        }
                    });
            }
        };

        // Initialize Leaflet Routing Machine with our custom router
        const routingControl = L.Routing.control({
            waypoints: waypoints,
            routeWhileDragging: false,
            showAlternatives: false,
            fitSelectedRoutes: true,
            show: false, // Don't show the routing interface
            lineOptions: {
                styles: [
                    {color: '#5c2be2', opacity: 0.8, weight: 5, fill: false, fillOpacity: 0}
                ],
                addWaypoints: false,
                extendToWaypoints: true,
                missingRouteTolerance: 0,
                className: 'routing-path'
            },
            createMarker: function() {
                return null; // Don't create default markers
            },
            router: customRouter
        }).addTo(map);

        // Add event listener to fix path styling after it's added to the map
        routingControl.on('routeselected', function() {
            // Find all path elements and ensure they have no fill
            setTimeout(function() {
                const pathElements = document.querySelectorAll('.leaflet-overlay-pane svg path');
                pathElements.forEach(function(path) {
                    path.setAttribute('fill-opacity', '0');
                    path.setAttribute('fill', 'none');
                });
            }, 100);
        });

        // Handle successful route calculation
        routingControl.on('routesfound', function(e) {
            console.log('Routes found:', e.routes);

            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }

            // Get the route coordinates
            const route = e.routes[0];
            const coordinates = route.coordinates;

            // First, remove any existing paths
            map.eachLayer(function(layer) {
                // Remove any ant paths or custom paths
                if (layer._path && layer._path.classList) {
                    if (layer._path.classList.contains('leaflet-ant-path') ||
                        layer._path.classList.contains('custom-ant-path') ||
                        layer._path.classList.contains('fallback-ant-path') ||
                        layer._path.classList.contains('fallback-route')) {
                        map.removeLayer(layer);
                    }
                }
            });

            // Also remove any existing custom paths
            if (window.currentAntPath && window.currentAntPath.remove) {
                window.currentAntPath.remove();
                window.currentAntPath = null;
            }

            if (window.currentFallbackPath && window.currentFallbackPath.remove) {
                window.currentFallbackPath.remove();
                window.currentFallbackPath = null;
            }

            // Try to use our custom AntPath implementation first
            if (typeof window.createCustomAntPath === 'function') {
                try {
                    // Create the custom ant path with animation
                    window.currentAntPath = window.createCustomAntPath(map, coordinates, {
                        color: '#5c2be2',
                        weight: 5,
                        opacity: 0.8,
                        dashArray: '10, 20',
                        pulseColor: '#00d45f',
                        className: 'custom-ant-path'
                    });

                    console.log('Added Custom AntPath animation');

                    // Hide the original route line to avoid duplicate paths
                    setTimeout(function() {
                        const routeLines = document.querySelectorAll('.leaflet-overlay-pane svg path:not(.custom-ant-path):not(.custom-ant-path-pulse)');
                        routeLines.forEach(function(line) {
                            if (!line.classList.contains('custom-ant-path') && !line.classList.contains('custom-ant-path-pulse')) {
                                line.style.opacity = '0';
                            }
                        });
                    }, 100);
                } catch (e) {
                    console.error('Error creating Custom AntPath:', e);

                    // Fall back to the original AntPath implementation
                    fallbackToOriginalAntPath();
                }
            } else {
                // Fall back to the original AntPath implementation
                fallbackToOriginalAntPath();
            }

            // Function to fall back to the original AntPath implementation
            function fallbackToOriginalAntPath() {
                if (typeof L.Polyline.AntPath === 'function') {
                    try {
                        // Create the ant path with animation
                        const antPath = L.polyline.antPath(coordinates, {
                            delay: 800,
                            dashArray: [10, 20],
                            weight: 5,
                            color: '#FF6B6B',
                            pulseColor: "#FFFFFF",
                            paused: false,
                            reverse: false,
                            hardwareAccelerated: true,
                            className: 'leaflet-ant-path' // Add specific class for styling
                        }).addTo(map);

                        // Store the route path for other calculations
                        window.routePath = coordinates;

                        // Ensure the path has no fill
                        if (antPath._path) {
                            antPath._path.setAttribute('fill-opacity', '0');
                            antPath._path.setAttribute('fill', 'none');

                            // Add animation directly to the path element
                            antPath._path.style.strokeDasharray = '10, 20';
                            antPath._path.style.strokeLinecap = 'round';
                            antPath._path.style.animation = 'dash 30s linear infinite';
                            antPath._path.classList.add('ant-path-animated');
                        }

                        console.log('Added original AntPath animation');
                    } catch (e) {
                        console.error('Error creating original AntPath:', e);
                    }
                }
            }

            // No direction arrows as per user preference

            // Add distance indicator
            const distanceMiles = Math.round(route.summary.totalDistance / 1609.34);
            const distanceInfo = L.control({position: 'bottomleft'});
            distanceInfo.onAdd = function() {
                const div = L.DomUtil.create('div', 'distance-info');
                div.innerHTML = `<i class="fas fa-route"></i> Total Distance: ~${distanceMiles} miles`;
                return div;
            };
            distanceInfo.addTo(map);

            // Estimated time indicator removed as requested

            // Update shipment progress
            updateShipmentProgress(trackingUpdates, coordinates);
        });

        // Handle routing errors
        routingControl.on('routingerror', function(e) {
            console.error('Routing error:', e.error);

            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }

            // Fall back to direct line
            createFallbackRoute(waypoints, map);
        });
    } else if (waypoints.length === 1) {
        // If only one point, center on it
        map.setView(waypoints[0], 10);

        // Still update the progress bar
        updateShipmentProgress(trackingUpdates, waypoints);
    }

    // Function to create a fallback route when routing fails
    function createFallbackRoute(waypoints, map) {
        console.log('Creating fallback route');

        // First, remove any existing paths
        map.eachLayer(function(layer) {
            // Remove any ant paths or custom paths
            if (layer._path && layer._path.classList) {
                if (layer._path.classList.contains('leaflet-ant-path') ||
                    layer._path.classList.contains('custom-ant-path') ||
                    layer._path.classList.contains('fallback-ant-path') ||
                    layer._path.classList.contains('fallback-route')) {
                    map.removeLayer(layer);
                }
            }
        });

        // Also remove any existing custom paths
        if (window.currentAntPath && window.currentAntPath.remove) {
            window.currentAntPath.remove();
            window.currentAntPath = null;
        }

        if (window.currentFallbackPath && window.currentFallbackPath.remove) {
            window.currentFallbackPath.remove();
            window.currentFallbackPath = null;
        }

        // Try to use our custom AntPath implementation for the fallback route
        if (typeof window.createCustomAntPath === 'function') {
            try {
                // Create the custom ant path with animation for the fallback route
                window.currentFallbackPath = window.createCustomAntPath(map, waypoints, {
                    color: '#5c2be2',
                    weight: 4,
                    opacity: 0.7,
                    dashArray: '10, 10',  // Make it dashed to indicate it's not the actual road route
                    pulseColor: '#00d45f',
                    className: 'fallback-ant-path'
                });

                console.log('Added Custom AntPath animation for fallback route');
            } catch (e) {
                console.error('Error creating Custom AntPath for fallback:', e);
                createFallbackPolyline();
            }
        } else {
            createFallbackPolyline();
        }

        // Function to create a simple polyline as fallback
        function createFallbackPolyline() {
            // Create a direct path
            const directPath = L.polyline(waypoints, {
                color: '#5c2be2',
                weight: 4,
                opacity: 0.7,
                lineJoin: 'round',
                dashArray: '10, 10',  // Make it dashed to indicate it's not the actual road route
                className: 'fallback-route',
                fill: false,
                fillOpacity: 0
            }).addTo(map);

            // Ensure the path has no fill
            if (directPath._path) {
                directPath._path.setAttribute('fill-opacity', '0');
                directPath._path.setAttribute('fill', 'none');

                // Add animation directly to the path element
                directPath._path.style.strokeDasharray = '10, 10';
                directPath._path.style.strokeLinecap = 'round';
                directPath._path.style.animation = 'dash 30s linear infinite';
                directPath._path.classList.add('ant-path-animated');
            }
        }

        // No direction arrows as per user preference

        // Fit map to path
        map.fitBounds(directPath.getBounds(), {
            padding: [50, 50]
        });

        // Add distance indicator (straight-line distance)
        if (waypoints.length >= 2) {
            // Calculate total distance
            let totalDistance = 0;
            for (let i = 0; i < waypoints.length - 1; i++) {
                totalDistance += waypoints[i].distanceTo(waypoints[i+1]);
            }

            // Convert to miles (approximate)
            const distanceMiles = Math.round(totalDistance / 1609.34);

            // Add distance info box
            const distanceInfo = L.control({position: 'bottomleft'});
            distanceInfo.onAdd = function() {
                const div = L.DomUtil.create('div', 'distance-info');
                div.innerHTML = `<i class="fas fa-route"></i> Approx. Distance: ~${distanceMiles} miles`;
                return div;
            };
            distanceInfo.addTo(map);

            // Update shipment progress
            updateShipmentProgress(trackingUpdates, waypoints);
        }
    }

    console.log('Map initialized successfully');

    /**
     * Update shipment progress based on tracking updates and route
     * @param {Array} trackingUpdates - Array of tracking updates
     * @param {Array} routeCoordinates - Array of route coordinates
     */
    function updateShipmentProgress(trackingUpdates, routeCoordinates) {
        const progressElement = document.getElementById('shipment-progress-bar');
        const progressTextElement = document.getElementById('progress-percentage');

        if (!progressElement || !progressTextElement) {
            console.log('Progress elements not found');
            return;
        }

        console.log('Updating shipment progress');

        // Sort tracking updates by timestamp (oldest first)
        const sortedUpdates = [...trackingUpdates].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        // Calculate progress based on status
        let progressPercentage = 0;

        // Define status weights for progress calculation
        const statusWeights = {
            'processing': 10,
            'pending': 10,
            'picked_up': 20,
            'in_transit': 40,
            'arrived_at_facility': 60,
            'out_for_delivery': 80,
            'delivered': 100,
            'delayed': 60,  // Assume delayed is somewhere in the middle
            'cancelled': 100  // Cancelled shipments show as 100% complete
        };

        // Get the current status (last update)
        let currentStatus = '';
        try {
            // Handle different possible formats of status
            const statusRaw = sortedUpdates[sortedUpdates.length - 1].status;
            if (typeof statusRaw === 'string') {
                currentStatus = statusRaw.toLowerCase().replace(/[-\s]/g, '_');
            } else if (typeof statusRaw === 'object' && statusRaw !== null) {
                // If status is an object, try to get a string property
                currentStatus = (statusRaw.name || statusRaw.value || statusRaw.status || '').toLowerCase().replace(/[-\s]/g, '_');
            }
            console.log('Current shipment status:', currentStatus);
        } catch (e) {
            console.error('Error getting shipment status:', e);
            currentStatus = 'unknown';
        }

        // Get the base progress from status
        progressPercentage = statusWeights[currentStatus] || 0;

        // If the shipment is in transit or out for delivery, calculate more precise progress
        if ((currentStatus === 'in_transit' || currentStatus === 'out_for_delivery' || currentStatus === 'arrived_at_facility') && routeCoordinates.length > 1) {
            // Calculate total route distance
            let totalDistance = 0;
            let traveledDistance = 0;

            // If routeCoordinates are Leaflet latLng objects
            if (routeCoordinates[0] instanceof L.LatLng) {
                for (let i = 0; i < routeCoordinates.length - 1; i++) {
                    const segmentDistance = routeCoordinates[i].distanceTo(routeCoordinates[i+1]);
                    totalDistance += segmentDistance;

                    // Only count traveled segments (up to the current location)
                    if (i < sortedUpdates.length - 1) {
                        traveledDistance += segmentDistance;
                    }
                }
            }
            // If routeCoordinates are arrays of [lat, lng]
            else if (Array.isArray(routeCoordinates[0])) {
                for (let i = 0; i < routeCoordinates.length - 1; i++) {
                    const from = L.latLng(routeCoordinates[i][0], routeCoordinates[i][1]);
                    const to = L.latLng(routeCoordinates[i+1][0], routeCoordinates[i+1][1]);
                    const segmentDistance = from.distanceTo(to);
                    totalDistance += segmentDistance;

                    // Only count traveled segments (up to the current location)
                    if (i < sortedUpdates.length - 1) {
                        traveledDistance += segmentDistance;
                    }
                }
            }

            // Calculate progress percentage based on distance traveled
            if (totalDistance > 0) {
                // Set base and max progress based on status
                let baseProgress, maxProgress;
                if (currentStatus === 'in_transit') {
                    baseProgress = 20;
                    maxProgress = 60;
                } else if (currentStatus === 'arrived_at_facility') {
                    baseProgress = 40;
                    maxProgress = 70;
                } else if (currentStatus === 'out_for_delivery') {
                    baseProgress = 60;
                    maxProgress = 90;
                } else {
                    baseProgress = 20;
                    maxProgress = 70;
                }

                // Calculate scaled progress
                const distanceProgress = (traveledDistance / totalDistance);
                const progressRange = maxProgress - baseProgress;
                progressPercentage = baseProgress + (distanceProgress * progressRange);
                console.log(`Distance progress calculation: ${traveledDistance}/${totalDistance} = ${distanceProgress.toFixed(2)} -> ${progressPercentage.toFixed(2)}%`);
            } else {
                console.log('Total distance is zero or invalid, using status-based progress only');
            }
        }

        // Ensure progress is between 0 and 100
        progressPercentage = Math.max(0, Math.min(100, Math.round(progressPercentage)));

        // Update progress bar width
        progressElement.style.width = `${progressPercentage}%`;

        // Update progress text
        progressTextElement.textContent = `${progressPercentage}%`;

        console.log(`Shipment progress: ${progressPercentage}%`);
    }
});
