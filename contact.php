<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Set page title
$pageTitle = 'Contact Us';

// Process contact form submission
$formSubmitted = false;
$formSuccess = false;
$formError = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['contact_submit'])) {
    $formSubmitted = true;

    // Get form data
    $name = isset($_POST['name']) ? sanitize($_POST['name']) : '';
    $email = isset($_POST['email']) ? sanitize($_POST['email']) : '';
    $phone = isset($_POST['phone']) ? sanitize($_POST['phone']) : '';
    $subject = isset($_POST['subject']) ? sanitize($_POST['subject']) : '';
    $message = isset($_POST['message']) ? sanitize($_POST['message']) : '';

    // Validate form data
    if (empty($name) || empty($email) || empty($message)) {
        $formError = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $formError = 'Please enter a valid email address.';
    } else {
        // Prepare form data for email
        $formData = [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'subject' => !empty($subject) ? $subject : 'Contact Form Submission',
            'message' => $message
        ];

        // Send email notification to admin
        $emailSent = sendContactFormEmail($formData);

        if ($emailSent) {
            $formSuccess = true;
            // Set success notification
            setSuccessNotification('Your message has been sent successfully. We will contact you soon!');
        } else {
            // Email failed to send
            $formSuccess = false;
            $formError = 'There was a problem sending your message. Please try again later or contact us directly by phone.';
            // Log the error
            debugLog('Failed to send contact form email', $formData);
            // Set error notification
            setErrorNotification($formError);
        }

        // Redirect to avoid form resubmission
        header('Location: contact.php?success=1');
        exit;
    }
}

// Check for success parameter in URL
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $formSubmitted = true;
    $formSuccess = true;
}

// Include header
include_once 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero gradient-hero textured">
    <div class="container">
        <div class="hero-content">
            <h1>Contact Us</h1>
            <p>Get in touch with our team for inquiries, quotes, or support.</p>
        </div>
    </div>
</section>

<!-- Contact Information Section -->
<section class=" section-spacing">
    <div class="container">
        <div class="contact-grid">
            <div class="contact-card glass-card animate-slide-up">
                <div class="contact-icon">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <h3>Our Headquarters</h3>
                <p>123 Logistics Way<br>New York, NY 10001<br>United States</p>
            </div>

            <div class="contact-card glass-card animate-slide-up">
                <div class="contact-icon">
                    <i class="fas fa-phone-alt"></i>
                </div>
                <h3>Phone</h3>
                <p>General: +****************<br>Customer Service: +****************<br>Support: +****************</p>
            </div>

            <div class="contact-card glass-card animate-slide-up">
                <div class="contact-icon">
                    <i class="fas fa-envelope"></i>
                </div>
                <h3>Email</h3>
                <p>General: <EMAIL><br>Sales: <EMAIL><br>Support: <EMAIL></p>
            </div>

            <div class="contact-card glass-card animate-slide-up">
                <div class="contact-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <h3>Business Hours</h3>
                <p>Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 1:00 PM<br>Sunday: Closed</p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="contact-form-section section-spacing gradient-light-section">
    <div class="container">
        <div class="form-container glass-card-strong">
            <h2>Send Us a Message</h2>

            <?php if ($formSubmitted && $formSuccess): ?>
            <div class="form-success">
                <i class="fas fa-check-circle"></i>
                <p>Your message has been sent successfully. We will contact you soon!</p>
            </div>
            <?php else: ?>

            <?php if (!empty($formError)): ?>
            <div class="form-error">
                <i class="fas fa-exclamation-circle"></i>
                <p><?php echo $formError; ?></p>
            </div>
            <?php endif; ?>

            <form class="contact-form" method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                <div class="form-row">
                    <div class="form-group">
                        <label for="name">Your Name *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone">
                    </div>
                    <div class="form-group">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject">
                    </div>
                </div>

                <div class="form-group">
                    <label for="message">Your Message *</label>
                    <textarea id="message" name="message" rows="6" required></textarea>
                </div>

                <div class="form-group">
                    <button type="submit" name="contact_submit" class="btn primary-btn gradient-btn">Send Message</button>
                </div>

                <p class="form-note">* Required fields</p>
            </form>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Global Offices Section -->
<section class="global-offices textured">
    <div class="container">
        <h2 class="section-title">Our Global Offices</h2>
        <div class="offices-grid">
            <div class="office-card glass-card">
                <h3>North America</h3>
                <ul class="office-list">
                    <li>
                        <strong>New York (HQ)</strong><br>
                        123 Logistics Way, New York, NY 10001<br>
                        +****************
                    </li>
                    <li>
                        <strong>Chicago</strong><br>
                        456 Transport Ave, Chicago, IL 60601<br>
                        +1 (312) 555-4567
                    </li>
                    <li>
                        <strong>Los Angeles</strong><br>
                        789 Shipping Blvd, Los Angeles, CA 90001<br>
                        +1 (213) 555-7890
                    </li>
                </ul>
            </div>

            <div class="office-card glass-card">
                <h3>Europe</h3>
                <ul class="office-list">
                    <li>
                        <strong>London</strong><br>
                        10 Logistics Lane, London, E14 5AB<br>
                        +44 20 7946 0123
                    </li>
                    <li>
                        <strong>Frankfurt</strong><br>
                        Logistikstraße 20, 60549 Frankfurt<br>
                        +49 69 7124 0000
                    </li>
                    <li>
                        <strong>Rotterdam</strong><br>
                        Havenlaan 30, 3000 Rotterdam<br>
                        +31 10 414 5000
                    </li>
                </ul>
            </div>

            <div class="office-card glass-card">
                <h3>Asia Pacific</h3>
                <ul class="office-list">
                    <li>
                        <strong>Singapore</strong><br>
                        50 Shipping Road, Singapore 117607<br>
                        +65 6123 4567
                    </li>
                    <li>
                        <strong>Hong Kong</strong><br>
                        88 Logistics Centre, Kowloon, Hong Kong<br>
                        +852 3456 7890
                    </li>
                    <li>
                        <strong>Shanghai</strong><br>
                        200 Shipping Street, Shanghai, 200120<br>
                        +86 21 5123 4567
                    </li>
                </ul>
            </div>

            <div class="office-card glass-card">
                <h3>Other Regions</h3>
                <ul class="office-list">
                    <li>
                        <strong>Dubai</strong><br>
                        Logistics Tower, Dubai Logistics City<br>
                        +971 4 123 4567
                    </li>
                    <li>
                        <strong>São Paulo</strong><br>
                        Avenida Logística 500, São Paulo<br>
                        +55 11 3456 7890
                    </li>
                    <li>
                        <strong>Sydney</strong><br>
                        75 Shipping Street, Sydney NSW 2000<br>
                        +61 2 8765 4321
                    </li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section gradient-light-section">
    <div class="container">
        <h2 class="section-title">Find Us</h2>
        <div id="contact-map"></div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="cta-section gradient-primary-section textured">
    <div class="container">
        <div class="cta-content">
            <h2>Need Immediate Assistance?</h2>
            <p>Our customer service team is available to help you with any urgent inquiries.</p>
            <a href="tel:+12125557891" class="btn primary-btn glass-btn"><i class="fas fa-phone-alt"></i> Call Us Now</a>
        </div>
    </div>
</section>

<!-- Map Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the map
    const map = L.map('contact-map').setView([40.7128, -74.0060], 13);

    // Add the OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
    }).addTo(map);

    // Add marker for headquarters
    const marker = L.marker([40.7128, -74.0060]).addTo(map);

    // Add popup to marker
    marker.bindPopup('<strong>Global TransLogix Headquarters</strong><br>123 Logistics Way<br>New York, NY 10001').openPopup();
});
</script>

<?php
// Include footer
include_once 'includes/footer.php';
?>
