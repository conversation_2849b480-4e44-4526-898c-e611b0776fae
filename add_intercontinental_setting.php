<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Set page title
$pageTitle = 'Add Intercontinental Settings';

// Include header
include_once 'includes/header.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

echo "<div class='container' style='padding: 40px 0;'>";
echo "<div class='card' style='padding: 20px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);'>";
echo "<h1>Add Intercontinental Settings</h1>";
echo "<p>This page will add the necessary settings for intercontinental routing.</p>";


try {
    global $conn;

    // Check if setting already exists
    $stmt = $conn->query("SELECT COUNT(*) as count FROM system_settings WHERE setting_key = 'enable_intercontinental_routing'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result['count'] == 0) {
        // Add new setting
        $stmt = $conn->prepare("INSERT INTO system_settings
            (setting_key, setting_value, setting_group, setting_type, setting_label, setting_description, is_public)
            VALUES (?, ?, ?, ?, ?, ?, ?)");

        $stmt->execute([
            'enable_intercontinental_routing',
            '0', // Disabled by default
            'tracking',
            'boolean',
            'Enable Intercontinental Routing',
            'Enable smart routing through airports and seaports for intercontinental shipments',
            0 // Not public
        ]);

        echo "<p>Successfully added intercontinental routing setting.</p>";
    } else {
        echo "<p>Intercontinental routing setting already exists.</p>";
    }

    // Add airport/seaport preference setting
    $stmt = $conn->query("SELECT COUNT(*) as count FROM system_settings WHERE setting_key = 'intercontinental_routing_preference'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($result['count'] == 0) {
        // Add new setting
        $stmt = $conn->prepare("INSERT INTO system_settings
            (setting_key, setting_value, setting_group, setting_type, setting_label, setting_description, is_public)
            VALUES (?, ?, ?, ?, ?, ?, ?)");

        $stmt->execute([
            'intercontinental_routing_preference',
            'auto', // Auto-detect by default
            'tracking',
            'select',
            'Intercontinental Routing Preference',
            'Preferred transportation method for intercontinental shipments',
            0 // Not public
        ]);

        echo "<p>Successfully added intercontinental routing preference setting.</p>";
    } else {
        echo "<p>Intercontinental routing preference setting already exists.</p>";
    }

    echo "<p>All settings have been added. <a href='admin/tracking-settings.php' class='btn primary-btn'>Go to Tracking Settings</a></p>";

} catch (PDOException $e) {
    echo "<p class='error'>Error: " . $e->getMessage() . "</p>";
}

echo "</div>";
echo "</div>";

// Include footer
include_once 'includes/footer.php';
?>
