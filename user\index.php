<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    header('Location: ../login.php');
    exit;
}

// Get user information
$userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
$userInfo = getUserById($userId);
$userRole = isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'customer';

// Set page title based on role
$pageTitle = (isStaff()) ? 'Staff Dashboard' : 'Customer Dashboard';

// Include header
include_once 'includes/header.php';

// Get shipments based on user role
$shipments = [];
try {
    if (hasPermission('view_all_shipments')) {
        // Staff and admin can see all shipments
        $stmt = $conn->prepare("SELECT * FROM shipments ORDER BY created_at DESC LIMIT 5");
        $stmt->execute();
    } else {
        // Customers can only see their own shipments
        $stmt = $conn->prepare("SELECT * FROM shipments WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
        $stmt->execute([$userId]);
    }
    $shipments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error silently
    error_log("Error fetching shipments: " . $e->getMessage());
}
?>

<div class="container">
    <div class="dashboard-header">
        <h1>Welcome, <?php echo htmlspecialchars($userInfo['first_name'] ?? $userInfo['username']); ?>!</h1>
        <?php if (isStaff()): ?>
        <p>Manage shipments, track packages, and update delivery information</p>
        <?php else: ?>
        <p>Track your shipments and manage your account information</p>
        <?php endif; ?>
    </div>

    <div class="row">
        <div class="col-md-4">
            <div class="profile-card">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3><?php echo htmlspecialchars(($userInfo['first_name'] ?? $userInfo['username']) . ' ' . ($userInfo['last_name'] ?? '')); ?></h3>
                    <p><?php echo htmlspecialchars($userInfo['email']); ?></p>
                </div>
                <div class="profile-actions">
                    <a href="profile.php" class="btn btn-sm primary-btn">Edit Profile</a>
                    <a href="change-password.php" class="btn btn-sm outline-btn">Change Password</a>
                </div>
            </div>

            <div class="quick-links">
                <h3>Quick Links</h3>
                <ul>
                    <li>
                        <a href="shipments.php">
                            <i class="fas fa-box"></i>
                            <?php if (hasPermission('view_all_shipments')): ?>
                            <span>All Shipments</span>
                            <?php else: ?>
                            <span>My Shipments</span>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php if (canCreateShipment()): ?>
                    <li>
                        <a href="create-shipment.php">
                            <i class="fas fa-plus-circle"></i>
                            <span>Create New Shipment</span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <?php if (canAddTrackingUpdate()): ?>
                    <li>
                        <a href="add-tracking-update.php">
                            <i class="fas fa-truck"></i>
                            <span>Add Tracking Update</span>
                        </a>
                    </li>
                    <?php endif; ?>
                    <li>
                        <a href="../tracking/index.php">
                            <i class="fas fa-search"></i>
                            <span>Track Shipment</span>
                        </a>
                    </li>
                    <li>
                        <a href="support.php">
                            <i class="fas fa-headset"></i>
                            <span>Support</span>
                        </a>
                    </li>
                    <li>
                        <a href="../logout.php">
                            <i class="fas fa-sign-out-alt"></i>
                            <span>Logout</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="col-md-8">
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Recent Shipments</h2>
                    <div class="header-actions">
                        <?php if (canCreateShipment()): ?>
                        <a href="create-shipment.php" class="btn btn-sm primary-btn">Create New</a>
                        <?php endif; ?>
                        <a href="shipments.php" class="btn btn-sm outline-btn">View All</a>
                    </div>
                </div>

                <?php if (count($shipments) > 0): ?>
                    <div class="shipments-list">
                        <?php foreach ($shipments as $shipment): ?>
                            <div class="shipment-card">
                                <div class="shipment-info">
                                    <h3>
                                        <a href="../tracking/index.php?tracking_number=<?php echo htmlspecialchars($shipment['tracking_number']); ?>">
                                            <?php echo htmlspecialchars($shipment['tracking_number']); ?>
                                        </a>
                                    </h3>
                                    <p class="shipment-date">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?php echo date('M d, Y', strtotime($shipment['created_at'])); ?>
                                    </p>
                                    <p class="shipment-route">
                                        <i class="fas fa-map-marker-alt"></i>
                                        <?php echo htmlspecialchars($shipment['origin']); ?> to <?php echo htmlspecialchars($shipment['destination']); ?>
                                    </p>
                                    <p class="shipment-status">
                                        <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $shipment['status'])); ?>">
                                            <?php echo htmlspecialchars($shipment['status']); ?>
                                        </span>
                                    </p>
                                </div>
                                <div class="shipment-actions">
                                    <a href="../tracking/index.php?tracking_number=<?php echo htmlspecialchars($shipment['tracking_number']); ?>" class="btn-icon" title="Track">
                                        <i class="fas fa-search"></i>
                                    </a>
                                    <button class="btn-icon share-shipment" data-tracking="<?php echo htmlspecialchars($shipment['tracking_number']); ?>" title="Share">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                    <?php if (canUpdateShipment()): ?>
                                    <a href="edit-shipment.php?id=<?php echo htmlspecialchars($shipment['id']); ?>" class="btn-icon" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php endif; ?>
                                    <?php if (canAddTrackingUpdate()): ?>
                                    <a href="add-tracking-update.php?shipment_id=<?php echo htmlspecialchars($shipment['id']); ?>" class="btn-icon" title="Add Update">
                                        <i class="fas fa-plus"></i>
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-box-open"></i>
                        </div>
                        <h3>No Shipments Yet</h3>
                        <?php if (canCreateShipment()): ?>
                        <p>You don't have any shipments yet. Create your first shipment to get started.</p>
                        <a href="create-shipment.php" class="btn primary-btn">Create Shipment</a>
                        <?php else: ?>
                        <p>You don't have any shipments yet. Your shipments will appear here once they are created.</p>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Share Modal -->
<div id="shareModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Share Tracking Link</h3>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <p>Share this tracking link with others to let them track this shipment:</p>

            <div class="share-link-container">
                <input type="text" id="shareLink" readonly>
                <button class="btn primary-btn" id="copyLink">Copy</button>
            </div>

            <p>Or share via:</p>
            <div class="share-options">
                <div class="share-option" id="share-email">
                    <i class="fas fa-envelope"></i>
                    <span>Email</span>
                </div>
                <div class="share-option" id="share-whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    <span>WhatsApp</span>
                </div>
                <div class="share-option" id="share-facebook">
                    <i class="fab fa-facebook"></i>
                    <span>Facebook</span>
                </div>
                <div class="share-option" id="share-twitter">
                    <i class="fab fa-twitter"></i>
                    <span>Twitter</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Share Functionality Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Share modal elements
        const modal = document.getElementById('shareModal');
        const closeBtn = document.querySelector('.close-modal');
        const shareButtons = document.querySelectorAll('.share-shipment');
        const shareLinkInput = document.getElementById('shareLink');
        const copyLinkBtn = document.getElementById('copyLink');

        // Share options
        const shareEmail = document.getElementById('share-email');
        const shareWhatsApp = document.getElementById('share-whatsapp');
        const shareFacebook = document.getElementById('share-facebook');
        const shareTwitter = document.getElementById('share-twitter');

        // Current tracking number being shared
        let currentTracking = '';

        // Open modal when share button is clicked
        shareButtons.forEach(button => {
            button.addEventListener('click', function() {
                currentTracking = this.getAttribute('data-tracking');
                const trackingUrl = getBasePath() + '/tracking/index.php?tracking_number=' + currentTracking;
                shareLinkInput.value = trackingUrl;
                modal.style.display = 'block';
            });
        });

        // Close modal when X is clicked
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Copy link to clipboard
        copyLinkBtn.addEventListener('click', function() {
            shareLinkInput.select();
            document.execCommand('copy');
            showNotification('Link copied to clipboard!', 'success');
        });

        // Share via email
        shareEmail.addEventListener('click', function() {
            const subject = 'Track your shipment';
            const body = 'You can track your shipment using this link: ' + shareLinkInput.value;
            window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        });

        // Share via WhatsApp
        shareWhatsApp.addEventListener('click', function() {
            const text = 'Track your shipment: ' + shareLinkInput.value;
            window.open(`https://wa.me/?text=${encodeURIComponent(text)}`, '_blank');
        });

        // Share via Facebook
        shareFacebook.addEventListener('click', function() {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareLinkInput.value)}`, '_blank');
        });

        // Share via Twitter
        shareTwitter.addEventListener('click', function() {
            const text = 'Track your shipment with Global TransLogix: ';
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(shareLinkInput.value)}`, '_blank');
        });

        // Helper function to get base path
        function getBasePath() {
            // Try to extract from meta tag
            const basePathMeta = document.querySelector('meta[name="base-path"]');
            if (basePathMeta && basePathMeta.content) {
                return basePathMeta.content.replace(/\/$/, ''); // Remove trailing slash if present
            }

            // Try to extract from script tags (looking for assets/js paths)
            const scriptTags = document.querySelectorAll('script[src*="assets/js"]');
            if (scriptTags.length > 0) {
                const scriptSrc = scriptTags[0].getAttribute('src');
                const match = scriptSrc.match(/^(.*?)\/assets\/js/);
                if (match && match[1]) {
                    return match[1];
                }
            }

            // Try to extract from the current path
            const pathParts = window.location.pathname.split('/');
            if (pathParts.includes('user')) {
                // Find the index of 'user' and get everything before it
                const userIndex = pathParts.indexOf('user');
                if (userIndex > 0) {
                    return pathParts.slice(0, userIndex).join('/');
                }
            }

            // Default to empty string (root of the domain)
            return '';
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            if (typeof window.showNotification === 'function') {
                window.showNotification(message, type);
            } else {
                alert(message);
            }
        }
    });
</script>

<!-- Add CSS for user dashboard -->
<style>
    /* User Dashboard Styles */
    .profile-card {
        background-color: var(--glass-bg);
        border-radius: 16px;
        padding: 25px;
        margin-bottom: 30px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
        text-align: center;
    }

    .profile-header {
        margin-bottom: 20px;
    }

    .profile-avatar {
        width: 80px;
        height: 80px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 2rem;
    }

    .profile-header h3 {
        margin-bottom: 5px;
        color: var(--primary-color);
    }

    .profile-header p {
        color: var(--text-secondary);
        font-size: 0.9rem;
    }

    .profile-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .btn-sm {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .outline-btn {
        background-color: transparent;
        border: 1px solid var(--primary-color);
        color: var(--primary-color);
    }

    .outline-btn:hover {
        background-color: var(--primary-color);
        color: white;
    }

    .quick-links {
        background-color: var(--glass-bg);
        border-radius: 16px;
        padding: 25px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
        margin-bottom: 15px;
    }

    .quick-links h3 {
        margin-bottom: 15px;
        color: var(--primary-color);
    }

    .quick-links ul {
        list-style: none;
        padding: 0;
    }

    .quick-links li {
        margin-bottom: 12px;
    }

    .quick-links a {
        display: flex;
        align-items: center;
        gap: 10px;
        color: var(--text-primary);
        text-decoration: none;
        padding: 8px 0;
        transition: all 0.3s ease;
    }

    .quick-links a:hover {
        color: var(--primary-color);
        transform: translateX(5px);
    }

    .quick-links i {
        color: var(--primary-color);
        width: 20px;
        text-align: center;
    }

    .empty-state {
        text-align: center;
        padding: 40px 20px;
        background-color: var(--glass-bg);
        border-radius: 16px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
    }

    .empty-icon {
        font-size: 3rem;
        color: var(--text-secondary);
        margin-bottom: 15px;
    }

    .empty-state h3 {
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .empty-state p {
        color: var(--text-secondary);
    }

    .btn-icon {
        background-color: transparent;
        border: none;
        color: var(--primary-color);
        font-size: 1rem;
        cursor: pointer;
        padding: 5px 10px;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .btn-icon:hover {
        background-color: rgba(92, 43, 226, 0.1);
        color: var(--primary-color);
    }

    .header-actions {
        display: flex;
        gap: 10px;
    }

    .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    /* Share Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background-color: var(--bg-color);
        margin: 10% auto;
        padding: 0;
        width: 90%;
        max-width: 500px;
        border-radius: 16px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        animation: modalFadeIn 0.3s;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .modal-header h3 {
        margin: 0;
        color: var(--primary-color);
    }

    .close-modal {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
    }

    .modal-body {
        padding: 25px;
    }

    .share-link-container {
        display: flex;
        margin: 20px 0;
    }

    .share-link-container input {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px 0 0 8px;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .share-link-container button {
        border-radius: 0 8px 8px 0;
    }

    .share-options {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        margin-top: 20px;
    }

    .share-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 15px;
        border-radius: 8px;
        background-color: var(--glass-bg);
        border: var(--glass-border);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .share-option:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .share-option i {
        font-size: 1.5rem;
    }

    #share-email i {
        color: #d44638;
    }

    #share-whatsapp i {
        color: #25d366;
    }

    #share-facebook i {
        color: #1877f2;
    }

    #share-twitter i {
        color: #1da1f2;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }

        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Dark theme adjustments */
    .dark-theme .modal-content {
        background-color: var(--dark-bg);
    }

    .dark-theme .share-link-container input {
        background-color: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .dark-theme .share-option {
        background-color: rgba(255, 255, 255, 0.05);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .share-options {
            grid-template-columns: repeat(2, 1fr);
        }

        .modal-content {
            margin: 20% auto;
            width: 95%;
        }
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
