/* Tracking Page Styles */

/* Tracking Form Section */
.tracking-form-section {
    padding: 40px 0;
    background-color: var(--bg-secondary);
}

.tracking-box {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.tracking-form {
    display: flex;
    gap: 15px;
}

.tracking-form .form-group {
    flex: 1;
}

/* Tracking Results Section */
.tracking-results {
    padding: 40px 0;
    background-color: var(--bg-color);
}

.shipment-details {
    margin-bottom: 40px;
}

.shipment-details h2 {
    margin-bottom: 20px;
    color: var(--text-color);
    font-size: 1.8rem;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.detail-card {
    background-color: var(--glass-bg);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.detail-card h3 {
    margin-bottom: 10px;
    color: var(--text-secondary);
    font-size: 1rem;
    font-weight: 500;
}

.detail-card p {
    color: var(--text-color);
    font-size: 1.1rem;
    font-weight: 600;
}

/* Status Badge Styles */
.status-badge {
    display: inline-block;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
}

.status-pending {
    background-color: rgba(108, 117, 125, 0.2);
    color: #6c757d;
}

.status-transit {
    background-color: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.status-delivered {
    background-color: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.status-delayed {
    background-color: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.status-cancelled {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
}

/* Dark theme overrides for status badges */
.dark-theme .status-pending {
    background-color: rgba(108, 117, 125, 0.3);
    color: #adb5bd;
}

.dark-theme .status-transit {
    background-color: rgba(255, 193, 7, 0.3);
    color: #ffe066;
}

.dark-theme .status-delivered {
    background-color: rgba(40, 167, 69, 0.3);
    color: #8fd19e;
}

.dark-theme .status-delayed {
    background-color: rgba(255, 152, 0, 0.3);
    color: #ffcc80;
}

.dark-theme .status-cancelled {
    background-color: rgba(220, 53, 69, 0.3);
    color: #f5c2c7;
}

/* Tracking Map Styles */
.tracking-map-container {
    margin-bottom: 40px;
    position: relative;
}

.tracking-map-container h2 {
    margin-bottom: 20px;
    color: var(--text-color);
    font-size: 1.8rem;
}

.map-section-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    position: relative;
}

#tracking-map {
    height: 500px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    position: relative;
    z-index: 5;
}

.no-map-data {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    font-style: italic;
    padding: 30px;
    text-align: center;
    border-radius: 12px;
}

.no-map-data i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    opacity: 0.5;
}

/* Current Location Info */
.current-location-info {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
}

.location-card {
    background-color: var(--glass-bg);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.location-card h3 {
    margin-bottom: 15px;
    color: var(--text-color);
    font-size: 1.2rem;
}

.location-card .status-badge {
    margin-bottom: 20px;
    align-self: flex-start;
}

.location-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
}

.location-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.location-item i {
    font-size: 1.2rem;
    color: var(--primary-color);
    margin-top: 3px;
}

.location-item h4 {
    margin-bottom: 5px;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.location-item p {
    color: var(--text-color);
    font-weight: 600;
}

.no-location-data {
    background-color: rgba(92, 43, 226, 0.05);
    padding: 15px;
    border-radius: 8px;
    margin-top: 15px;
    text-align: center;
    color: var(--text-secondary);
}

.no-location-data i {
    margin-right: 5px;
    color: var(--primary-color);
}

/* Tracking Examples */
.tracking-examples {
    margin-top: 15px;
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.tracking-examples a {
    color: var(--primary-color);
    text-decoration: underline;
    transition: color 0.3s ease;
}

.tracking-examples a:hover {
    color: var(--secondary-color);
}

/* Map provider control */
.map-provider-control {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.4);
    padding: 5px;
}

.map-provider-control select {
    border: none;
    background-color: transparent;
    font-size: 14px;
    padding: 5px;
    cursor: pointer;
}

/* Dark theme map */
.dark-theme-map {
    filter: invert(100%) hue-rotate(180deg) brightness(95%) contrast(90%);
}

.dark-theme-map .leaflet-tile {
    filter: brightness(0.6) contrast(1.4);
}

/* Map popup styles */
.map-info-window {
    padding: 10px;
    max-width: 300px;
    font-family: var(--body-font);
}

.map-info-window h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.map-info-window p {
    margin: 8px 0;
    font-size: 14px;
    line-height: 1.4;
}

.map-info-window i {
    margin-right: 5px;
    color: var(--primary-color);
    width: 16px;
    text-align: center;
}

/* Enhanced popup styling */
.enhanced-popup .leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 3px 14px rgba(0,0,0,0.2);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
}

.dark-theme .enhanced-popup .leaflet-popup-content-wrapper {
    background: rgba(40, 44, 52, 0.95);
    color: #fff;
}

.enhanced-popup .leaflet-popup-content {
    margin: 0;
    width: auto !important;
}

.enhanced-popup .leaflet-popup-tip {
    background: var(--primary-color);
}

/* Popup header */
.popup-header {
    border-bottom: 1px solid rgba(0,0,0,0.1);
    padding-bottom: 8px;
    margin-bottom: 8px;
}

.dark-theme .popup-header {
    border-bottom-color: rgba(255,255,255,0.1);
}

/* Popup badges */
.popup-badges {
    display: flex;
    gap: 8px;
    margin-top: 5px;
    flex-wrap: wrap;
}

.marker-type, .transport-mode {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    background: rgba(0,0,0,0.05);
}

.dark-theme .marker-type,
.dark-theme .transport-mode {
    background: rgba(255,255,255,0.1);
}

.current-type {
    background: rgba(0, 128, 255, 0.1);
    color: #0080ff;
}

.origin-type {
    background: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.destination-type {
    background: rgba(156, 39, 176, 0.1);
    color: #9C27B0;
}

.transit-type {
    background: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

/* Transport mode badges */
.transport-mode i {
    margin-right: 4px;
}

.road-mode {
    background: rgba(92, 43, 226, 0.1);
    color: #5c2be2;
}

.air-mode {
    background: rgba(0, 168, 255, 0.1);
    color: #00a8ff;
}

.sea-mode {
    background: rgba(0, 151, 230, 0.1);
    color: #0097e6;
}

.rail-mode {
    background: rgba(232, 65, 24, 0.1);
    color: #e84118;
}

/* Current badge */
.current-badge {
    background: #0080ff;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    margin-left: 5px;
}

/* Popup content */
.popup-content {
    padding: 5px 0;
}

/* Coordinates link */
.coordinate-link {
    cursor: pointer;
    color: var(--primary-color);
    text-decoration: underline;
    text-decoration-style: dotted;
}

.map-link {
    margin-left: 5px;
    color: var(--primary-color);
}

/* Popup footer */
.popup-footer {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(0,0,0,0.1);
    font-size: 12px;
    color: #666;
}

.dark-theme .popup-footer {
    border-top-color: rgba(255,255,255,0.1);
    color: #aaa;
}

.update-info {
    display: flex;
    align-items: center;
    justify-content: center;
    font-style: italic;
}

/* Custom marker styles */
.custom-div-icon {
    background: none;
    border: none;
}

.marker-pin {
    width: 30px;
    height: 30px;
    border-radius: 50% 50% 50% 0;
    background: #c30b82;
    position: absolute;
    transform: rotate(-45deg);
    left: 50%;
    top: 50%;
    margin: -15px 0 0 -15px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.4);
}

.marker-pin::after {
    content: '';
    width: 24px;
    height: 24px;
    margin: 3px 0 0 3px;
    background: #fff;
    position: absolute;
    border-radius: 50%;
}

.marker-pin i {
    transform: rotate(45deg);
    color: #333;
    position: relative;
    z-index: 1;
    font-size: 14px;
}

/* Pin colors for different types */
.origin-pin {
    background: #ffc107; /* Gold for origin */
}

.destination-pin {
    background: #28a745; /* Green for destination */
}

.current-pin {
    background: #007bff; /* Blue for current location */
}

.transit-pin {
    background: #5c2be2; /* Purple for transit points */
}

.delayed-pin {
    background: #ff9800; /* Orange for delayed points */
}

/* Map labels */
.map-label div {
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    font-weight: 600;
    color: #333;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    white-space: nowrap;
    text-align: center;
}

.dark-theme .map-label div {
    background-color: rgba(50, 50, 50, 0.9);
    color: #fff;
}

/* Distance info */
.distance-info {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    box-shadow: 0 1px 5px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 8px;
}

.distance-info i {
    color: var(--primary-color);
}

.dark-theme .distance-info {
    background-color: rgba(50, 50, 50, 0.9);
    color: #fff;
}

/* Route loading indicator */
.route-loading-indicator {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(255, 255, 255, 0.8);
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    box-shadow: 0 1px 5px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    gap: 8px;
}

.route-loading-indicator i {
    color: var(--primary-color);
}

.dark-theme .route-loading-indicator {
    background: rgba(30, 30, 30, 0.8);
    color: #fff;
}

/* Tracking Timeline Styles */
.tracking-timeline {
    margin-bottom: 40px;
}

.tracking-timeline h2 {
    margin-bottom: 20px;
    color: var(--text-color);
    font-size: 1.8rem;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    left: 6px;
    height: 100%;
    width: 2px;
    background-color: var(--primary-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    top: 0;
    left: -30px;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 2px solid var(--bg-color);
}

.timeline-content {
    background-color: var(--glass-bg);
    border-radius: 12px;
    padding: 20px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.timeline-content h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.timeline-location {
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-time {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.timeline-notes {
    background-color: rgba(92, 43, 226, 0.05);
    padding: 10px;
    border-radius: 8px;
    font-style: italic;
}

.no-updates {
    text-align: center;
    padding: 30px;
    background-color: var(--glass-bg);
    border-radius: 12px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-secondary);
    font-style: italic;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .map-section-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    #tracking-map {
        height: 400px;
    }

    .location-card {
        margin-bottom: 20px;
    }

    .current-location-info {
        margin-top: 20px;
    }
}

@media (max-width: 768px) {
    .tracking-form {
        flex-direction: column;
    }

    .tracking-form .btn {
        width: 100%;
    }

    /* Fix for label overlapping with input */
    .tracking-form .form-group {
        position: relative;
        margin-bottom: 30px;
        padding-top: 20px;
    }

    .tracking-form label {
        position: absolute;
        top: 0;
        left: 0;
        margin-bottom: 0;
        font-size: 0.85rem;
        font-weight: bold;
        z-index: 10;
    }

    .tracking-form input {
        padding-top: 20px;
    }

    #tracking-map {
        height: 350px;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline-marker {
        left: -20px;
    }

    .details-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 480px) {
    .details-grid {
        grid-template-columns: 1fr;
    }

    .detail-card {
        padding: 15px;
    }

    .timeline-content {
        padding: 15px;
    }

    .location-item {
        flex-direction: column;
        gap: 5px;
    }

    .location-item i {
        margin-bottom: 5px;
    }
}
