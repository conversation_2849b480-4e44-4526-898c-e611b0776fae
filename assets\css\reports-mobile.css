/* Reports Mobile Responsive Styles */

/* Base Mobile Styles for Reports */
@media (max-width: 1200px) {
    /* Report Layout */
    .report-container {
        flex-direction: column;
    }
    
    .report-sidebar {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .report-content {
        width: 100%;
    }
    
    /* Chart containers */
    .chart-wrapper {
        height: 300px;
    }
}

@media (max-width: 992px) {
    /* Filter Form */
    .filter-form {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .filter-form .form-group {
        width: 100%;
    }
    
    /* Summary Grid */
    .summary-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    /* Data Table */
    .data-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .data-table {
        min-width: 600px;
    }
}

@media (max-width: 768px) {
    /* Report Header */
    .report-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .report-header h1 {
        font-size: 1.5rem;
    }
    
    /* Summary Grid */
    .summary-grid {
        grid-template-columns: 1fr;
    }
    
    /* Summary Cards */
    .summary-card {
        flex-direction: row;
        align-items: center;
        padding: 15px;
    }
    
    .summary-icon {
        margin-right: 15px;
        margin-bottom: 0;
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }
    
    .summary-info {
        text-align: left;
    }
    
    /* Chart Containers */
    .chart-wrapper {
        height: 250px;
    }
    
    /* Date Range Picker */
    .date-range-picker {
        flex-direction: column;
        gap: 10px;
    }
    
    .date-range-picker input {
        width: 100%;
    }
}

@media (max-width: 576px) {
    /* Container Padding */
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    /* Report Types */
    .report-types {
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .report-type-btn {
        flex: 1 0 calc(50% - 10px);
        font-size: 0.9rem;
        padding: 8px;
    }
    
    /* Chart Containers */
    .chart-wrapper {
        height: 200px;
    }
    
    /* Data Table */
    .data-table th,
    .data-table td {
        padding: 8px;
        font-size: 0.9rem;
    }
}

/* Responsive Chart Handling */
@media (max-width: 768px) {
    /* Adjust chart legend position for small screens */
    .chart-container canvas {
        max-height: 250px;
    }
}

/* Print Styles */
@media print {
    .admin-hero,
    .filter-form,
    .admin-actions,
    .report-types,
    .sidebar-toggle,
    footer {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .chart-wrapper {
        height: 300px;
        page-break-inside: avoid;
    }
    
    .data-summary {
        page-break-inside: avoid;
    }
    
    .data-table-container {
        overflow-x: visible;
    }
    
    .data-table {
        width: 100%;
        min-width: auto;
    }
    
    body {
        background-color: white;
        color: black;
    }
    
    .report-content {
        width: 100%;
    }
}
