// Intercontinental Tracking Map Script
// This script handles both intercontinental and non-intercontinental routing

// Add CSS to hide Leaflet Routing Machine paths and enhance our custom paths
const style = document.createElement('style');
style.textContent = `
    /* Hide Leaflet Routing Machine paths */
    .leaflet-routing-line { display: none !important; }

    /* Ensure our custom paths are visible with enhanced styling */
    .custom-ant-path, .custom-ant-path-pulse, .route-path, .air-path, .sea-path, .rail-path, .road-path {
        display: block !important;
        stroke-linecap: round !important;
        stroke-linejoin: round !important;
        filter: drop-shadow(0px 0px 3px rgba(255, 255, 255, 0.5)) !important;
    }

    /* Transport mode specific styling */
    .air-path {
        filter: drop-shadow(0px 0px 4px rgba(0, 229, 255, 0.7)) !important;
    }

    .sea-path {
        filter: drop-shadow(0px 0px 4px rgba(83, 109, 254, 0.7)) !important;
    }

    .rail-path {
        filter: drop-shadow(0px 0px 4px rgba(213, 0, 249, 0.7)) !important;
    }

    .road-path {
        filter: drop-shadow(0px 0px 4px rgba(101, 31, 255, 0.7)) !important;
    }

    /* Hide Leaflet Routing Machine container */
    .leaflet-routing-container {
        display: none !important;
    }
`;
document.head.appendChild(style);

// Use a self-executing function to avoid global namespace pollution
(function() {
    console.log('Intercontinental tracking map script loaded');

    // Check if tracking map element exists
    const mapElement = document.getElementById('tracking-map');
    if (!mapElement) {
        console.log('Map element not found');
        return;
    }

    // Check if tracking updates exist
    if (typeof trackingUpdates === 'undefined') {
        console.log('No tracking updates defined');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    console.log('Tracking updates:', trackingUpdates);

    // Function to check if any tracking update has coordinates
    function hasCoordinates(updates) {
        if (!updates || updates.length === 0) return false;

        for (let i = 0; i < updates.length; i++) {
            if (updates[i].latitude && updates[i].longitude) {
                return true;
            }
        }
        return false;
    }

    // Check if tracking updates have coordinates
    if (!hasCoordinates(trackingUpdates)) {
        console.log('No coordinates found in tracking updates');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available for this shipment.</p></div>';
        return;
    }

    // Initialize the map
    const map = L.map('tracking-map', {
        zoomControl: false, // We'll add it in a different position
        minZoom: 2,
        maxZoom: 18
    }).setView([39.8283, -98.5795], 3);

    // Add zoom control to the top-right corner
    L.control.zoom({
        position: 'topright'
    }).addTo(map);

    // Define available map types
    const mapTypes = {
        'streets': {
            name: 'Streets',
            url: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19
        },
        'satellite': {
            name: 'Satellite',
            url: 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',
            attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
            maxZoom: 19
        },
        'terrain': {
            name: 'Terrain',
            url: 'https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png',
            attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 18
        },
        'dark': {
            name: 'Dark',
            url: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}{r}.png',
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> &copy; <a href="http://cartodb.com/attributions">CartoDB</a>',
            maxZoom: 19
        },
        'light': {
            name: 'Light',
            url: 'https://cartodb-basemaps-{s}.global.ssl.fastly.net/light_all/{z}/{x}/{y}{r}.png',
            attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> &copy; <a href="http://cartodb.com/attributions">CartoDB</a>',
            maxZoom: 19
        }
    };

    // Create a layer for the current map type
    let currentLayer = null;

    // Function to change map type
    function changeMapType(type) {
        // Remove current layer if it exists
        if (currentLayer) {
            map.removeLayer(currentLayer);
        }

        // Add the new layer
        const mapType = mapTypes[type] || mapTypes['streets'];
        currentLayer = L.tileLayer(mapType.url, {
            attribution: mapType.attribution,
            maxZoom: mapType.maxZoom,
            language: 'en' // Ensure map labels are in English
        }).addTo(map);
    }

    // Add map legend with transport hubs
    const legend = L.control({position: 'bottomright'});
    legend.onAdd = function() {
        const div = L.DomUtil.create('div', 'map-legend');
        div.innerHTML = `
            <div class="legend-item">
                <div class="legend-color origin"></div>
                <span>Origin</span>
            </div>
            <div class="legend-item">
                <div class="legend-color destination"></div>
                <span>Destination</span>
            </div>
            <div class="legend-item">
                <div class="legend-color current"></div>
                <span>Current Location</span>
            </div>
            <div class="legend-item">
                <div class="legend-color transit"></div>
                <span>Transit Point</span>
            </div>
            <div class="legend-item">
                <div class="legend-color airport"></div>
                <span>Airport</span>
            </div>
            <div class="legend-item">
                <div class="legend-color seaport"></div>
                <span>Seaport</span>
            </div>
        `;
        return div;
    };
    legend.addTo(map);

    // Create map type control
    const mapTypeControl = L.control({position: 'topleft'});
    mapTypeControl.onAdd = function() {
        const div = L.DomUtil.create('div', 'map-type-control');
        div.innerHTML = `
            <select id="map-type-select">
                ${Object.keys(mapTypes).map(key => `<option value="${key}">${mapTypes[key].name}</option>`).join('')}
            </select>
        `;
        return div;
    };
    mapTypeControl.addTo(map);

    // Add event listener to the map type selector
    setTimeout(() => {
        const mapTypeSelect = document.getElementById('map-type-select');
        if (mapTypeSelect) {
            mapTypeSelect.addEventListener('change', function() {
                changeMapType(this.value);
            });
        }
    }, 100);

    // Initialize with Streets map
    changeMapType('streets');

    // Custom marker icons - Simple dot style
    const markerIcons = {
        origin: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot origin-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        }),
        destination: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot destination-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        }),
        current: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot current-dot'></div>`,
            iconSize: [20, 20],
            iconAnchor: [10, 10],
            popupAnchor: [0, -12]
        }),
        transit: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot transit-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        }),
        airport: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot airport-dot'><i class="fas fa-plane"></i></div>`,
            iconSize: [24, 24],
            iconAnchor: [12, 12],
            popupAnchor: [0, -12]
        }),
        seaport: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot seaport-dot'><i class="fas fa-ship"></i></div>`,
            iconSize: [24, 24],
            iconAnchor: [12, 12],
            popupAnchor: [0, -12]
        }),
        delayed: L.divIcon({
            className: 'custom-div-icon',
            html: `<div class='marker-dot delayed-dot'></div>`,
            iconSize: [16, 16],
            iconAnchor: [8, 8],
            popupAnchor: [0, -10]
        })
    };

    // Process tracking updates
    const markers = [];

    // Sort tracking updates by timestamp (oldest first for proper routing)
    trackingUpdates.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    // Extract origin and destination coordinates
    const originUpdate = trackingUpdates[0];
    const destinationUpdate = trackingUpdates[trackingUpdates.length - 1];

    const originCoord = [parseFloat(originUpdate.latitude), parseFloat(originUpdate.longitude)];
    const destinationCoord = [parseFloat(destinationUpdate.latitude), parseFloat(destinationUpdate.longitude)];

    // Check if this is an intercontinental route
    const isIntercontinental = window.isIntercontinentalRoute && window.isIntercontinentalRoute(originCoord, destinationCoord);
    console.log('Is intercontinental route:', isIntercontinental);
    console.log('Origin coordinates:', originCoord);
    console.log('Destination coordinates:', destinationCoord);

    // Check if the intercontinental routing function exists
    console.log('isIntercontinentalRoute function exists:', typeof window.isIntercontinentalRoute === 'function');
    console.log('generateIntercontinentalRoute function exists:', typeof window.generateIntercontinentalRoute === 'function');

    // Get routing preference from data attribute or default to 'auto'
    const routingPreference = mapElement.getAttribute('data-routing-preference') || 'auto';

    // Create waypoints based on whether it's intercontinental or not
    let routeWaypoints = [];
    let useLeafletRouting = false; // Flag to determine if we should use Leaflet Routing Machine

    if (isIntercontinental && window.generateIntercontinentalRoute) {
        console.log('Generating intercontinental route with preference:', routingPreference);
        // Generate intercontinental route through airports/seaports
        try {
            routeWaypoints = window.generateIntercontinentalRoute(originCoord, destinationCoord, routingPreference);
            console.log('Generated intercontinental route waypoints:', routeWaypoints);

            // Log details about each waypoint
            routeWaypoints.forEach((waypoint, index) => {
                console.log(`Waypoint ${index}:`, {
                    latLng: waypoint.latLng,
                    transportMode: waypoint.transportMode,
                    isHub: waypoint.isHub,
                    hubType: waypoint.hubType,
                    name: waypoint.name
                });
            });
        } catch (error) {
            console.error('Error generating intercontinental route:', error);
            // Fall back to empty waypoints which will trigger the original method
            routeWaypoints = [];
        }

        // If the route is not actually intercontinental (determined by the generateIntercontinentalRoute function),
        // it will return just origin and destination points, and we'll use the original method below
        if (routeWaypoints.length > 2) {
            console.log('Using intercontinental routing with transport hubs');
            useLeafletRouting = false; // Don't use Leaflet Routing for intercontinental routes
        } else {
            console.log('Route is not intercontinental, using original routing method');
            // Clear the waypoints to use the original method
            routeWaypoints = [];
            useLeafletRouting = true; // Use Leaflet Routing for non-intercontinental routes
        }
    } else {
        // Not intercontinental, use original routing method
        useLeafletRouting = true;
    }

    // If routeWaypoints is empty or we're using Leaflet Routing, prepare waypoints
    if (routeWaypoints.length === 0 || useLeafletRouting) {
        // Create direct waypoints from tracking updates (original method)
        trackingUpdates.forEach((update, index) => {
            // Skip if no coordinates
            if (!update.latitude || !update.longitude) {
                return;
            }

            const lat = parseFloat(update.latitude);
            const lng = parseFloat(update.longitude);

            // Add to waypoints for routing
            const waypoint = {
                latLng: L.latLng(lat, lng),
                transportMode: 'road',
                name: update.location || (index === 0 ? 'Origin' : (index === trackingUpdates.length - 1 ? 'Destination' : 'Transit Point'))
            };

            routeWaypoints.push(waypoint);
        });
        console.log('Prepared waypoints for routing');
    }

    // Create markers for all waypoints
    routeWaypoints.forEach((waypoint, index) => {
        const lat = waypoint.latLng.lat;
        const lng = waypoint.latLng.lng;

        // Determine icon based on waypoint type
        let icon;
        let markerType = '';

        if (waypoint.isHub) {
            // This is a transport hub (airport or seaport)
            icon = markerIcons[waypoint.hubType];
            markerType = waypoint.hubType === 'airport' ? 'Airport' : 'Seaport';
        } else if (index === 0) {
            // Origin point
            icon = markerIcons.origin;
            markerType = 'Origin';
        } else if (index === routeWaypoints.length - 1) {
            // Destination point
            icon = markerIcons.destination;
            markerType = 'Destination';
        } else {
            // Transit point
            icon = markerIcons.transit;
            markerType = 'Transit Point';
        }

        // Create marker
        const marker = L.marker([lat, lng], {
            icon: icon,
            title: waypoint.name || markerType,
            zIndexOffset: index === 0 || index === routeWaypoints.length - 1 ? 1000 : 0 // Make origin/destination markers appear on top
        }).addTo(map);

        // Get transportation mode display name and icon
        let transportModeDisplay = waypoint.transportMode ? waypoint.transportMode.charAt(0).toUpperCase() + waypoint.transportMode.slice(1) : 'Road';
        let transportIcon = '';

        switch(waypoint.transportMode) {
            case 'air':
                transportIcon = '<i class="fas fa-plane"></i>';
                break;
            case 'sea':
                transportIcon = '<i class="fas fa-ship"></i>';
                break;
            case 'rail':
                transportIcon = '<i class="fas fa-train"></i>';
                break;
            default:
                transportIcon = '<i class="fas fa-truck"></i>';
                break;
        }

        // Create popup content
        let popupContent = `
            <div class="map-info-window">
                <div class="popup-header">
                    <h3>${waypoint.name || markerType}</h3>
                    <div class="popup-badges">
                        <span class="marker-type ${markerType.toLowerCase().replace(' ', '-')}-type">
                            ${markerType}
                        </span>`;

        // Add transport mode badge if it's a hub
        if (waypoint.isHub) {
            popupContent += `
                        <span class="transport-mode ${waypoint.transportMode}-mode">
                            ${transportIcon} ${transportModeDisplay}
                        </span>`;
        }

        popupContent += `
                    </div>
                </div>

                <div class="popup-content">
                    <p><i class="fas fa-map-marker-alt"></i> <strong>Location:</strong> ${waypoint.city || waypoint.name || markerType}</p>`;

        // Add hub code if it's a transport hub
        if (waypoint.hubCode) {
            popupContent += `
                    <p><i class="fas fa-code"></i> <strong>Code:</strong> ${waypoint.hubCode}</p>`;
        }

        // Add coordinates
        popupContent += `
                    <p class="coordinates"><i class="fas fa-globe"></i> <strong>Coordinates:</strong>
                        <span title="Click to open in Google Maps" data-lat="${lat}" data-lng="${lng}" class="coordinate-link">
                            ${lat.toFixed(4)}, ${lng.toFixed(4)}
                        </span>
                        <a href="https://www.google.com/maps?q=${lat},${lng}" target="_blank" class="map-link" title="Open in Google Maps">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </p>
                </div>
            </div>
        `;

        // Add popup to marker with enhanced styling
        const popup = L.popup({
            maxWidth: 300,
            className: 'enhanced-popup'
        }).setContent(popupContent);

        marker.bindPopup(popup);

        // Add event listener for coordinate link clicks
        marker.on('popupopen', function() {
            const coordinateLink = document.querySelector('.coordinate-link');
            if (coordinateLink) {
                coordinateLink.addEventListener('click', function() {
                    const lat = this.getAttribute('data-lat');
                    const lng = this.getAttribute('data-lng');
                    window.open(`https://www.google.com/maps?q=${lat},${lng}`, '_blank');
                });
            }
        });

        // Store marker
        markers.push(marker);

        // Add label for origin and destination
        if (index === 0 || index === routeWaypoints.length - 1) {
            const labelText = index === 0 ? 'Origin' : 'Destination';
            L.marker([lat, lng], {
                icon: L.divIcon({
                    className: 'map-label',
                    html: `<div>${labelText}</div>`,
                    iconSize: [120, 20],
                    iconAnchor: [60, -10]
                })
            }).addTo(map);
        }
    });

    // Create the route lines
    if (routeWaypoints.length > 1) {
        // Add loading indicator
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'map-loading';
        loadingIndicator.innerHTML = '<i class="fas fa-circle-notch fa-spin"></i> Calculating route...';
        mapElement.appendChild(loadingIndicator);

        if (useLeafletRouting) {
            // Use Leaflet Routing Machine for non-intercontinental routes (original method)
            console.log('Using Leaflet Routing Machine for non-intercontinental route');

            // Create waypoints for Leaflet Routing Machine
            const routingWaypoints = routeWaypoints.map(wp => L.latLng(wp.latLng.lat, wp.latLng.lng));

            // Create the routing control
            const routingControl = L.Routing.control({
                waypoints: routingWaypoints,
                routeWhileDragging: false,
                showAlternatives: false,
                fitSelectedRoutes: true,
                show: false, // Don't show the routing control UI
                lineOptions: {
                    styles: [
                        { color: '#5c2be2', opacity: 0.8, weight: 4 }
                    ],
                    addWaypoints: false
                },
                createMarker: function() { return null; } // Don't create markers, we already have them
            }).addTo(map);

            // Listen for route calculation completion
            routingControl.on('routesfound', function(e) {
                console.log('Routes found:', e.routes);

                // Get the route coordinates
                const route = e.routes[0];
                const coordinates = route.coordinates;

                // Create animated path
                if (window.createCustomAntPath) {
                    console.log('Creating custom ant paths for Leaflet route with', coordinates.length, 'points');

                    // First, completely remove the Leaflet Routing Machine paths
                    // This is more aggressive than just hiding them
                    try {
                        // Remove all routing lines from the DOM
                        const routeLines = document.querySelectorAll('.leaflet-routing-line');
                        routeLines.forEach(line => {
                            if (line.parentNode) {
                                line.parentNode.removeChild(line);
                            }
                        });

                        // Also remove all paths from the overlay pane that aren't our custom paths
                        const overlayPaths = document.querySelectorAll('.leaflet-overlay-pane path:not(.custom-ant-path):not(.custom-ant-path-pulse)');
                        overlayPaths.forEach(path => {
                            if (path.parentNode) {
                                path.parentNode.removeChild(path);
                            }
                        });

                        console.log('Removed', routeLines.length + overlayPaths.length, 'original routing paths');
                    } catch (e) {
                        console.error('Error removing original paths:', e);
                    }

                    // Create a single path for the entire route first with enhanced visibility
                    const mainPath = window.createCustomAntPath(map, coordinates, {
                        color: '#651fff', // Bright deep purple for better visibility
                        weight: 5, // Thicker line
                        opacity: 0.9, // Higher opacity
                        pulseColor: '#69f0ae', // Bright green pulse
                        className: 'custom-ant-path road-path main-route'
                    });

                    console.log('Created main route path:', mainPath);

                    // For non-intercontinental routes, we'll just use the main path
                    // and not create additional segment paths to avoid duplication
                }

                // Make sure our custom paths are visible
                setTimeout(function() {
                    const customPaths = document.querySelectorAll('.custom-ant-path, .custom-ant-path-pulse, .route-path, .air-path, .sea-path, .rail-path, .road-path');
                    customPaths.forEach(path => {
                        path.style.display = 'block';
                        path.style.visibility = 'visible';
                    });
                }, 100);

                // Remove loading indicator
                if (loadingIndicator.parentNode) {
                    loadingIndicator.parentNode.removeChild(loadingIndicator);
                }
            });

            // Handle routing errors
            routingControl.on('routingerror', function(e) {
                console.error('Routing error:', e.error);

                // Fall back to direct lines if routing fails
                createDirectLines();

                // Completely remove any Leaflet Routing Machine paths that might have been created
                try {
                    // Remove all routing lines from the DOM
                    const routeLines = document.querySelectorAll('.leaflet-routing-line');
                    routeLines.forEach(line => {
                        if (line.parentNode) {
                            line.parentNode.removeChild(line);
                        }
                    });

                    // Also remove all paths from the overlay pane that aren't our custom paths
                    const overlayPaths = document.querySelectorAll('.leaflet-overlay-pane path:not(.custom-ant-path):not(.custom-ant-path-pulse)');
                    overlayPaths.forEach(path => {
                        if (path.parentNode) {
                            path.parentNode.removeChild(path);
                        }
                    });

                    console.log('Removed', routeLines.length + overlayPaths.length, 'original routing paths after error');
                } catch (e) {
                    console.error('Error removing original paths after routing error:', e);
                }

                // Remove loading indicator
                if (loadingIndicator.parentNode) {
                    loadingIndicator.parentNode.removeChild(loadingIndicator);
                }
            });
        } else {
            // Create direct lines for intercontinental routes
            createDirectLines();

            // Remove loading indicator
            if (loadingIndicator.parentNode) {
                loadingIndicator.parentNode.removeChild(loadingIndicator);
            }
        }
    }

    // Function to create lines between waypoints, using routing for road segments
    function createDirectLines() {
        console.log('Creating lines between waypoints, using routing for road segments');

        // Completely remove any Leaflet Routing Machine paths
        try {
            // Remove all routing lines from the DOM
            const routeLines = document.querySelectorAll('.leaflet-routing-line');
            routeLines.forEach(line => {
                if (line.parentNode) {
                    line.parentNode.removeChild(line);
                }
            });

            // Also remove all paths from the overlay pane that aren't our custom paths
            const overlayPaths = document.querySelectorAll('.leaflet-overlay-pane path:not(.custom-ant-path):not(.custom-ant-path-pulse)');
            overlayPaths.forEach(path => {
                if (path.parentNode) {
                    path.parentNode.removeChild(path);
                }
            });

            console.log('Removed', routeLines.length + overlayPaths.length, 'original routing paths before creating lines');
        } catch (e) {
            console.error('Error removing original paths before creating lines:', e);
        }

        // Create route segments
        for (let i = 0; i < routeWaypoints.length - 1; i++) {
            const from = routeWaypoints[i];
            const to = routeWaypoints[i + 1];

            // Determine the transport mode for this segment
            const transportMode = to.transportMode || 'road';

            // For road segments, use Leaflet Routing Machine to follow actual roads
            if (transportMode === 'road') {
                console.log('Creating road segment with routing from', from.name, 'to', to.name);

                // Create a temporary routing control for this segment
                const segmentRoutingControl = L.Routing.control({
                    waypoints: [
                        from.latLng,
                        to.latLng
                    ],
                    routeWhileDragging: false,
                    addWaypoints: false,
                    draggableWaypoints: false,
                    fitSelectedRoutes: false,
                    showAlternatives: false,
                    lineOptions: {
                        styles: [
                            { color: '#651fff', opacity: 0, weight: 0 } // Hide the original path
                        ]
                    },
                    createMarker: function() { return null; } // Don't create markers
                }).addTo(map);

                // Listen for route calculation completion
                segmentRoutingControl.on('routesfound', function(e) {
                    console.log('Road segment route found:', e.routes[0]);

                    // Get the route coordinates
                    const route = e.routes[0];
                    const coordinates = route.coordinates;

                    // Create animated path for the road segment
                    if (window.createCustomAntPath) {
                        window.createCustomAntPath(map, coordinates, {
                            color: '#651fff', // Bright deep purple for road
                            weight: 5,
                            opacity: 0.9,
                            pulseColor: '#69f0ae',
                            className: 'custom-ant-path road-path'
                        });

                        console.log('Created road segment path with routing');

                        // Hide the original routing line
                        setTimeout(function() {
                            // Hide only Leaflet Routing Machine paths
                            const routeLines = document.querySelectorAll('.leaflet-routing-line');
                            routeLines.forEach(line => {
                                line.style.display = 'none';
                            });
                        }, 100);
                    }
                });

                // Handle routing errors
                segmentRoutingControl.on('routingerror', function(e) {
                    console.error('Road segment routing error:', e.error);

                    // Fall back to direct line for this segment
                    createDirectSegment(from, to, transportMode);

                    // Hide the routing control's UI elements
                    setTimeout(function() {
                        const routingContainer = document.querySelector('.leaflet-routing-container');
                        if (routingContainer) {
                            routingContainer.style.display = 'none';
                        }
                    }, 100);
                });

                continue; // Skip to the next segment
            }

            // For non-road segments (air, sea, rail), create direct lines
            createDirectSegment(from, to, transportMode);
        }

        // Helper function to create a direct segment between two points
        function createDirectSegment(from, to, transportMode) {
            console.log('Creating direct segment for', transportMode, 'from', from.name, 'to', to.name);

            // Create a path between these two points
            const pathCoordinates = [from.latLng, to.latLng];

            // Style based on transport mode
            let pathStyle = {
                weight: 4,
                opacity: 0.8,
                className: `route-path ${transportMode}-path`
            };

            // Set color based on transport mode with brighter, more visible colors
            switch (transportMode) {
                case 'air':
                    pathStyle.color = '#00e5ff'; // Bright cyan for air
                    pathStyle.dashArray = '10, 10'; // Dashed line for air
                    pathStyle.weight = 5; // Thicker line for better visibility
                    break;
                case 'sea':
                    pathStyle.color = '#536dfe'; // Bright indigo for sea
                    pathStyle.dashArray = '5, 10'; // Different dash pattern for sea
                    pathStyle.weight = 5; // Thicker line for better visibility
                    break;
                case 'rail':
                    pathStyle.color = '#d500f9'; // Bright purple for rail
                    pathStyle.dashArray = '1, 10'; // Dotted line for rail
                    pathStyle.weight = 5; // Thicker line for better visibility
                    break;
                default:
                    pathStyle.color = '#651fff'; // Bright deep purple for road
                    pathStyle.weight = 5; // Thicker line for better visibility
                    break;
            }

            // Create the path
            console.log(`Creating path for transport mode: ${transportMode} with class: ${pathStyle.className}`);
            const path = L.polyline(pathCoordinates, pathStyle).addTo(map);

            // Ensure the path is visible by directly setting its style
            if (path._path) {
                console.log('Path element found, ensuring visibility');
                path._path.style.display = 'block';
                path._path.style.visibility = 'visible';
                path._path.style.opacity = pathStyle.opacity.toString();
            } else {
                console.log('Path element not found immediately');
                // Try to find and style the path after a short delay
                setTimeout(() => {
                    if (path._path) {
                        console.log('Path element found after delay');
                        path._path.style.display = 'block';
                        path._path.style.visibility = 'visible';
                        path._path.style.opacity = pathStyle.opacity.toString();
                    } else {
                        console.log('Path element still not found after delay');
                    }
                }, 100);
            }

            // Add animation if supported
            if (window.createCustomAntPath && transportMode !== 'air') {
                // Remove the regular path
                map.removeLayer(path);

                // Create animated path with enhanced visibility
                console.log(`Creating custom ant path for transport mode: ${transportMode}`);

                // Define pulse colors based on transport mode for better contrast
                let pulseColor;
                switch (transportMode) {
                    case 'air':
                        pulseColor = '#b2ff59'; // Lime green for air
                        break;
                    case 'sea':
                        pulseColor = '#00e5ff'; // Cyan for sea
                        break;
                    case 'rail':
                        pulseColor = '#ffea00'; // Yellow for rail
                        break;
                    default:
                        pulseColor = '#69f0ae'; // Green for road
                        break;
                }

                const antPath = window.createCustomAntPath(map, pathCoordinates, {
                    color: pathStyle.color,
                    weight: pathStyle.weight,
                    opacity: 0.9, // Increased opacity for better visibility
                    dashArray: pathStyle.dashArray || '10, 20',
                    pulseColor: pulseColor,
                    className: `custom-ant-path ${transportMode}-path`
                });

                // Log the created path for debugging
                console.log('Custom ant path created:', antPath);

                // Ensure the path is visible after a short delay
                setTimeout(() => {
                    // Find all paths with this class and ensure they're visible
                    const customPaths = document.querySelectorAll(`.custom-ant-path.${transportMode}-path, .custom-ant-path-pulse.${transportMode}-path`);
                    console.log(`Found ${customPaths.length} custom paths for ${transportMode}`);

                    customPaths.forEach(path => {
                        path.style.display = 'block';
                        path.style.visibility = 'visible';
                        path.style.opacity = pathStyle.opacity.toString();
                    });
                }, 200);
            }
        }
    }

    // Fit map to markers or center on single point
    if (routeWaypoints.length > 1) {
        // Fit map to all markers
        const group = new L.featureGroup(markers);
        map.fitBounds(group.getBounds(), {
            padding: [50, 50]
        });
    } else if (routeWaypoints.length === 1) {
        // If only one point, center on it
        map.setView(routeWaypoints[0].latLng, 10);
    }

    // Update shipment progress based on tracking updates
    updateShipmentProgress(trackingUpdates, routeWaypoints);

    /**
     * Update shipment progress based on tracking updates and route
     * @param {Array} trackingUpdates - Array of tracking updates
     * @param {Array} routeWaypoints - Array of route waypoints
     */
    function updateShipmentProgress(trackingUpdates, routeWaypoints) {
        const progressElement = document.getElementById('shipment-progress-bar');
        const progressTextElement = document.getElementById('progress-percentage');

        if (!progressElement || !progressTextElement) {
            console.log('Progress elements not found');
            return;
        }

        console.log('Updating shipment progress');

        // Sort tracking updates by timestamp (oldest first)
        const sortedUpdates = [...trackingUpdates].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        // Calculate progress based on status
        let progressPercentage = 0;

        // Define status weights for progress calculation
        const statusWeights = {
            'processing': 10,
            'pending': 10,
            'picked_up': 20,
            'in_transit': 40,
            'arrived_at_facility': 60,
            'out_for_delivery': 80,
            'delivered': 100,
            'delayed': 60,  // Assume delayed is somewhere in the middle
            'cancelled': 100  // Cancelled shipments show as 100% complete
        };

        // Get the current status (last update)
        let currentStatus = '';
        try {
            // Handle different possible formats of status
            const statusRaw = sortedUpdates[sortedUpdates.length - 1].status;
            if (typeof statusRaw === 'string') {
                currentStatus = statusRaw.toLowerCase().replace(/[-\s]/g, '_');
            } else if (typeof statusRaw === 'object' && statusRaw !== null) {
                // If status is an object, try to get a string property
                currentStatus = (statusRaw.name || statusRaw.value || statusRaw.status || '').toLowerCase().replace(/[-\s]/g, '_');
            }
            console.log('Current shipment status:', currentStatus);
        } catch (e) {
            console.error('Error getting shipment status:', e);
            currentStatus = 'unknown';
        }

        // Get the base progress from status
        progressPercentage = statusWeights[currentStatus] || 0;

        // If the shipment is in transit or out for delivery, calculate more precise progress
        if ((currentStatus === 'in_transit' || currentStatus === 'out_for_delivery' || currentStatus === 'arrived_at_facility') && routeWaypoints.length > 1) {
            // For intercontinental routes, calculate progress based on completed segments
            if (isIntercontinental) {
                // Count how many segments are complete
                let completedSegments = 0;
                const totalSegments = routeWaypoints.length - 1;

                // Assume segments are completed based on the current status
                if (currentStatus === 'in_transit') {
                    // In transit - assume first segment is complete
                    completedSegments = 1;
                } else if (currentStatus === 'arrived_at_facility') {
                    // Arrived at facility - assume first two segments are complete
                    completedSegments = Math.min(2, totalSegments);
                } else if (currentStatus === 'out_for_delivery') {
                    // Out for delivery - assume all but last segment are complete
                    completedSegments = totalSegments - 1;
                }

                // Calculate progress percentage
                const segmentProgress = completedSegments / totalSegments;

                // Set base and max progress based on status
                let baseProgress, maxProgress;
                if (currentStatus === 'in_transit') {
                    baseProgress = 20;
                    maxProgress = 60;
                } else if (currentStatus === 'arrived_at_facility') {
                    baseProgress = 40;
                    maxProgress = 70;
                } else if (currentStatus === 'out_for_delivery') {
                    baseProgress = 60;
                    maxProgress = 90;
                } else {
                    baseProgress = 20;
                    maxProgress = 70;
                }

                // Calculate scaled progress
                const progressRange = maxProgress - baseProgress;
                progressPercentage = baseProgress + (segmentProgress * progressRange);
                console.log(`Segment progress calculation: ${completedSegments}/${totalSegments} = ${segmentProgress.toFixed(2)} -> ${progressPercentage.toFixed(2)}%`);
            } else {
                // For regular routes, calculate progress based on distance
                let totalDistance = 0;
                let traveledDistance = 0;

                // Calculate total route distance
                for (let i = 0; i < routeWaypoints.length - 1; i++) {
                    const segmentDistance = routeWaypoints[i].latLng.distanceTo(routeWaypoints[i+1].latLng);
                    totalDistance += segmentDistance;

                    // Only count traveled segments (up to the current location)
                    if (i < sortedUpdates.length - 1) {
                        traveledDistance += segmentDistance;
                    }
                }

                // Calculate progress percentage based on distance traveled
                if (totalDistance > 0) {
                    // Set base and max progress based on status
                    let baseProgress, maxProgress;
                    if (currentStatus === 'in_transit') {
                        baseProgress = 20;
                        maxProgress = 60;
                    } else if (currentStatus === 'arrived_at_facility') {
                        baseProgress = 40;
                        maxProgress = 70;
                    } else if (currentStatus === 'out_for_delivery') {
                        baseProgress = 60;
                        maxProgress = 90;
                    } else {
                        baseProgress = 20;
                        maxProgress = 70;
                    }

                    // Calculate scaled progress
                    const distanceProgress = (traveledDistance / totalDistance);
                    const progressRange = maxProgress - baseProgress;
                    progressPercentage = baseProgress + (distanceProgress * progressRange);
                    console.log(`Distance progress calculation: ${traveledDistance}/${totalDistance} = ${distanceProgress.toFixed(2)} -> ${progressPercentage.toFixed(2)}%`);
                }
            }
        }

        // Ensure progress is between 0 and 100
        progressPercentage = Math.max(0, Math.min(100, Math.round(progressPercentage)));

        // Update progress bar width
        progressElement.style.width = `${progressPercentage}%`;

        // Update progress text
        progressTextElement.textContent = `${progressPercentage}%`;

        console.log(`Shipment progress: ${progressPercentage}%`);
    }
})();
// End of intercontinental tracking map script
