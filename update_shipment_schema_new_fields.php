<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Set page title
$pageTitle = 'Update Shipment Schema';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Include header
include_once 'includes/header.php';
?>

<div class="container" style="padding: 40px 0;">
    <div class="card" style="padding: 20px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1);">
        <h1>Update Shipment Schema</h1>
        <p>This script will update the shipments table to add new fields for shipment name, description, package name, and package description.</p>
        
        <div class="update-log" style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0; max-height: 300px; overflow-y: auto;">
            <?php
            try {
                // Check if shipment_name column exists
                $stmt = $conn->prepare("SELECT COUNT(*) as column_exists FROM information_schema.COLUMNS 
                                        WHERE TABLE_SCHEMA = DATABASE() 
                                        AND TABLE_NAME = 'shipments' 
                                        AND COLUMN_NAME = 'shipment_name'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($result['column_exists'] == 0) {
                    // Add shipment name and description columns
                    $conn->exec("ALTER TABLE shipments ADD COLUMN shipment_name VARCHAR(100) DEFAULT NULL");
                    $conn->exec("ALTER TABLE shipments ADD COLUMN shipment_description TEXT DEFAULT NULL");
                    echo "<p class='success'>✓ Added shipment name and description columns to shipments table.</p>";
                } else {
                    echo "<p class='info'>ℹ Shipment name and description columns already exist.</p>";
                }
                
                // Check if package_name column exists
                $stmt = $conn->prepare("SELECT COUNT(*) as column_exists FROM information_schema.COLUMNS 
                                        WHERE TABLE_SCHEMA = DATABASE() 
                                        AND TABLE_NAME = 'shipments' 
                                        AND COLUMN_NAME = 'package_name'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($result['column_exists'] == 0) {
                    // Add package name and description columns
                    $conn->exec("ALTER TABLE shipments ADD COLUMN package_name VARCHAR(100) DEFAULT NULL");
                    $conn->exec("ALTER TABLE shipments ADD COLUMN package_description TEXT DEFAULT NULL");
                    echo "<p class='success'>✓ Added package name and description columns to shipments table.</p>";
                } else {
                    echo "<p class='info'>ℹ Package name and description columns already exist.</p>";
                }
                
                echo "<p class='success'>✓ Database schema update completed successfully.</p>";
                
            } catch (PDOException $e) {
                echo "<p class='error'>✗ Error updating database schema: " . $e->getMessage() . "</p>";
            }
            ?>
        </div>
        
        <p>The database schema has been updated to include fields for shipment name, shipment description, package name, and package description. These fields are now available for use in the admin interface and tracking page.</p>
        
        <p><strong>Next Steps:</strong></p>
        <ul>
            <li>Go to the admin dashboard to manage shipments with the new fields</li>
            <li>Add shipment and package details using the new fields</li>
            <li>View the enhanced tracking page with the new information</li>
        </ul>
    </div>

    <div class="update-footer" style="margin-top: 20px; text-align: center;">
        <a href="admin/index.php" class="btn primary-btn">Go to Admin Dashboard</a>
    </div>
</div>

<?php
// Include footer
include_once 'includes/footer.php';
?>
