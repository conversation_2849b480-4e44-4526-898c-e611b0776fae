-- Check if delivered_at column exists and add it if it doesn't
SET @dbname = 'tracking_cms';
SET @tablename = 'shipments';
SET @columnname = 'delivered_at';
SET @preparedStatement = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
   WHERE TABLE_SCHEMA = @dbname
     AND TABLE_NAME = @tablename
     AND COLUMN_NAME = @columnname) > 0,
  'SELECT 1',
  CONCAT('ALTER TABLE ', @tablename, ' ADD COLUMN ', @columnname, ' DATETIME DEFAULT NULL')
));
PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;

-- Update delivered_at for delivered shipments
UPDATE shipments SET delivered_at = DATE_SUB(NOW(), INTERVAL FLOOR(RAND() * 10) DAY) WHERE status = 'delivered';
