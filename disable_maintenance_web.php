<?php
// Include configuration and database connection
require_once 'includes/config.php';
require_once 'includes/db.php';

// Add some basic styling
echo '<!DOCTYPE html>
<html>
<head>
    <title>Disable Maintenance Mode</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        a.button {
            display: inline-block;
            background-color: #4a6cf7;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
    </style>
</head>
<body>';

try {
    // Update the maintenance_mode setting to '0' (disabled)
    $updateStmt = $conn->prepare("UPDATE system_settings SET setting_value = '0', updated_at = NOW() WHERE setting_key = 'maintenance_mode'");
    $result = $updateStmt->execute();
    
    if($result) {
        echo '<div class="success">';
        echo "<h2>Success!</h2>";
        echo "<p>Maintenance mode has been disabled.</p>";
        echo '</div>';
    } else {
        echo '<div class="error">';
        echo "<h2>Error</h2>";
        echo "<p>Failed to disable maintenance mode.</p>";
        echo '</div>';
    }
} catch (PDOException $e) {
    echo '<div class="error">';
    echo "<h2>Database Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo '</div>';
}

echo '<a href="index.php" class="button">Return to Homepage</a>';
echo '</body></html>';
?>
