<?php
/**
 * Maintenance Tools Configuration
 *
 * This file defines the maintenance tools available in the system.
 * Each tool is defined with its name, description, icon, file path, and category.
 *
 * Categories:
 * - database: Database structure and data tools
 * - geocoding: Geocoding and mapping tools
 * - system: System maintenance and configuration tools
 * - debug: Debugging tools
 */

// Define the maintenance tools
$maintenanceTools = [
    // Database Tools
    [
        'id' => 'check_database',
        'name' => 'Database Overview',
        'description' => 'View database tables and structure',
        'icon' => 'fas fa-database',
        'file' => 'check_database.php',
        'category' => 'database',
        'enabled' => true
    ],
    [
        'id' => 'check_status_enum',
        'name' => 'Check Status Values',
        'description' => 'Check shipment status values in database',
        'icon' => 'fas fa-list-check',
        'file' => 'check_status_enum_new.php',
        'category' => 'database',
        'enabled' => true
    ],
    [
        'id' => 'update_status_enum',
        'name' => 'Update Database Schema',
        'description' => 'Update database schema with new status values',
        'icon' => 'fas fa-wrench',
        'file' => 'update_status_enum_fixed.php',
        'category' => 'database',
        'enabled' => true
    ],
    [
        'id' => 'fix_status_consistency',
        'name' => 'Fix Status Consistency',
        'description' => 'Fix inconsistent status values in database',
        'icon' => 'fas fa-broom',
        'file' => 'fix_status_consistency.php',
        'category' => 'database',
        'enabled' => true
    ],
    [
        'id' => 'create_missing_tables',
        'name' => 'Create Missing Tables',
        'description' => 'Create geocoding_cache and system_settings tables',
        'icon' => 'fas fa-table',
        'file' => 'create_missing_tables.php',
        'category' => 'database',
        'enabled' => true
    ],

    // Geocoding Tools
    [
        'id' => 'test_geocoding',
        'name' => 'Test Geocoding',
        'description' => 'Test geocoding functionality with sample addresses',
        'icon' => 'fas fa-map-marker-alt',
        'file' => 'test_geocoding.php',
        'category' => 'geocoding',
        'enabled' => true
    ],
    [
        'id' => 'debug_geocoding',
        'name' => 'Debug Geocoding',
        'description' => 'Advanced debugging for geocoding issues',
        'icon' => 'fas fa-bug',
        'file' => 'debug_geocoding.php',
        'category' => 'geocoding',
        'enabled' => true
    ],
    [
        'id' => 'geocode_existing',
        'name' => 'Geocode Existing Updates',
        'description' => 'Add coordinates to tracking updates that lack them',
        'icon' => 'fas fa-map-pin',
        'file' => 'geocode_existing_updates.php',
        'category' => 'geocoding',
        'enabled' => true
    ],
    [
        'id' => 'geocode_tracking_updates',
        'name' => 'Geocode All Tracking Updates',
        'description' => 'Process all tracking updates to add geolocation data',
        'icon' => 'fas fa-globe',
        'file' => 'geocode_tracking_updates.php',
        'category' => 'geocoding',
        'enabled' => true
    ],

    // System Tools
    [
        'id' => 'toggle_maintenance',
        'name' => 'Toggle Maintenance Mode',
        'description' => 'Enable or disable maintenance mode',
        'icon' => 'fas fa-toggle-on',
        'file' => 'admin/toggle-maintenance.php',
        'category' => 'system',
        'enabled' => true
    ],
    [
        'id' => 'disable_maintenance_direct',
        'name' => 'Disable Maintenance Mode',
        'description' => 'Emergency tool to disable maintenance mode',
        'icon' => 'fas fa-power-off',
        'file' => 'disable_maintenance_direct.php',
        'category' => 'system',
        'enabled' => true
    ],

    // Legacy/Deprecated Tools (disabled by default)
    [
        'id' => 'update_status_enum_debug',
        'name' => 'Debug Database Schema',
        'description' => 'Debug version of update_status_enum_new.php',
        'icon' => 'fas fa-microscope',
        'file' => 'update_status_enum_debug.php',
        'category' => 'debug',
        'enabled' => false
    ],
    [
        'id' => 'check_status_enum_old',
        'name' => 'Check Status Values (Old)',
        'description' => 'Old version of status check tool',
        'icon' => 'fas fa-list',
        'file' => 'check_status_enum.php',
        'category' => 'debug',
        'enabled' => false
    ],
    [
        'id' => 'update_status_enum_old',
        'name' => 'Update Database Schema (Old)',
        'description' => 'Old version of database update tool',
        'icon' => 'fas fa-tools',
        'file' => 'update_status_enum.php',
        'category' => 'debug',
        'enabled' => false
    ],
    [
        'id' => 'update_db',
        'name' => 'Update Database (Legacy)',
        'description' => 'Legacy database update tool',
        'icon' => 'fas fa-database',
        'file' => 'update_db.php',
        'category' => 'debug',
        'enabled' => false
    ]
];

/**
 * Get all maintenance tools
 *
 * @param bool $enabledOnly Whether to return only enabled tools
 * @return array Array of maintenance tools
 */
function getMaintenanceTools($enabledOnly = true) {
    global $maintenanceTools;

    if ($enabledOnly) {
        return array_filter($maintenanceTools, function($tool) {
            return $tool['enabled'];
        });
    }

    return $maintenanceTools;
}

/**
 * Get maintenance tools by category
 *
 * @param string $category The category to filter by
 * @param bool $enabledOnly Whether to return only enabled tools
 * @return array Array of maintenance tools in the specified category
 */
function getMaintenanceToolsByCategory($category, $enabledOnly = true) {
    global $maintenanceTools;

    $tools = array_filter($maintenanceTools, function($tool) use ($category, $enabledOnly) {
        if ($enabledOnly && !$tool['enabled']) {
            return false;
        }

        return $tool['category'] === $category;
    });

    return $tools;
}

/**
 * Get all maintenance tool categories
 *
 * @return array Array of unique categories
 */
function getMaintenanceToolCategories() {
    global $maintenanceTools;

    $categories = array_map(function($tool) {
        return $tool['category'];
    }, $maintenanceTools);

    return array_unique($categories);
}

/**
 * Get a maintenance tool by ID
 *
 * @param string $id The tool ID
 * @return array|null The tool or null if not found
 */
function getMaintenanceToolById($id) {
    global $maintenanceTools;

    foreach ($maintenanceTools as $tool) {
        if ($tool['id'] === $id) {
            return $tool;
        }
    }

    return null;
}

/**
 * Get category name for display
 *
 * @param string $category The category key
 * @return string The formatted category name
 */
function getMaintenanceToolCategoryName($category) {
    $names = [
        'database' => 'Database Tools',
        'geocoding' => 'Geocoding Tools',
        'system' => 'System Tools',
        'debug' => 'Debug & Legacy Tools'
    ];

    return $names[$category] ?? ucfirst($category);
}

/**
 * Get category icon
 *
 * @param string $category The category key
 * @return string The category icon class
 */
function getMaintenanceToolCategoryIcon($category) {
    $icons = [
        'database' => 'fas fa-database',
        'geocoding' => 'fas fa-map-marked-alt',
        'system' => 'fas fa-cogs',
        'debug' => 'fas fa-bug'
    ];

    return $icons[$category] ?? 'fas fa-tools';
}
