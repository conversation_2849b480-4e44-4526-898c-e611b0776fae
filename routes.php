<?php
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Set page title
$pageTitle = 'Route Information';

// Get origin and destination from query parameters
$origin = isset($_GET['origin']) ? sanitize($_GET['origin']) : '';
$destination = isset($_GET['destination']) ? sanitize($_GET['destination']) : '';

// Check if both origin and destination are provided
$showResults = !empty($origin) && !empty($destination);

// Get route information if showing results
$routeInfo = null;
$estimatedTime = null;
$estimatedCost = null;

if ($showResults) {
    // Calculate distance (simplified for demo)
    $distance = calculateDistance($origin, $destination);

    // Calculate estimated time (simplified for demo)
    $estimatedTime = calculateEstimatedTime($distance);

    // Calculate estimated cost (simplified for demo)
    $estimatedCost = calculateEstimatedCost($distance);

    // Get available services for this route
    $availableServices = getAvailableServices($origin, $destination);
}

// Add Leaflet CSS and JS for the route map
$additionalCss = [
    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css',
    'https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.css',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'
];

$additionalJs = [
    'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js',
    'https://unpkg.com/leaflet-routing-machine@3.2.12/dist/leaflet-routing-machine.js',
    'https://unpkg.com/leaflet-polylinedecorator@1.6.0/dist/leaflet.polylineDecorator.js'
];

// Add custom JS for routing
// We'll implement the custom-ant-path directly in this file instead of loading it externally

// Include header
include_once 'includes/header.php';

/**
 * Calculate approximate distance between two locations
 * This is a simplified version for demo purposes
 */
function calculateDistance($origin, $destination) {
    // For demo purposes, return a random distance between 500 and 5000 miles
    return rand(500, 5000);
}

/**
 * Calculate estimated delivery time based on distance
 * This is a simplified version for demo purposes
 */
function calculateEstimatedTime($distance) {
    // For demo purposes, calculate based on distance
    if ($distance < 1000) {
        return '2-3 days';
    } else if ($distance < 2000) {
        return '4-5 days';
    } else if ($distance < 3000) {
        return '7-10 days';
    } else {
        return '10-14 days';
    }
}

/**
 * Calculate estimated cost based on distance
 * This is a simplified version for demo purposes
 */
function calculateEstimatedCost($distance) {
    // Base rate plus per-mile cost
    $baseRate = 150;
    $perMileRate = 0.75;

    return $baseRate + ($distance * $perMileRate);
}

/**
 * Get available services for a route
 * This is a simplified version for demo purposes
 */
function getAvailableServices($origin, $destination) {
    // For demo purposes, return a set of services
    return [
        [
            'name' => 'Standard Shipping',
            'description' => 'Regular delivery service with standard handling',
            'time' => 'Standard delivery time',
            'cost' => 1.0 // Multiplier of base cost
        ],
        [
            'name' => 'Express Shipping',
            'description' => 'Expedited delivery with priority handling',
            'time' => '30% faster than standard',
            'cost' => 1.5 // Multiplier of base cost
        ],
        [
            'name' => 'Premium Shipping',
            'description' => 'Our fastest service with guaranteed delivery times',
            'time' => '50% faster than standard',
            'cost' => 2.0 // Multiplier of base cost
        ]
    ];
}
?>

<!-- Hero Section -->
<section class="hero">
    <div class="container">
        <div class="hero-content">
            <h1>Route Information</h1>
            <p>Find the best shipping options between locations worldwide.</p>
        </div>
    </div>
</section>

<!-- Route Search Section -->
<section class="route-search-section">
    <div class="container">
        <div class="route-search-box">
            <h2>Find Routes & Rates</h2>
            <form class="route-form" method="GET" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                <div class="form-row">
                    <div class="form-group">
                        <label for="origin">From</label>
                        <input type="text" id="origin" name="origin" placeholder="Origin" value="<?php echo htmlspecialchars($origin); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="destination">To</label>
                        <input type="text" id="destination" name="destination" placeholder="Destination" value="<?php echo htmlspecialchars($destination); ?>" required>
                    </div>
                </div>
                <button type="submit" class="btn primary-btn"><i class="fas fa-route"></i> Search</button>
            </form>
        </div>
    </div>
</section>

<?php if ($showResults): ?>
<!-- Route Results Section -->
<section class="route-results">
    <div class="container">
        <div class="route-overview">
            <h2>Route Overview</h2>
            <div class="route-details-grid">
                <div class="route-detail-card">
                    <div class="detail-icon">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="detail-content">
                        <h3>Origin</h3>
                        <p><?php echo htmlspecialchars($origin); ?></p>
                    </div>
                </div>

                <div class="route-detail-card">
                    <div class="detail-icon">
                        <i class="fas fa-flag-checkered"></i>
                    </div>
                    <div class="detail-content">
                        <h3>Destination</h3>
                        <p><?php echo htmlspecialchars($destination); ?></p>
                    </div>
                </div>

                <div class="route-detail-card">
                    <div class="detail-icon">
                        <i class="fas fa-route"></i>
                    </div>
                    <div class="detail-content">
                        <h3>Distance</h3>
                        <p><?php echo number_format($distance); ?> miles</p>
                    </div>
                </div>

                <div class="route-detail-card">
                    <div class="detail-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="detail-content">
                        <h3>Estimated Time</h3>
                        <p><?php echo htmlspecialchars($estimatedTime); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="available-services">
            <h2>Available Services</h2>
            <div class="services-table-container">
                <table class="services-table">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Description</th>
                            <th>Delivery Time</th>
                            <th>Estimated Cost</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($availableServices as $service): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($service['name']); ?></td>
                            <td><?php echo htmlspecialchars($service['description']); ?></td>
                            <td><?php echo htmlspecialchars($service['time']); ?></td>
                            <td>$<?php echo number_format($estimatedCost * $service['cost'], 2); ?></td>
                            <td><a href="contact.html" class="btn small-btn">Request Quote</a></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="route-map-container">
            <h2>Route Map</h2>
            <div id="tracking-map" style="height: 500px; width: 100%; position: relative; z-index: 1; background-color: #f8f9fa;">
                <noscript>
                    <div class="no-map-data">
                        <i class="fas fa-map-marked-alt"></i>
                        <p>JavaScript is required to display the interactive map.</p>
                    </div>
                </noscript>
                <!-- Map loading indicator will be added by JavaScript -->
            </div>
            <p class="map-disclaimer">This map shows an approximate route and is for illustrative purposes only. Actual routes may vary based on carrier, weather, and other factors.</p>
        </div>

        <div class="route-cta">
            <h2>Need a Custom Quote?</h2>
            <p>Contact our logistics experts for a detailed quote tailored to your specific shipping needs.</p>
            <a href="contact.html" class="btn primary-btn">Contact Us</a>
        </div>
    </div>
</section>

<!-- Initialize tracking data for map -->
<script>
    // Store origin and destination for the map
    const trackingUpdates = [
        {
            status: 'Origin',
            location: '<?php echo addslashes($origin); ?>',
            timestamp: '<?php echo date("Y-m-d H:i:s"); ?>',
            latitude: null,
            longitude: null
        },
        {
            status: 'Destination',
            location: '<?php echo addslashes($destination); ?>',
            timestamp: '<?php echo date("Y-m-d H:i:s", strtotime("+3 days")); ?>',
            latitude: null,
            longitude: null
        }
    ];
</script>

<!-- Custom AntPath Implementation -->
<script>
    // Custom Ant Path implementation for Leaflet
    window.createCustomAntPath = function(map, coordinates, options) {
        console.log('Creating custom AntPath with coordinates:', coordinates);

        if (!map) {
            console.error('Map is not defined for createCustomAntPath');
            return null;
        }

        if (!coordinates || !Array.isArray(coordinates) || coordinates.length < 2) {
            console.error('Invalid coordinates for createCustomAntPath:', coordinates);
            return null;
        }

        // Default options
        const defaultOptions = {
            color: '#e83e8c', // Pinkish red color
            weight: 4,
            opacity: 0.8,
            pulseColor: '#ff6b9d', // Lighter pinkish red for pulse
            dashArray: '10, 15',
            className: 'custom-ant-path'
        };

        // Merge options
        const pathOptions = Object.assign({}, defaultOptions, options);
        console.log('Using path options:', pathOptions);

        try {
            // Create the path
            const path = L.polyline(coordinates, {
                color: pathOptions.color,
                weight: pathOptions.weight,
                opacity: pathOptions.opacity,
                dashArray: pathOptions.dashArray,
                className: pathOptions.className,
                lineCap: 'round',
                lineJoin: 'round'
            }).addTo(map);

            console.log('Created main path');

            // Add CSS animation - directly set the style on the SVG path element
            const styleId = 'ant-path-style';
            if (!document.getElementById(styleId)) {
                const styleElement = document.createElement('style');
                styleElement.id = styleId;
                styleElement.textContent = `
                    .custom-ant-path {
                        stroke-dasharray: 10, 15;
                        animation: antDash 30s linear infinite;
                    }
                    @keyframes antDash {
                        to {
                            stroke-dashoffset: -1000;
                        }
                    }
                `;
                document.head.appendChild(styleElement);
                console.log('Added ant path animation styles');
            }

            // Ensure the animation is applied directly to the SVG path
            setTimeout(() => {
                try {
                    // Find the SVG path element and apply the animation directly
                    const pathElements = document.querySelectorAll('path.custom-ant-path');
                    console.log('Found ' + pathElements.length + ' path elements to animate');

                    pathElements.forEach(pathEl => {
                        // Apply the animation directly to the SVG element
                        pathEl.style.strokeDasharray = '10, 15';
                        pathEl.style.animation = 'antDash 30s linear infinite';
                        console.log('Applied animation directly to path element');
                    });
                } catch (e) {
                    console.error('Error applying direct animation:', e);
                }
            }, 100);

            // Add pulse effect if specified
            let pulsePath = null;
            if (pathOptions.pulseColor) {
                // Create a second line with pulse effect
                pulsePath = L.polyline(coordinates, {
                    color: pathOptions.pulseColor,
                    weight: pathOptions.weight * 1.5,
                    opacity: 0.3,
                    className: 'pulse-path'
                }).addTo(map);

                console.log('Created pulse path');

                // Add pulse animation
                const pulseStyleId = 'pulse-path-style';
                if (!document.getElementById(pulseStyleId)) {
                    const pulseStyle = document.createElement('style');
                    pulseStyle.id = pulseStyleId;
                    pulseStyle.textContent = `
                        .pulse-path {
                            animation: pulsePath 2s ease-in-out infinite;
                        }
                        @keyframes pulsePath {
                            0% { opacity: 0.1; stroke-width: ${pathOptions.weight}px; }
                            50% { opacity: 0.3; stroke-width: ${pathOptions.weight * 2}px; }
                            100% { opacity: 0.1; stroke-width: ${pathOptions.weight}px; }
                        }
                    `;
                    document.head.appendChild(pulseStyle);
                    console.log('Added pulse path animation styles');
                }
            }

            // Return both paths as an object
            return {
                mainPath: path,
                pulsePath: pulsePath,
                remove: function() {
                    try {
                        if (path) map.removeLayer(path);
                        if (pulsePath) map.removeLayer(pulsePath);
                        console.log('Removed ant path layers');
                    } catch (e) {
                        console.error('Error removing ant path layers:', e);
                    }
                }
            };
        } catch (e) {
            console.error('Error in createCustomAntPath:', e);
            return null;
        }
    };
</script>

<!-- Route Map Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing route map...');

    // Enable debug mode for Leaflet
    L.Browser.debug = true;

    // Add debug info to page
    const debugInfo = document.createElement('div');
    debugInfo.className = 'debug-info';
    debugInfo.style.display = 'none'; // Hidden by default
    debugInfo.innerHTML = '<h3>Debug Info</h3><pre id="debug-output"></pre>';
    document.body.appendChild(debugInfo);

    // Helper function to log debug info
    window.debugLog = function(message, data) {
        console.log(message, data);
        const output = document.getElementById('debug-output');
        if (output) {
            const timestamp = new Date().toISOString().substr(11, 8);
            const formattedMessage = timestamp + ': ' + message +
                (data ? ' ' + JSON.stringify(data, null, 2) : '');
            output.innerHTML += formattedMessage + '\n';
        }
    };

    // Press D to toggle debug info
    document.addEventListener('keydown', function(e) {
        if (e.key === 'd' || e.key === 'D') {
            debugInfo.style.display = debugInfo.style.display === 'none' ? 'block' : 'none';
        }
    });

    window.debugLog('Starting route map initialization');

    // Force map element to be visible with proper dimensions
    const mapElement = document.getElementById('tracking-map');
    if (!mapElement) {
        console.error('Tracking map element not found');
        return;
    }

    // Add loading indicator with ID for easier removal
    const loadingIndicatorId = 'map-loading-indicator';
    // First, remove any existing loading indicators
    const existingIndicator = document.getElementById(loadingIndicatorId);
    if (existingIndicator) {
        existingIndicator.parentNode.removeChild(existingIndicator);
    }

    // Create new loading indicator
    let loadingIndicator = document.createElement('div');
    loadingIndicator.id = loadingIndicatorId;
    loadingIndicator.className = 'map-loading';
    loadingIndicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading map...';
    mapElement.appendChild(loadingIndicator);

    // Set a timeout to remove the loading indicator after 2 seconds
    setTimeout(function() {
        const indicator = document.getElementById(loadingIndicatorId);
        if (indicator) {
            indicator.parentNode.removeChild(indicator);
            window.debugLog('Loading indicator removed by timeout');
        }
    }, 2000);

    // Also remove it when the map is ready
    window.removeMapLoading = function() {
        const indicator = document.getElementById(loadingIndicatorId);
        if (indicator) {
            indicator.parentNode.removeChild(indicator);
            window.debugLog('Loading indicator removed by map ready event');
        }
    };

    // Helper function to apply animations to path elements
    window.applyPathAnimations = function() {
        setTimeout(() => {
            try {
                // Find all SVG path elements in the document
                const allPaths = document.querySelectorAll('path');
                window.debugLog('Found ' + allPaths.length + ' total path elements');

                let animatedCount = 0;

                // Apply animation to each path that has the pinkish red color
                allPaths.forEach(path => {
                    // Check for the pinkish red color in various attributes
                    const stroke = path.getAttribute('stroke');
                    const style = path.getAttribute('style');

                    if ((stroke && stroke === '#e83e8c') ||
                        (style && style.includes('#e83e8c'))) {

                        // Add the custom class
                        path.classList.add('custom-ant-path');

                        // Apply the animation directly
                        path.style.strokeDasharray = '10, 15';
                        path.style.animation = 'antDash 30s linear infinite';

                        // Also set attributes for better compatibility
                        path.setAttribute('stroke-dasharray', '10, 15');

                        animatedCount++;
                    }
                });

                window.debugLog('Applied animations to ' + animatedCount + ' path elements');

                // If we didn't find any paths to animate, try a more aggressive approach
                if (animatedCount === 0) {
                    // Find all leaflet-interactive paths
                    const leafletPaths = document.querySelectorAll('path.leaflet-interactive');
                    window.debugLog('Found ' + leafletPaths.length + ' leaflet-interactive paths');

                    leafletPaths.forEach(path => {
                        // Apply animation to all Leaflet paths
                        path.classList.add('custom-ant-path');
                        path.style.strokeDasharray = '10, 15';
                        path.style.animation = 'antDash 30s linear infinite';
                    });

                    window.debugLog('Applied animations to all leaflet paths as fallback');
                }
            } catch (e) {
                window.debugLog('Error applying path animations: ' + e.message);
            }
        }, 1000);
    };

    // Initialize the map with explicit options
    const map = L.map('tracking-map', {
        center: [20, 0],
        zoom: 2,
        minZoom: 2,
        maxZoom: 18,
        zoomControl: true,
        trackResize: true,
        renderer: L.canvas()
    });

    window.debugLog('Map initialized');

    // Add the OpenStreetMap tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
        maxZoom: 19
    }).addTo(map);

    window.debugLog('Map tiles added');

    // Remove loading indicator when tiles are loaded
    map.whenReady(function() {
        window.debugLog('Map is ready');
        window.removeMapLoading();

        // Apply animations immediately
        window.applyPathAnimations();

        // Set up a timer to periodically apply animations
        // This helps ensure animations are applied even after map interactions
        setInterval(window.applyPathAnimations, 3000);

        // Also apply animations when the map moves or zooms
        map.on('moveend', window.applyPathAnimations);
        map.on('zoomend', window.applyPathAnimations);
    });

    // Add additional event listeners for debugging
    map.on('load', function() {
        window.debugLog('Map load event fired');
    });

    map.on('layeradd', function(e) {
        window.debugLog('Layer added to map', e.layer && e.layer.options ? e.layer.options : 'Unknown layer');
    });

    // Log any errors
    window.addEventListener('error', function(e) {
        window.debugLog('JavaScript error: ' + e.message + ' at ' + e.filename + ':' + e.lineno);
    });

    // Store origin and destination names for display
    const originName = '<?php echo addslashes($origin); ?>';
    const destinationName = '<?php echo addslashes($destination); ?>';

    // Check if tracking updates exist
    if (typeof trackingUpdates === 'undefined' || !trackingUpdates || trackingUpdates.length < 2) {
        window.debugLog('No tracking updates defined or insufficient data');
        mapElement.innerHTML = '<div class="no-map-data"><i class="fas fa-map-marked-alt"></i><p>No location data available.</p></div>';
        return;
    }

    // Simulate origin and destination coordinates (for demo purposes)
    // In a real application, you would use geocoding to get actual coordinates
    const originCoords = simulateCoordinates(originName);
    const destinationCoords = simulateCoordinates(destinationName);

    // Update tracking updates with the coordinates
    trackingUpdates[0].latitude = originCoords[0];
    trackingUpdates[0].longitude = originCoords[1];
    trackingUpdates[1].latitude = destinationCoords[0];
    trackingUpdates[1].longitude = destinationCoords[1];

    window.debugLog('Origin coordinates:', originCoords);
    window.debugLog('Destination coordinates:', destinationCoords);

    // Create custom marker icons - Simple dot style
    const originIcon = L.divIcon({
        html: '<div class="marker-dot origin-dot"></div>',
        iconSize: [16, 16],
        iconAnchor: [8, 8],
        popupAnchor: [0, -10],
        className: 'custom-marker'
    });

    const destinationIcon = L.divIcon({
        html: '<div class="marker-dot destination-dot"></div>',
        iconSize: [16, 16],
        iconAnchor: [8, 8],
        popupAnchor: [0, -10],
        className: 'custom-marker'
    });

    // Add markers for origin and destination
    const originMarker = L.marker(originCoords, {
        title: originName,
        icon: originIcon
    }).addTo(map);

    const destinationMarker = L.marker(destinationCoords, {
        title: destinationName,
        icon: destinationIcon
    }).addTo(map);

    // Add enhanced popups to markers
    originMarker.bindPopup(`
        <div class="map-info-window">
            <div class="popup-header">
                <h3>Origin</h3>
                <div class="popup-badges">
                    <span class="marker-type origin-type">Origin Point</span>
                </div>
            </div>
            <div class="popup-content">
                <p><i class="fas fa-map-marker-alt"></i> <strong>Location:</strong> ${originName}</p>
            </div>
        </div>
    `, { className: 'enhanced-popup' });

    destinationMarker.bindPopup(`
        <div class="map-info-window">
            <div class="popup-header">
                <h3>Destination</h3>
                <div class="popup-badges">
                    <span class="marker-type destination-type">Destination Point</span>
                </div>
            </div>
            <div class="popup-content">
                <p><i class="fas fa-map-marker-alt"></i> <strong>Location:</strong> ${destinationName}</p>
            </div>
        </div>
    `, { className: 'enhanced-popup' });

    // Use Leaflet Routing Machine for better routing
    window.debugLog('Setting up routing control with waypoints', [
        [originCoords[0], originCoords[1]],
        [destinationCoords[0], destinationCoords[1]]
    ]);

    // Create waypoints
    const waypoints = [
        L.latLng(originCoords[0], originCoords[1]),
        L.latLng(destinationCoords[0], destinationCoords[1])
    ];

    // Helper function to calculate distance between coordinates
    function calculateRouteDistance(coordinates) {
        let distance = 0;
        for (let i = 0; i < coordinates.length - 1; i++) {
            const p1 = coordinates[i];
            const p2 = coordinates[i + 1];
            distance += p1.distanceTo(p2);
        }
        return distance;
    }

    // Helper function to calculate time based on distance (assuming 50 km/h average speed)
    function calculateRouteTime(coordinates) {
        const distance = calculateRouteDistance(coordinates);
        // Assume average speed of 50 km/h (13.89 m/s)
        return distance / 13.89;
    }

    // We'll use a direct approach with a simple line
    window.debugLog('Using direct route approach with simple line');
    createFallbackRoute();

    // Function to create a route using Leaflet Routing Machine
    function createFallbackRoute() {
        window.debugLog('Creating route with Leaflet Routing Machine');

        // Remove any existing paths with the same class
        map.eachLayer(function(layer) {
            if (layer._path && layer._path.classList) {
                if (layer._path.classList.contains('custom-ant-path') ||
                    layer._path.classList.contains('fallback-route')) {
                    map.removeLayer(layer);
                    window.debugLog('Removed existing path');
                }
            }
        });

        // Create waypoints for Leaflet Routing Machine
        const startPoint = L.latLng(originCoords[0], originCoords[1]);
        const endPoint = L.latLng(destinationCoords[0], destinationCoords[1]);
        const waypoints = [startPoint, endPoint];

        // Remove any existing routing control
        if (window.routingControl) {
            map.removeControl(window.routingControl);
            window.debugLog('Removed existing routing control');
        }

        // Create custom icons for markers - Simple dot style
        const createCustomIcon = function(dotClass) {
            return L.divIcon({
                className: 'custom-marker',
                html: `<div class="marker-dot ${dotClass}"></div>`,
                iconSize: [16, 16],
                iconAnchor: [8, 8],
                popupAnchor: [0, -10]
            });
        };

        // Create routing control with custom options
        window.routingControl = L.Routing.control({
            waypoints: waypoints,
            routeWhileDragging: false,
            showAlternatives: false,
            fitSelectedRoutes: true,
            show: false, // Don't show the routing interface
            lineOptions: {
                styles: [
                    {color: '#e83e8c', opacity: 0.8, weight: 5} // Pinkish red color
                ],
                addWaypoints: false,
                extendToWaypoints: true,
                missingRouteTolerance: 0
            },
            createMarker: function(i, waypoint, n) {
                const icons = [
                    createCustomIcon('origin-dot'),
                    createCustomIcon('destination-dot')
                ];

                const marker = L.marker(waypoint.latLng, {
                    icon: icons[i] || icons[0]
                });

                // Add enhanced popups to markers
                const popupContent = i === 0 ?
                    `<div class="map-info-window">
                        <div class="popup-header">
                            <h3>Origin</h3>
                            <div class="popup-badges">
                                <span class="marker-type origin-type">Origin Point</span>
                            </div>
                        </div>
                        <div class="popup-content">
                            <p><i class="fas fa-map-marker-alt"></i> <strong>Location:</strong> ${originName}</p>
                        </div>
                    </div>` :
                    `<div class="map-info-window">
                        <div class="popup-header">
                            <h3>Destination</h3>
                            <div class="popup-badges">
                                <span class="marker-type destination-type">Destination Point</span>
                            </div>
                        </div>
                        <div class="popup-content">
                            <p><i class="fas fa-map-marker-alt"></i> <strong>Location:</strong> ${destinationName}</p>
                        </div>
                    </div>`;

                marker.bindPopup(popupContent, { className: 'enhanced-popup' });
                return marker;
            }
        }).addTo(map);

        // Handle routing success
        window.routingControl.on('routesfound', function(e) {
            window.debugLog('Routes found event triggered', e.routes.length);

            // Remove loading indicator
            window.removeMapLoading();

            if (e.routes && e.routes.length > 0) {
                const route = e.routes[0];
                const routeCoordinates = route.coordinates;

                // Create AntPath for the route
                try {
                    window.debugLog('Creating AntPath with route coordinates');

                    // First, remove any existing AntPath
                    if (window.currentAntPath) {
                        try {
                            window.currentAntPath.remove();
                            window.debugLog('Removed existing AntPath');
                        } catch (err) {
                            window.debugLog('Error removing existing AntPath', err.message);
                        }
                    }

                    // Create a new AntPath with our custom implementation
                    window.currentAntPath = window.createCustomAntPath(map, routeCoordinates, {
                        color: '#e83e8c', // Pinkish red color
                        weight: 5,
                        opacity: 0.8,
                        pulseColor: '#ff6b9d', // Lighter pinkish red for pulse
                        dashArray: '10, 15',
                        className: 'custom-ant-path'
                    });

                    // Apply animation directly to the route path elements
                    setTimeout(() => {
                        try {
                            // Get all SVG elements in the document
                            const svgElements = document.querySelectorAll('svg');
                            window.debugLog('Found ' + svgElements.length + ' SVG elements');

                            // Loop through each SVG element
                            svgElements.forEach(svg => {
                                // Find all path elements within this SVG
                                const pathElements = svg.querySelectorAll('path');
                                window.debugLog('Found ' + pathElements.length + ' path elements in SVG');

                                // Apply animation to each path
                                pathElements.forEach(pathEl => {
                                    // Check if this is a route path (has the pinkish red color)
                                    const stroke = pathEl.getAttribute('stroke');
                                    if (stroke === '#e83e8c') {
                                        // Apply the animation directly to the path
                                        pathEl.classList.add('custom-ant-path');
                                        pathEl.style.strokeDasharray = '10, 15';
                                        pathEl.style.animation = 'antDash 30s linear infinite';
                                        window.debugLog('Applied animation to route path element');
                                    }
                                });
                            });

                            // Also try the direct approach
                            window.applyPathAnimations();
                        } catch (e) {
                            window.debugLog('Error applying animation to route path:', e.message);
                        }
                    }, 1000);

                    window.debugLog('Added custom AntPath for route');

                    // Apply animations to all paths
                    window.applyPathAnimations();
                } catch (e) {
                    window.debugLog('Error creating AntPath', e.message);
                }

                // Add route summary to the page
                if (route.summary) {
                    // Format distance and time
                    const distanceKm = (route.summary.totalDistance / 1000).toFixed(1);
                    const distanceMiles = (distanceKm * 0.621371).toFixed(1);

                    // Format time (hours and minutes)
                    const totalSeconds = route.summary.totalTime;
                    const hours = Math.floor(totalSeconds / 3600);
                    const minutes = Math.floor((totalSeconds % 3600) / 60);
                    const timeStr = hours > 0 ?
                        hours + ' hour' + (hours > 1 ? 's' : '') + ' ' + minutes + ' min' :
                        minutes + ' minutes';

                    // Add summary to the page
                    const summaryDiv = document.createElement('div');
                    summaryDiv.className = 'route-summary';
                    summaryDiv.innerHTML = `
                        <div class="route-summary-inner">
                            <h3>Route Summary</h3>
                            <div class="route-info">
                                <div class="route-info-item">
                                    <i class="fas fa-road"></i>
                                    <div>
                                        <span class="label">Distance</span>
                                        <span class="value">${distanceMiles} mi (${distanceKm} km)</span>
                                    </div>
                                </div>
                                <div class="route-info-item">
                                    <i class="fas fa-clock"></i>
                                    <div>
                                        <span class="label">Estimated Time</span>
                                        <span class="value">${timeStr}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add to the page
                    const mapContainer = document.getElementById('map-container');
                    if (mapContainer) {
                        // Remove any existing summary
                        const existingSummary = document.querySelector('.route-summary');
                        if (existingSummary) {
                            existingSummary.parentNode.removeChild(existingSummary);
                        }

                        mapContainer.appendChild(summaryDiv);
                    }
                }
            }
        });

        // Handle routing errors
        window.routingControl.on('routingerror', function(e) {
            window.debugLog('Routing error event triggered', e.error ? e.error.message : 'Unknown error');

            // Remove loading indicator
            window.removeMapLoading();

            // Fall back to a simple straight line
            const routePoints = [startPoint, endPoint];

            // Create a simple line
            const fallbackLine = L.polyline(routePoints, {
                color: '#e83e8c', // Pinkish red color
                weight: 4,
                opacity: 0.8,
                dashArray: '10, 10',
                lineJoin: 'round',
                className: 'fallback-route'
            }).addTo(map);

            // Create AntPath for the fallback route
            try {
                window.currentAntPath = window.createCustomAntPath(map, routePoints, {
                    color: '#e83e8c', // Pinkish red color
                    weight: 5,
                    opacity: 0.8,
                    pulseColor: '#ff6b9d', // Lighter pinkish red for pulse
                    dashArray: '10, 15',
                    className: 'custom-ant-path'
                });

                window.debugLog('Added custom AntPath for fallback route');
            } catch (e) {
                window.debugLog('Error creating AntPath for fallback', e.message);
            }

            // Fit map to show both markers
            const bounds = L.latLngBounds(routePoints);
            map.fitBounds(bounds, {
                padding: [50, 50]
            });
        });

        return window.routingControl;
    }

    window.debugLog('Routing control added to map');

    // Function to simulate coordinates based on location name (for demo purposes)
    function simulateCoordinates(locationName) {
        // This is a simplified version for demo purposes
        // In a real application, you would use geocoding

        // Common locations with approximate coordinates
        const knownLocations = {
            'new york': [40.7128, -74.0060],
            'london': [51.5074, -0.1278],
            'paris': [48.8566, 2.3522],
            'tokyo': [35.6762, 139.6503],
            'sydney': [-33.8688, 151.2093],
            'beijing': [39.9042, 116.4074],
            'moscow': [55.7558, 37.6173],
            'dubai': [25.2048, 55.2708],
            'los angeles': [34.0522, -118.2437],
            'chicago': [41.8781, -87.6298],
            'toronto': [43.6532, -79.3832],
            'mexico city': [19.4326, -99.1332],
            'sao paulo': [-23.5505, -46.6333],
            'cairo': [30.0444, 31.2357],
            'johannesburg': [-26.2041, 28.0473],
            'mumbai': [19.0760, 72.8777],
            'singapore': [1.3521, 103.8198],
            'hong kong': [22.3193, 114.1694],
            'berlin': [52.5200, 13.4050],
            'rome': [41.9028, 12.4964],
            'madrid': [40.4168, -3.7038],
            'amsterdam': [52.3676, 4.9041],
            'bangkok': [13.7563, 100.5018],
            'seoul': [37.5665, 126.9780],
            'shanghai': [31.2304, 121.4737],
            'lagos': [6.5244, 3.3792],
            'nairobi': [-1.2921, 36.8219],
            'rio de janeiro': [-22.9068, -43.1729],
            'buenos aires': [-34.6037, -58.3816],
            'lima': [-12.0464, -77.0428],
            'vancouver': [49.2827, -123.1207],
            'montreal': [45.5017, -73.5673],
            'san francisco': [37.7749, -122.4194],
            'miami': [25.7617, -80.1918],
            'seattle': [47.6062, -122.3321],
            'dallas': [32.7767, -96.7970],
            'houston': [29.7604, -95.3698],
            'atlanta': [33.7490, -84.3880],
            'boston': [42.3601, -71.0589],
            'washington': [38.9072, -77.0369],
            'philadelphia': [39.9526, -75.1652],
            'phoenix': [33.4484, -112.0740],
            'denver': [39.7392, -104.9903],
            'las vegas': [36.1699, -115.1398],
            'austin': [30.2672, -97.7431],
            'nashville': [36.1627, -86.7816],
            'new orleans': [29.9511, -90.0715],
            'san diego': [32.7157, -117.1611],
            'portland': [45.5051, -122.6750],
            'minneapolis': [44.9778, -93.2650],
            'st louis': [38.6270, -90.1994],
            'kansas city': [39.0997, -94.5786],
            'pittsburgh': [40.4406, -79.9959],
            'cleveland': [41.4993, -81.6944],
            'cincinnati': [39.1031, -84.5120],
            'indianapolis': [39.7684, -86.1581],
            'columbus': [39.9612, -82.9988],
            'detroit': [42.3314, -83.0458],
            'milwaukee': [43.0389, -87.9065],
            'baltimore': [39.2904, -76.6122],
            'charlotte': [35.2271, -80.8431],
            'raleigh': [35.7796, -78.6382],
            'memphis': [35.1495, -90.0490],
            'louisville': [38.2527, -85.7585],
            'richmond': [37.5407, -77.4360],
            'oklahoma city': [35.4676, -97.5164],
            'salt lake city': [40.7608, -111.8910],
            'orlando': [28.5383, -81.3792],
            'tampa': [27.9506, -82.4572],
            'jacksonville': [30.3322, -81.6557],
            'honolulu': [21.3069, -157.8583],
            'anchorage': [61.2181, -149.9003],
            'cameroon': [7.3697, 12.3547],
            'china': [35.8617, 104.1954],
            'usa': [37.0902, -95.7129],
            'canada': [56.1304, -106.3468],
            'brazil': [-14.2350, -51.9253],
            'australia': [-25.2744, 133.7751],
            'india': [20.5937, 78.9629],
            'russia': [61.5240, 105.3188],
            'japan': [36.2048, 138.2529],
            'germany': [51.1657, 10.4515],
            'uk': [55.3781, -3.4360],
            'france': [46.2276, 2.2137],
            'italy': [41.8719, 12.5674],
            'spain': [40.4637, -3.7492],
            'mexico': [23.6345, -102.5528],
            'south africa': [-30.5595, 22.9375],
            'egypt': [26.8206, 30.8025],
            'nigeria': [9.0820, 8.6753],
            'kenya': [0.0236, 37.9062],
            'argentina': [-38.4161, -63.6167],
            'peru': [-9.1900, -75.0152],
            'colombia': [4.5709, -74.2973],
            'venezuela': [6.4238, -66.5897],
            'chile': [-35.6751, -71.5430],
            'sweden': [60.1282, 18.6435],
            'norway': [60.4720, 8.4689],
            'finland': [61.9241, 25.7482],
            'denmark': [56.2639, 9.5018],
            'poland': [51.9194, 19.1451],
            'ukraine': [48.3794, 31.1656],
            'greece': [39.0742, 21.8243],
            'turkey': [38.9637, 35.2433],
            'saudi arabia': [23.8859, 45.0792],
            'iran': [32.4279, 53.6880],
            'iraq': [33.2232, 43.6793],
            'pakistan': [30.3753, 69.3451],
            'afghanistan': [33.9391, 67.7100],
            'thailand': [15.8700, 100.9925],
            'vietnam': [14.0583, 108.2772],
            'philippines': [12.8797, 121.7740],
            'indonesia': [-0.7893, 113.9213],
            'malaysia': [4.2105, 101.9758],
            'new zealand': [-40.9006, 174.8860],
            // Add common country abbreviations
            'us': [37.0902, -95.7129],
            'uk': [55.3781, -3.4360],
            'uae': [23.4241, 53.8478],
            'usa': [37.0902, -95.7129],
            'united states': [37.0902, -95.7129],
            'united kingdom': [55.3781, -3.4360],
            'united arab emirates': [23.4241, 53.8478],
            // Add common city abbreviations
            'la': [34.0522, -118.2437],
            'nyc': [40.7128, -74.0060],
            'sf': [37.7749, -122.4194],
            'dc': [38.9072, -77.0369],
            // Add common state abbreviations
            'ca': [36.7783, -119.4179], // California
            'ny': [40.7128, -74.0060], // New York
            'tx': [31.9686, -99.9018], // Texas
            'fl': [27.6648, -81.5158], // Florida
            'il': [41.8781, -87.6298], // Illinois
            'pa': [41.2033, -77.1945], // Pennsylvania
            'oh': [40.4173, -82.9071], // Ohio
            'mi': [44.3148, -85.6024], // Michigan
            'ga': [33.0406, -83.6431], // Georgia
            'nc': [35.7596, -79.0193], // North Carolina
            'nj': [40.0583, -74.4057], // New Jersey
            'va': [37.7693, -78.1700], // Virginia
            'wa': [47.7511, -120.7401], // Washington
            'ma': [42.4072, -71.3824], // Massachusetts
            'az': [34.0489, -111.0937], // Arizona
            'in': [40.2672, -86.1349], // Indiana
            'tn': [35.5175, -86.5804], // Tennessee
            'mo': [37.9643, -91.8318], // Missouri
            'md': [39.0458, -76.6413], // Maryland
            'wi': [43.7844, -88.7879], // Wisconsin
            'mn': [46.7296, -94.6859], // Minnesota
            'co': [39.5501, -105.7821], // Colorado
            'al': [32.3182, -86.9023], // Alabama
            'sc': [33.8361, -81.1637], // South Carolina
            'la': [31.1695, -91.8678], // Louisiana
            'ky': [37.8393, -84.2700], // Kentucky
            'or': [43.8041, -120.5542], // Oregon
            'ok': [35.0078, -97.0929], // Oklahoma
            'ct': [41.6032, -73.0877], // Connecticut
            'ia': [41.8780, -93.0977], // Iowa
            'ms': [32.3547, -89.3985], // Mississippi
            'ar': [35.2010, -91.8318], // Arkansas
            'ks': [39.0119, -98.4842], // Kansas
            'ut': [39.3210, -111.0937], // Utah
            'nv': [38.8026, -116.4194], // Nevada
            'nm': [34.5199, -105.8701], // New Mexico
            'ne': [41.4925, -99.9018], // Nebraska
            'wv': [38.5976, -80.4549], // West Virginia
            'id': [44.0682, -114.7420], // Idaho
            'hi': [19.8968, -155.5828], // Hawaii
            'me': [44.6939, -69.3819], // Maine
            'nh': [43.1939, -71.5724], // New Hampshire
            'ri': [41.5801, -71.4774], // Rhode Island
            'mt': [46.8797, -110.3626], // Montana
            'de': [38.9108, -75.5277], // Delaware
            'sd': [43.9695, -99.9018], // South Dakota
            'nd': [47.5515, -101.0020], // North Dakota
            'ak': [64.2008, -149.4937], // Alaska
            'vt': [44.5588, -72.5778], // Vermont
            'wy': [43.0760, -107.2903] // Wyoming
        };

        // Convert location name to lowercase for matching
        const lowerName = locationName.toLowerCase();

        // Check if we have coordinates for this location
        if (knownLocations[lowerName]) {
            window.debugLog('Found known coordinates for ' + locationName, knownLocations[lowerName]);
            return knownLocations[lowerName];
        }

        // Try to match parts of the location name
        for (const key in knownLocations) {
            // Check if the location name contains a known location
            if (lowerName.includes(key)) {
                window.debugLog('Found partial match for ' + locationName + ' with ' + key, knownLocations[key]);
                return knownLocations[key];
            }
            // Check if a known location contains the location name
            if (key.includes(lowerName)) {
                window.debugLog('Found containing match for ' + locationName + ' with ' + key, knownLocations[key]);
                return knownLocations[key];
            }
        }

        // If not found, generate random but plausible coordinates
        window.debugLog('Generating simulated coordinates for ' + locationName);

        // Generate a hash from the location name
        let hash = 0;
        for (let i = 0; i < locationName.length; i++) {
            hash = ((hash << 5) - hash) + locationName.charCodeAt(i);
            hash = hash & hash; // Convert to 32bit integer
        }

        // Use the hash to generate latitude and longitude
        const lat = (Math.abs(hash % 180) - 90) * 0.8; // -72 to 72 degrees
        const lng = (Math.abs((hash >> 8) % 360) - 180) * 0.8; // -144 to 144 degrees

        return [lat, lng];
    }

    // Inject animation styles directly to target SVG paths
    const styleElement = document.createElement('style');
    styleElement.textContent = `
        /* Direct selector for SVG paths with the pinkish red color */
        path[stroke="#e83e8c"] {
            stroke-dasharray: 10, 15 !important;
            animation: antDash 30s linear infinite !important;
        }

        /* Target all paths with inline styles containing the color */
        path[style*="#e83e8c"] {
            stroke-dasharray: 10, 15 !important;
            animation: antDash 30s linear infinite !important;
        }
    `;
    document.head.appendChild(styleElement);
    window.debugLog('Injected direct animation styles for SVG paths');
});
</script>

<!-- Add CSS for route results and animations -->
<style>
/* Ant path animation */
@keyframes antDash {
    to {
        stroke-dashoffset: -1000;
    }
}

.custom-ant-path {
    stroke-dasharray: 10, 15 !important;
    animation: antDash 30s linear infinite !important;
}

.pulse-path {
    animation: pulsePath 2s ease-in-out infinite;
}

@keyframes pulsePath {
    0% { opacity: 0.1; }
    50% { opacity: 0.3; }
    100% { opacity: 0.1; }
}
/* Custom map markers - Simple dot style */
.custom-marker {
    background: none;
    border: none;
}

.marker-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    position: absolute;
    left: 50%;
    top: 50%;
    margin: -8px 0 0 -8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    border: 2px solid white;
}

/* Marker colors for different types */
.origin-dot {
    background: #28a745; /* Green for origin */
}

.destination-dot {
    background: #dc3545; /* Red for destination */
}

.current-dot {
    background: #007bff; /* Blue for current location */
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    /* Pulsing animation for current location */
    animation: pulse 2s infinite;
}

.transit-dot {
    background: #5c2be2; /* Purple for transit points */
}

.delayed-dot {
    background: #fd7e14; /* Orange for delayed points */
}

/* Pulsing animation for current location dot */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

/* Old marker pin styles removed - using dot style now */

.map-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    padding: 10px 20px;
    border-radius: 20px;
    z-index: 9999; /* Ensure it's above everything */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    font-weight: 500;
    pointer-events: none; /* Allow clicking through */
    display: flex;
    align-items: center;
    justify-content: center;
}

.map-loading i {
    margin-right: 8px;
    color: #e83e8c; /* Match the path color */
    font-size: 16px;
}

/* Map info window styles moved to the main section below */

/* Ant path animation - defined in JavaScript */

/* Debug info */
.debug-info {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #fff;
    padding: 10px;
    border-radius: 5px;
    z-index: 1000;
    max-width: 500px;
    max-height: 300px;
    overflow: auto;
    font-family: monospace;
    font-size: 12px;
}

.debug-info h3 {
    margin-top: 0;
    color: #00d45f;
}

.debug-info pre {
    margin: 0;
    white-space: pre-wrap;
}

/* Custom markers */
.custom-marker {
    background: transparent;
    border: none;
}

/* Old marker pin styles removed - using dot style now */

/* Map info windows */
.map-info-window {
    padding: 10px;
    font-family: var(--body-font);
}

.map-info-window h3 {
    margin-bottom: 10px;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.map-info-window p {
    margin-bottom: 6px;
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-color);
}

/* Enhanced popup styling */
.enhanced-popup .leaflet-popup-content-wrapper {
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.5);
    padding: 0;
    overflow: hidden;
}

.dark-theme .enhanced-popup .leaflet-popup-content-wrapper {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.3);
}

.enhanced-popup .leaflet-popup-content {
    margin: 0;
    width: 280px !important;
}

.enhanced-popup .leaflet-popup-tip {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.dark-theme .enhanced-popup .leaflet-popup-tip {
    background: rgba(30, 30, 30, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.3);
}

/* Popup header */
.popup-header {
    background: rgba(92, 43, 226, 0.1);
    padding: 10px;
    border-bottom: 1px solid rgba(92, 43, 226, 0.2);
}

.dark-theme .popup-header {
    background: rgba(92, 43, 226, 0.2);
    border-bottom: 1px solid rgba(92, 43, 226, 0.3);
}

/* Popup content */
.popup-content {
    padding: 10px;
}

/* Popup badges */
.popup-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.marker-type, .transport-mode {
    display: inline-flex;
    align-items: center;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-theme .marker-type, .dark-theme .transport-mode {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.marker-type.origin-type {
    color: #28a745;
}

.marker-type.destination-type {
    color: #dc3545;
}

.marker-type.current-type {
    color: #007bff;
}

.marker-type.transit-type {
    color: #5c2be2;
}

.route-search-section {
    padding: 40px 0;
    background-color: var(--bg-secondary);
}

.route-search-box {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.route-results {
    padding: 40px 0;
    background-color: var(--bg-color);
}

.route-overview,
.available-services,
.route-map-container,
.route-cta {
    margin-bottom: 40px;
}

.route-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.route-detail-card {
    background-color: var(--glass-bg);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.detail-icon {
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.detail-content h3 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    color: var(--text-secondary);
}

.detail-content p {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-color);
}

.services-table-container {
    overflow-x: auto;
    margin-top: 20px;
}

.services-table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--glass-bg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--glass-shadow);
}

.services-table th,
.services-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.services-table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
}

.services-table tr:last-child td {
    border-bottom: none;
}

.services-table tr:hover td {
    background-color: rgba(92, 43, 226, 0.05);
}

.small-btn {
    padding: 8px 12px;
    font-size: 0.9rem;
}

#route-map {
    height: 400px;
    border-radius: 12px;
    overflow: hidden;
    margin-top: 20px;
    box-shadow: var(--box-shadow);
    position: relative;
    z-index: 1;
    background-color: #f8f9fa;
}

.map-disclaimer {
    margin-top: 10px;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-style: italic;
}

.route-cta {
    text-align: center;
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 40px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.route-cta h2 {
    margin-top: 0;
}

.route-cta .btn {
    margin-top: 20px;
}

@media (max-width: 768px) {
    .route-detail-card {
        flex-direction: column;
        text-align: center;
    }

    .detail-icon {
        margin-bottom: 10px;
    }

    #route-map {
        height: 300px;
    }
}
</style>
<?php endif; ?>

<?php
// Include footer
include_once 'includes/footer.php';
?>
