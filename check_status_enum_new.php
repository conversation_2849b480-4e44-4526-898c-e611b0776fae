<?php
/**
 * Check Status ENUM Values
 *
 * This script checks the status ENUM values in the shipments table.
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Check Database Status';

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-database"></i> Database Status Check</h1>
            <div class="admin-actions">
                <a href="update_status_enum_fixed.php" class="btn primary-btn"><i class="fas fa-wrench"></i> Update Database Schema</a>
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Status ENUM Values</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $db->query("SHOW COLUMNS FROM shipments LIKE 'status'");
                        $statusColumn = $db->single();

                        if ($statusColumn) {
                            $currentEnum = $statusColumn['Type'];
                            echo "<p>Current status ENUM: <code>{$currentEnum}</code></p>";

                            // Extract values from ENUM
                            preg_match("/^enum\(\'(.*)\'\)$/", $currentEnum, $matches);
                            if (isset($matches[1])) {
                                $values = explode("','", $matches[1]);

                                echo "<h3>Available Status Values:</h3>";
                                echo "<div class='table-responsive'>";
                                echo "<table class='table'>";
                                echo "<thead><tr><th>Status Value</th><th>Display Name</th></tr></thead>";
                                echo "<tbody>";
                                foreach ($values as $value) {
                                    $displayName = ucfirst(str_replace('_', ' ', $value));
                                    echo "<tr>";
                                    echo "<td><code>{$value}</code></td>";
                                    echo "<td>{$displayName}</td>";
                                    echo "</tr>";
                                }
                                echo "</tbody></table></div>";

                                // Check if new values exist
                                $newValues = ['picked_up', 'arrived_at_facility', 'out_for_delivery'];
                                $missingValues = array_diff($newValues, $values);

                                if (empty($missingValues)) {
                                    echo "<div class='alert alert-success'>All required status values exist in the database.</div>";
                                } else {
                                    echo "<div class='alert alert-danger'>Missing status values: <strong>" . implode(", ", $missingValues) . "</strong></div>";
                                    echo "<p>Please run the <a href='update_status_enum_fixed.php' class='btn primary-btn'><i class='fas fa-wrench'></i> Update Database Schema</a> script to add these values.</p>";
                                }
                            } else {
                                echo "<div class='alert alert-danger'>Could not parse ENUM values.</div>";
                            }
                        } else {
                            echo "<div class='alert alert-danger'>Could not find status column in shipments table.</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2>Test Adding Shipments with Different Statuses</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $db->query("SHOW COLUMNS FROM shipments LIKE 'status'");
                        $statusColumn = $db->single();

                        if ($statusColumn) {
                            $currentEnum = $statusColumn['Type'];

                            // Extract values from ENUM
                            preg_match("/^enum\(\'(.*)\'\)$/", $currentEnum, $matches);
                            if (isset($matches[1])) {
                                $values = explode("','", $matches[1]);

                                echo "<div class='table-responsive'>";
                                echo "<table class='table'>";
                                echo "<thead><tr><th>Status</th><th>Test Result</th></tr></thead>";
                                echo "<tbody>";

                                foreach ($values as $status) {
                                    echo "<tr>";
                                    echo "<td><span class='badge " . getStatusClass($status) . "'>" . ucfirst(str_replace('_', ' ', $status)) . "</span></td>";

                                    try {
                                        // Generate a unique tracking number
                                        $trackingNumber = 'TEST' . time() . rand(1000, 9999);

                                        // Try to insert a test shipment with this status
                                        $db->query("INSERT INTO shipments (tracking_number, customer_name, origin, destination, status, estimated_delivery)
                                                   VALUES (:tracking, 'Test Customer', 'Test Origin', 'Test Destination', :status, DATE_ADD(NOW(), INTERVAL 7 DAY))");
                                        $db->bind(':tracking', $trackingNumber);
                                        $db->bind(':status', $status);
                                        $result = $db->execute();

                                        if ($result) {
                                            echo "<td><span class='text-success'><i class='fas fa-check-circle'></i> Success</span></td>";

                                            // Delete the test shipment
                                            $db->query("DELETE FROM shipments WHERE tracking_number = :tracking");
                                            $db->bind(':tracking', $trackingNumber);
                                            $db->execute();
                                        } else {
                                            echo "<td><span class='text-danger'><i class='fas fa-times-circle'></i> Failed</span></td>";
                                        }
                                    } catch (Exception $e) {
                                        echo "<td><span class='text-danger'><i class='fas fa-exclamation-triangle'></i> Error: " . $e->getMessage() . "</span></td>";
                                    }

                                    echo "</tr>";
                                }

                                echo "</tbody></table></div>";
                            }
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error testing shipment addition: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h2>Geocoding Cache Table</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        $db->query("SHOW TABLES LIKE 'geocoding_cache'");
                        $tableExists = $db->rowCount() > 0;

                        if ($tableExists) {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Geocoding cache table exists.</div>";

                            // Show table structure
                            $db->query("DESCRIBE geocoding_cache");
                            $columns = $db->resultSet();

                            echo "<h3>Table Structure</h3>";
                            echo "<div class='table-responsive'>";
                            echo "<table class='table'>";
                            echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                            echo "<tbody>";

                            foreach ($columns as $column) {
                                echo "<tr>";
                                echo "<td>{$column['Field']}</td>";
                                echo "<td>{$column['Type']}</td>";
                                echo "<td>{$column['Null']}</td>";
                                echo "<td>{$column['Key']}</td>";
                                echo "<td>" . (is_null($column['Default']) ? 'NULL' : $column['Default']) . "</td>";
                                echo "<td>{$column['Extra']}</td>";
                                echo "</tr>";
                            }

                            echo "</tbody></table></div>";

                            // Show cache count
                            $db->query("SELECT COUNT(*) as total FROM geocoding_cache");
                            $totalCache = $db->single();
                            echo "<p>Total Cached Geocoding Results: <strong>" . ($totalCache['total'] ?? 0) . "</strong></p>";
                        } else {
                            echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Geocoding cache table does not exist. Please run the <a href='update_status_enum.php' class='btn primary-btn'><i class='fas fa-wrench'></i> Update Database Schema</a> script to create this table.</div>";
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'>Error checking geocoding_cache table: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>

        <div class="admin-actions mt-4">
            <a href="update_status_enum.php" class="btn primary-btn"><i class="fas fa-wrench"></i> Update Database Schema</a>
            <a href="admin/index.php" class="btn secondary-btn"><i class="fas fa-arrow-left"></i> Return to Dashboard</a>
        </div>
    </div>
</section>

<style>
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.text-success {
    color: #28a745;
}

.text-danger {
    color: #dc3545;
}

code {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.mt-4 {
    margin-top: 20px;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 12px;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    background-color: rgba(92, 43, 226, 0.05);
    color: var(--primary-color);
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(92, 43, 226, 0.03);
}
</style>

<?php include_once 'includes/footer.php'; ?>
