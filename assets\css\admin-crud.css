/* Admin CRUD Styles */

/* Admin Section */
.admin-section {
    padding: 120px 0 50px; /* Desktop padding */
    margin-bottom: 30px;
}

@media (max-width: 768px) {
    .admin-section {
        padding: 40px 0 50px; /* Reduced padding on mobile - no header */
    }
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.admin-header h1 {
    display: flex;
    align-items: center;
    font-size: 1.8rem;
    color: var(--primary-color);
    margin: 0;
}

.admin-header h1 i {
    margin-right: 10px;
}

.admin-actions {
    display: flex;
    gap: 10px;
}

.admin-content {
    margin-bottom: 50px; /* Increased margin between content sections */
}

/* Card Styles */
.card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    overflow: hidden;
    margin-bottom: 40px; /* Increased margin between cards */
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.card-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--text-color);
}

.card-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.card-body {
    padding: 20px;
}

/* Table Styles */
.table-responsive {
    overflow-x: auto;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.table th {
    font-weight: 600;
    color: var(--text-color);
    background-color: rgba(0, 0, 0, 0.03);
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.table td.actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    min-width: 140px; /* Ensure enough space for buttons */
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: capitalize;
}

.badge.admin {
    background-color: var(--primary-color);
    color: white;
}

.badge.staff {
    background-color: #17a2b8;
    color: white;
}

.badge.customer {
    background-color: #6c757d;
    color: white;
}

.badge.status-pending {
    background-color: #ffc107;
    color: #212529;
}

.badge.status-transit {
    background-color: #007bff;
    color: white;
}

.badge.status-delivered {
    background-color: #28a745;
    color: white;
}

.badge.status-delayed {
    background-color: #ff9800;
    color: white;
}

.badge.status-cancelled {
    background-color: #dc3545;
    color: white;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    text-decoration: none;
}

.btn i {
    margin-right: 8px;
}

.add-btn {
    background-color: var(--primary-color);
    color: white;
}

.add-btn:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
}

.back-btn {
    background-color: var(--text-secondary);
    color: white;
}

.back-btn:hover {
    background-color: var(--text-color);
    transform: translateY(-2px);
}

.btn-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: transparent;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--text-secondary);
}

.btn-icon:hover {
    background-color: rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
}

.edit-btn:hover {
    color: #17a2b8;
}

.delete-btn:hover {
    color: #dc3545;
}

.view-btn:hover {
    color: var(--primary-color);
}

.update-btn:hover {
    color: #28a745;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--text-color);
    font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(92, 43, 226, 0.2);
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-group.half {
    flex: 1;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.cancel-btn {
    background-color: var(--text-secondary);
    color: white;
}

.submit-btn {
    background-color: var(--primary-color);
    color: white;
}

.delete-btn {
    background-color: #dc3545;
    color: white;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
    padding: 120px 20px 50px; /* Further increased top padding to prevent header overlap */
}

.modal-content {
    background-color: var(--glass-bg);
    border-radius: 16px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    max-width: 600px;
    margin: 20px auto 50px; /* Adjusted margin for better positioning */
    overflow: hidden;
}

.modal-lg {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    margin: 0;
    font-size: 1.3rem;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: 20px;
}

/* Search and Filter Styles */
.search-box {
    position: relative;
}

.search-box input {
    padding: 8px 12px;
    padding-right: 40px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--text-color);
    width: 250px;
}

.search-box button,
.search-box i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
}

.filter-form {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-form .form-group {
    margin-bottom: 0;
}

.filter-form select {
    padding: 8px 12px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    background-color: var(--input-bg);
    color: var(--text-color);
}

/* Pagination Styles */
.pagination {
    display: flex;
    justify-content: center;
    gap: 5px;
    margin-top: 20px;
}

.page-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 8px;
    background-color: var(--glass-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.page-link.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Empty State Styles */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 15px;
    opacity: 0.5;
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

/* Shipment Details Styles */
.shipment-details {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.details-section {
    margin-bottom: 20px;
}

.details-section h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
    color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.detail-item {
    margin-bottom: 10px;
}

.detail-label {
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.detail-value {
    color: var(--text-color);
    font-weight: 500;
}

/* Tracking Timeline Styles */
.tracking-timeline {
    position: relative;
    padding-left: 30px;
}

.tracking-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 10px;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:last-child {
    margin-bottom: 0;
}

.timeline-marker {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color);
    border: 3px solid var(--glass-bg);
    z-index: 1;
}

.timeline-marker.status-pending {
    background-color: #ffc107;
}

.timeline-marker.status-transit {
    background-color: #007bff;
}

.timeline-marker.status-delivered {
    background-color: #28a745;
}

.timeline-marker.status-delayed {
    background-color: #ff9800;
}

.timeline-marker.status-cancelled {
    background-color: #dc3545;
}

.timeline-content {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 15px;
    border: 1px solid var(--border-color);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.timeline-title {
    font-weight: 600;
    color: var(--text-color);
}

.timeline-time {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.timeline-status {
    margin-bottom: 10px;
}

.timeline-notes {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px dashed var(--border-color);
    color: var(--text-color);
    font-size: 0.95rem;
}

.timeline-location {
    margin-top: 10px;
    font-size: 0.9rem;
}

.timeline-location a {
    color: var(--primary-color);
    text-decoration: none;
}

.timeline-location a:hover {
    text-decoration: underline;
}

/* Loading Styles */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--text-secondary);
}

.loading i {
    margin-right: 10px;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(2px);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    z-index: 10;
}

/* Error Styles */
.error {
    padding: 15px;
    background-color: rgba(220, 53, 69, 0.1);
    border-radius: 8px;
    color: #dc3545;
    margin-bottom: 15px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    /* Adjust padding for mobile */
    .admin-section {
        padding-top: 20px; /* Reduced since body has padding */
    }

    .modal {
        padding-top: 20px; /* Reduced since body has padding */
    }

    .admin-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        margin-bottom: 25px; /* Increased bottom margin */
    }

    .admin-actions {
        width: 100%;
    }

    .card-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .card-actions {
        width: 100%;
    }

    .filter-form {
        flex-direction: column;
        align-items: flex-start;
        width: 100%;
    }

    .search-box {
        width: 100%;
    }

    .search-box input {
        width: 100%;
    }

    .form-row {
        flex-direction: column;
    }

    .details-grid {
        grid-template-columns: 1fr;
    }
}
