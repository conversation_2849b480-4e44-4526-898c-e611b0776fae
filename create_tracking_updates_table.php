<?php
require_once 'includes/config.php';
require_once 'includes/db.php';

// Check if tracking_updates table exists
$db->query("SHOW TABLES LIKE 'tracking_updates'");
$tableExists = $db->single();

if (!$tableExists) {
    // Create tracking_updates table
    $db->query("CREATE TABLE tracking_updates (
        id INT AUTO_INCREMENT PRIMARY KEY,
        shipment_id INT NOT NULL,
        location VARCHAR(255) NOT NULL,
        status VARCHAR(50) NOT NULL,
        latitude DECIMAL(10, 8) NULL,
        longitude DECIMAL(11, 8) NULL,
        notes TEXT NULL,
        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (shipment_id) REFERENCES shipments(id) ON DELETE CASCADE
    )");
    
    if ($db->execute()) {
        echo "<h2>Tracking Updates Table Created Successfully</h2>";
        echo "<p>The tracking_updates table has been created in the database.</p>";
    } else {
        echo "<h2>Error Creating Table</h2>";
        echo "<p>There was an error creating the tracking_updates table.</p>";
    }
} else {
    echo "<h2>Table Already Exists</h2>";
    echo "<p>The tracking_updates table already exists in the database.</p>";
}

echo "<p><a href='admin/index.php'>Go to Admin Dashboard</a></p>";
?>
