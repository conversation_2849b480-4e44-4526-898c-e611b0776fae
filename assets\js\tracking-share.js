/**
 * Tracking Share Functionality
 * Allows users to share tracking links via Web Share API or copy to clipboard
 */
document.addEventListener('DOMContentLoaded', function() {
    // Get the share button
    const shareButton = document.getElementById('share-tracking-btn');

    if (!shareButton) {
        console.log('Share button not found');
        return;
    }

    // Add click event listener
    shareButton.addEventListener('click', function() {
        // Get the tracking number from the URL
        const trackingNumber = new URLSearchParams(window.location.search).get('tracking_number');

        // If no tracking number, use the full current URL
        if (!trackingNumber) {
            shareCurrentUrl();
            return;
        }

        // Create a clean tracking URL that will work even if domain changes or on different hosting servers
        // Get the base path from the current URL (handles subdirectories in hosting)
        const basePath = getBasePath();
        const trackingPath = basePath + '/tracking/index.php?tracking_number=' + encodeURIComponent(trackingNumber);

        // For sharing, we need the full URL, so construct it from current location
        const trackingLink = window.location.origin + trackingPath;

        // Get site name if available, otherwise use a default
        const siteName = document.querySelector('footer h3')?.textContent || 'Track My Shipment';

        // Default share title and text
        const shareTitle = `${siteName} - Track Shipment`;
        const shareText = `Track my shipment with tracking number: ${trackingNumber}`;

        // Check if Web Share API is supported
        if (navigator.share) {
            navigator.share({
                title: shareTitle,
                text: shareText,
                url: trackingLink
            })
            .then(() => {
                console.log('Tracking link shared successfully');
                showSuccess('Tracking link shared successfully');
            })
            .catch(error => {
                console.error('Error sharing tracking link:', error);
                // Fall back to copy to clipboard if sharing fails
                copyToClipboard(trackingLink);
            });
        } else {
            // Web Share API not supported, fall back to copy to clipboard
            copyToClipboard(trackingLink);
        }
    });

    /**
     * Copy text to clipboard
     * @param {string} text - Text to copy
     */
    function copyToClipboard(text) {
        // Create a temporary input element
        const tempInput = document.createElement('input');
        tempInput.value = text;
        document.body.appendChild(tempInput);

        // Select and copy the text
        tempInput.select();
        tempInput.setSelectionRange(0, 99999); // For mobile devices

        try {
            // Execute copy command
            const successful = document.execCommand('copy');

            // Show success or error notification
            if (successful) {
                console.log('Tracking link copied to clipboard');
                showSuccess('Tracking link copied to clipboard');
            } else {
                console.error('Failed to copy tracking link');
                showError('Failed to copy tracking link');
            }
        } catch (err) {
            console.error('Error copying tracking link:', err);
            showError('Error copying tracking link');
        }

        // Remove the temporary input
        document.body.removeChild(tempInput);
    }

    /**
     * Show success notification
     * @param {string} message - Success message
     */
    function showSuccess(message) {
        if (typeof window.showNotification === 'function') {
            window.showNotification(message, 'success', { duration: 3000 });
        } else {
            alert(message);
        }
    }

    /**
     * Show error notification
     * @param {string} message - Error message
     */
    function showError(message) {
        if (typeof window.showNotification === 'function') {
            window.showNotification(message, 'error', { duration: 5000 });
        } else {
            alert(message);
        }
    }

    /**
     * Get the base path of the application
     * This handles cases where the site is in a subdirectory on the hosting server
     * @returns {string} The base path (e.g., '', '/mysite', '/subdirectory/mysite')
     */
    function getBasePath() {
        // Try to get the base path from a meta tag if available
        const basePathMeta = document.querySelector('meta[name="base-path"]');
        if (basePathMeta && basePathMeta.content) {
            return basePathMeta.content.replace(/\/$/, ''); // Remove trailing slash if present
        }

        // Try to extract from script tags (looking for assets/js paths)
        const scriptTags = document.querySelectorAll('script[src*="assets/js"]');
        if (scriptTags.length > 0) {
            const scriptSrc = scriptTags[0].getAttribute('src');
            const match = scriptSrc.match(/^(.*?)\/assets\/js/);
            if (match && match[1]) {
                return match[1];
            }
        }

        // Try to extract from the current path
        const pathParts = window.location.pathname.split('/');
        if (pathParts.includes('tracking')) {
            // Find the index of 'tracking' and get everything before it
            const trackingIndex = pathParts.indexOf('tracking');
            if (trackingIndex > 0) {
                return pathParts.slice(0, trackingIndex).join('/');
            }
        }

        // Default to empty string (root of the domain)
        return '';
    }

    /**
     * Share the current URL as is
     * Used as a fallback when no tracking number is found
     */
    function shareCurrentUrl() {
        // Use the current URL, but ensure it's properly formatted with the base path
        // This helps when the site is in a subdirectory on a hosting server
        const currentUrl = window.location.href;
        const siteName = document.querySelector('footer h3')?.textContent || 'Track My Shipment';

        const shareTitle = `${siteName} - Track Shipment`;
        const shareText = 'Track my shipment';

        if (navigator.share) {
            navigator.share({
                title: shareTitle,
                text: shareText,
                url: currentUrl
            })
            .then(() => {
                console.log('URL shared successfully');
                showSuccess('Link shared successfully');
            })
            .catch(error => {
                console.error('Error sharing URL:', error);
                copyToClipboard(currentUrl);
            });
        } else {
            copyToClipboard(currentUrl);
        }
    }
});
