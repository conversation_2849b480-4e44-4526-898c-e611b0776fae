# TransLogix Tracking System

A PHP-based tracking system for a transport and logistics company, featuring a CMS for managing shipments and a public tracking interface with map visualization.

## Features

- **Public Website**
  - Responsive landing page with modern design
  - Shipment tracking with interactive map
  - Detailed tracking timeline
  - Light and dark theme support

- **Admin CMS**
  - Secure login system
  - Dashboard with shipment statistics
  - Shipment management (add, edit, delete)
  - Tracking updates management
  - User management
  - System settings customization
  - Alert system for important announcements

## Installation

1. **System Requirements**
   - PHP 7.4 or higher
   - MySQL 5.7 or higher
   - Apache/Nginx web server
   - mod_rewrite enabled

2. **Configure the application**
   - Upload all files to your web hosting
   - Navigate to `install.php` in your browser
   - Use these database connection details:
     - Database Host: `localhost`
     - Database Name: `[your_prefix]_tracking` (e.g., u181814817_tracking)
     - Database Username: `[your_prefix]_root` (e.g., u181814817_root)
     - Database Password: [your database password]

3. **Default Login Credentials**
   After installation, you can log in with these default credentials:

   **Admin Account:**
   - Username: `admin`
   - Password: `admin123`
   
   **Test Customer Account:**
   - Username: `customer`
   - Password: `customer123`

   ⚠️ **IMPORTANT:** Change these passwords immediately after first login!

## Common Problems and Solutions

### 1. Login Issues

**Problem:** Default admin credentials don't work
**Solution:** 
- Verify you're using exactly `admin` (lowercase) as username
- If still not working, use the password reset script:
  1. Upload `reset_admin.php` to your server
  2. Access it via browser
  3. Delete the file after use

**Problem:** "Invalid username or password" despite correct credentials
**Solution:**
- Clear browser cache and cookies
- Check if your hosting supports the required PHP version
- Verify database connection settings in `includes/config.php`

### 2. Database Connection Issues

**Problem:** Database connection errors
**Solution:**
- Always use `localhost` as Database Host on shared hosting
- Ensure database name includes your hosting prefix
- Check if your hosting uses a special port for MySQL

### 3. Installation Problems

**Problem:** Installation script fails
**Solution:**
- Verify PHP version compatibility
- Check database user permissions
- Ensure all required tables can be created
- Verify file permissions (755 for directories, 644 for files)

### 4. Access Control Issues

**Problem:** Redirect loops or access denied errors
**Solution:**
- Check .htaccess configuration
- Verify session handling in PHP
- Clear browser cache and cookies
- Check file permissions

## Security Notes

- Change default passwords immediately after installation
- Keep your PHP and MySQL installations up to date
- Implement HTTPS for secure data transmission
- Regular backup of database and files
- Remove installation files after setup
- Set proper file permissions
- Use strong passwords for all accounts

## Directory Structure

```
/
├── admin/                  # Admin CMS files
├── assets/                 # CSS, JS, and image files
│   ├── css/               # CSS files
│   ├── js/                # JavaScript files
│   └── img/               # Image files
├── includes/              # PHP includes and functions
├── tracking/              # Public tracking interface
├── database.sql          # Database setup script
└── README.md             # This file
```

## Usage

### Public Tracking

1. Enter a tracking number in the tracking form
2. View shipment details, map, and tracking timeline
3. Toggle between light and dark themes

### Admin CMS

1. Log in with admin credentials at `/admin`
2. Use the dashboard to view shipment statistics
3. Manage shipments and tracking updates
4. Add new users and configure system settings

## Support and Updates

For support issues:
1. Check the common problems section above
2. Review server logs for specific errors
3. Verify system requirements
4. Contact support with specific error messages

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Credits

- Design inspired by Maersk.com
- Uses OpenStreetMap with Leaflet for tracking visualization
- Font Awesome for icons
- PHP PDO for database operations


