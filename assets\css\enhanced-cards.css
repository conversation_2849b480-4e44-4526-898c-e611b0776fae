/* Enhanced Cards Styling */

/* Service Cards Enhancement */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.service-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
    padding: 30px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.service-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0.7;
}

.service-card::after {
    content: "";
    position: absolute;
    bottom: -50px;
    right: -50px;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    opacity: 0.1;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3), 0 0 0 1px rgba(92, 43, 226, 0.2);
    border-color: rgba(92, 43, 226, 0.3);
    background: rgba(255, 255, 255, 0.2);
}

.service-card:hover::after {
    transform: scale(1.5);
}

.service-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 10px 20px rgba(92, 43, 226, 0.2);
    transition: all 0.3s ease;
}

.service-card:hover .service-icon {
    box-shadow: 0 0 20px rgba(92, 43, 226, 0.5);
    transform: scale(1.05);
}

.service-card h3 {
    margin-bottom: 15px;
    color: var(--primary-color);
    font-weight: 600;
}

.service-card p {
    color: var(--text-secondary);
    margin-bottom: 20px;
    flex-grow: 1;
}

.service-card .learn-more {
    color: var(--primary-color);
    font-weight: 600;
    text-decoration: none;
    position: relative;
    display: inline-block;
    transition: all 0.3s ease;
}

.service-card .learn-more::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transform-origin: right;
    transition: transform 0.3s ease;
}

.service-card .learn-more:hover {
    color: var(--secondary-color);
}

.service-card .learn-more:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

/* Industry Cards Enhancement */
.industries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.industry-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
    padding: 30px;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.industry-card::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(92, 43, 226, 0.05), rgba(0, 212, 95, 0.05));
    z-index: -1;
}

.industry-card::after {
    content: "";
    position: absolute;
    bottom: -30px;
    right: -30px;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    opacity: 0.1;
    transition: all 0.3s ease;
}

.industry-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3), 0 0 0 1px rgba(92, 43, 226, 0.2);
    border-color: rgba(92, 43, 226, 0.3);
    background: rgba(255, 255, 255, 0.2);
}

.industry-card:hover::after {
    transform: scale(1.3);
}

.industry-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 10px 20px rgba(92, 43, 226, 0.2);
    transition: all 0.3s ease;
}

.industry-card:hover .industry-icon {
    box-shadow: 0 0 20px rgba(92, 43, 226, 0.5);
    transform: scale(1.05);
}

.industry-card h3 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0;
}

/* Dark theme adjustments */
.dark-theme .service-card,
.dark-theme .industry-card {
    background: rgba(30, 30, 30, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-theme .service-card:hover,
.dark-theme .industry-card:hover {
    background: rgba(40, 40, 40, 0.5);
    border-color: rgba(92, 43, 226, 0.4);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.25);
}

.dark-theme .service-card p,
.dark-theme .industry-card p {
    color: var(--text-secondary);
}

.dark-theme .service-card:hover .service-icon,
.dark-theme .industry-card:hover .industry-icon {
    box-shadow: 0 0 20px rgba(92, 43, 226, 0.6);
}

/* Card Animations - Removed to fix disappearing cards */

/* Responsive adjustments */
@media (max-width: 768px) {
    .services-grid,
    .industries-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .service-card,
    .industry-card {
        padding: 25px;
    }

    .service-icon,
    .industry-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }
}

@media (max-width: 576px) {
    .services-grid,
    .industries-grid {
        grid-template-columns: 1fr;
    }

    .service-card,
    .industry-card {
        padding: 20px;
    }
}
