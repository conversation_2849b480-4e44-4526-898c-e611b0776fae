<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if(!isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Get chart type
$chartType = isset($_GET['chart']) ? $_GET['chart'] : '';

// Initialize response
$response = [];

// Process based on chart type
switch($chartType) {
    case 'statusChart':
        // Get shipment statistics
        $db->query("SELECT
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                    SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                    SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as delayed,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                    FROM shipments");
        $stats = $db->single();
        
        // If query failed, initialize with default values
        if (!$stats) {
            $stats = [
                'total' => 0,
                'pending' => 0,
                'in_transit' => 0,
                'delivered' => 0,
                'delayed' => 0,
                'cancelled' => 0
            ];
        }
        
        // Format data for chart
        $response = [
            'labels' => ['Pending', 'In Transit', 'Delivered', 'Delayed', 'Cancelled'],
            'datasets' => [
                [
                    'label' => 'Shipment Status',
                    'data' => [
                        $stats['pending'] ?? 0,
                        $stats['in_transit'] ?? 0,
                        $stats['delivered'] ?? 0,
                        $stats['delayed'] ?? 0,
                        $stats['cancelled'] ?? 0
                    ],
                    'backgroundColor' => [
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(0, 123, 255, 0.8)',
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(255, 152, 0, 0.8)',
                        'rgba(220, 53, 69, 0.8)'
                    ],
                    'borderColor' => [
                        'rgba(255, 193, 7, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(255, 152, 0, 1)',
                        'rgba(220, 53, 69, 1)'
                    ],
                    'borderWidth' => 1
                ]
            ]
        ];
        break;
        
    case 'monthlyChart':
        // Get monthly shipment data for the current year
        $currentYear = date('Y');
        $db->query("SELECT 
                    MONTH(created_at) as month,
                    COUNT(*) as count
                    FROM shipments
                    WHERE YEAR(created_at) = :year
                    GROUP BY MONTH(created_at)
                    ORDER BY month");
        $db->bind(':year', $currentYear);
        $monthlyData = $db->resultSet();
        
        // If query failed, initialize with empty array
        if (!$monthlyData) {
            $monthlyData = [];
        }
        
        // Format monthly data for chart
        $monthlyShipments = array_fill(0, 12, 0); // Initialize with zeros for all months
        foreach ($monthlyData as $data) {
            $monthIndex = (int)$data['month'] - 1; // Convert to 0-based index
            $monthlyShipments[$monthIndex] = (int)$data['count'];
        }
        
        // Format data for chart
        $response = [
            'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            'datasets' => [
                [
                    'label' => 'Shipments in ' . $currentYear,
                    'data' => $monthlyShipments,
                    'backgroundColor' => 'rgba(92, 43, 226, 0.2)',
                    'borderColor' => 'rgba(92, 43, 226, 1)',
                    'borderWidth' => 2,
                    'tension' => 0.4,
                    'fill' => true
                ]
            ]
        ];
        break;
        
    case 'destinationsChart':
        // Get top destinations
        $db->query("SELECT 
                    destination,
                    COUNT(*) as count
                    FROM shipments
                    GROUP BY destination
                    ORDER BY count DESC
                    LIMIT 5");
        $topDestinations = $db->resultSet();
        
        // If query failed, initialize with empty array
        if (!$topDestinations) {
            $topDestinations = [];
        }
        
        // Format data for chart
        $destinations = [];
        $counts = [];
        $colors = [];
        
        foreach($topDestinations as $index => $destination) {
            $destinations[] = $destination['destination'];
            $counts[] = $destination['count'];
            // Generate a color based on index
            $colors[] = 'rgba(' . rand(50, 200) . ', ' . rand(50, 200) . ', ' . rand(50, 200) . ', 0.8)';
        }
        
        $response = [
            'labels' => $destinations,
            'datasets' => [
                [
                    'label' => 'Shipment Count',
                    'data' => $counts,
                    'backgroundColor' => $colors,
                    'borderColor' => $colors,
                    'borderWidth' => 1
                ]
            ]
        ];
        break;
        
    default:
        $response = ['error' => 'Invalid chart type'];
}

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
?>
