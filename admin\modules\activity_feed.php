<?php
/**
 * Activity Feed Module
 * 
 * Displays recent activity in the sidebar
 */

// If recent activity is not passed, fetch it
if (!isset($recentActivity)) {
    // Get recent activity
    $db->query("SELECT
                'shipment' as type,
                id,
                tracking_number,
                status,
                created_at as timestamp,
                CASE
                    WHEN status = 'pending' THEN 'New shipment created'
                    WHEN status = 'in_transit' THEN 'Shipment in transit'
                    WHEN status = 'delivered' THEN 'Shipment delivered'
                    WHEN status = 'delayed' THEN 'Shipment delayed'
                    WHEN status = 'cancelled' THEN 'Shipment cancelled'
                END as activity_title
                FROM shipments
                ORDER BY created_at DESC
                LIMIT 4");
    $recentActivity = $db->resultSet();

    // If query failed, initialize with empty array
    if (!$recentActivity) {
        $recentActivity = [];
    }
}
?>

<!-- Activity Feed -->
<div class="activity-feed">
    <h2 class="section-title">Recent Activity</h2>

    <div class="activity-list">
        <?php if(empty($recentActivity)): ?>
            <div class="no-activity">No recent activity found</div>
        <?php else: ?>
            <?php foreach($recentActivity as $activity): ?>
                <div class="activity-item">
                    <div class="activity-icon">
                        <?php
                        $icon = 'fas fa-box';
                        $activityTitle = $activity['activity_title'] ?? '';

                        if (!empty($activityTitle)) {
                            if (strpos($activityTitle, 'created') !== false) {
                                $icon = 'fas fa-plus';
                            } elseif (strpos($activityTitle, 'transit') !== false) {
                                $icon = 'fas fa-shipping-fast';
                            } elseif (strpos($activityTitle, 'delivered') !== false) {
                                $icon = 'fas fa-check';
                            } elseif (strpos($activityTitle, 'delayed') !== false) {
                                $icon = 'fas fa-exclamation-triangle';
                            } elseif (strpos($activityTitle, 'cancelled') !== false) {
                                $icon = 'fas fa-times';
                            }
                        }
                        ?>
                        <i class="<?php echo $icon; ?>"></i>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">
                            <?php echo $activityTitle; ?>
                            <?php if(!empty($activity['tracking_number'])): ?>
                                #<?php echo $activity['tracking_number']; ?>
                            <?php endif; ?>
                        </div>
                        <div class="activity-time"><?php echo timeAgo($activity['timestamp'] ?? ''); ?></div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>
