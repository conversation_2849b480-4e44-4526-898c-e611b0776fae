<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if (!isLoggedIn()) {
    setErrorNotification('Please log in to view your shipments');
    redirect('../login.php');
    exit;
}

// Get user information
$userId = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
$userInfo = getUserById($userId);
$userRole = isset($_SESSION['user_role']) ? $_SESSION['user_role'] : 'customer';

// Check if a specific user ID is requested in the URL
if (isset($_GET['user_id'])) {
    $requestedUserId = (int)$_GET['user_id'];

    // If the requested user ID doesn't match the logged-in user's ID
    // and the user doesn't have permission to view all shipments
    if ($requestedUserId !== $userId && !hasPermission('view_all_shipments')) {
        setErrorNotification('You do not have permission to view other users\'s shipments');
        redirect('index.php');
        exit;
    }

    // If the user has permission to view all shipments, update the userId to the requested one
    if (hasPermission('view_all_shipments')) {
        $userId = $requestedUserId;
        $userInfo = getUserById($userId);
        if (!$userInfo) {
            setErrorNotification('User not found');
            redirect('index.php');
            exit;
        }
    }
}

// Set page title based on role and context
if (hasPermission('view_all_shipments')) {
    if (isset($_GET['user_id']) && $userId != (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null)) {
        // Admin/staff viewing a specific user's shipments
        $pageTitle = $userInfo ? $userInfo['username'] . '\'s Shipments' : 'User Shipments';
    } else {
        // Admin/staff viewing all shipments
        $pageTitle = 'All Shipments';
    }
} else {
    // Regular user viewing their own shipments
    $pageTitle = 'My Shipments';
}

// Include header
include_once 'includes/header.php';

// Get shipments based on user role
$shipments = [];
try {
    if (hasPermission('view_all_shipments') && !isset($_GET['user_id'])) {
        // Staff and admin can see all shipments if not viewing a specific user
        $stmt = $conn->prepare("SELECT s.*, u.username FROM shipments s
                              LEFT JOIN users u ON s.user_id = u.id
                              ORDER BY s.created_at DESC");
        $stmt->execute();
    } else {
        // Customers can only see their own shipments
        // Or staff/admin viewing a specific user's shipments
        $stmt = $conn->prepare("SELECT s.*, u.username FROM shipments s
                              LEFT JOIN users u ON s.user_id = u.id
                              WHERE s.user_id = ?
                              ORDER BY s.created_at DESC");
        $stmt->execute([$userId]);
    }
    $shipments = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error silently
    error_log("Error fetching shipments: " . $e->getMessage());
}
?>

<div class="container">
    <div class="page-header">
        <h1><?php echo htmlspecialchars($pageTitle); ?></h1>
        <?php if (hasPermission('view_all_shipments') && !isset($_GET['user_id'])): ?>
        <p>View and manage all shipments in the system</p>
        <?php elseif (hasPermission('view_all_shipments') && isset($_GET['user_id']) && $userId != $_SESSION['user_id']): ?>
        <p>View and manage shipments for <?php echo htmlspecialchars($userInfo['username']); ?></p>
        <?php else: ?>
        <p>View and manage all your shipments</p>
        <?php endif; ?>
        <div class="header-actions">
            <?php if (hasPermission('view_all_shipments') && isset($_GET['user_id'])): ?>
            <a href="shipments.php" class="btn secondary-btn"><i class="fas fa-arrow-left"></i> Back to All Shipments</a>
            <?php endif; ?>
            <?php if (canCreateShipment()): ?>
            <a href="create-shipment.php" class="btn primary-btn"><i class="fas fa-plus-circle"></i> Create New Shipment</a>
            <?php endif; ?>
        </div>
    </div>

    <div class="shipments-filters glass-card">
        <div class="filter-controls">
            <div class="search-box">
                <input type="text" id="shipment-search" placeholder="Search by tracking number, origin, or destination...">
                <button id="search-btn"><i class="fas fa-search"></i></button>
            </div>
            <div class="filter-dropdown">
                <select id="status-filter">
                    <option value="all">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="in_transit">In Transit</option>
                    <option value="delivered">Delivered</option>
                    <option value="delayed">Delayed</option>
                    <option value="cancelled">Cancelled</option>
                </select>
            </div>
        </div>
    </div>

    <?php if (count($shipments) > 0): ?>
        <div class="shipments-list">
            <?php foreach ($shipments as $shipment): ?>
                <div class="shipment-card glass-card" data-status="<?php echo htmlspecialchars($shipment['status']); ?>">
                    <div class="shipment-header">
                        <h3>
                            <a href="../tracking/index.php?tracking_number=<?php echo htmlspecialchars($shipment['tracking_number']); ?>" class="tracking-link">
                                <i class="fas fa-search-location"></i> <?php echo htmlspecialchars($shipment['tracking_number']); ?>
                            </a>
                        </h3>
                        <span class="status-badge status-<?php echo strtolower(str_replace(' ', '-', $shipment['status'])); ?>">
                            <?php echo ucfirst(str_replace('_', ' ', $shipment['status'])); ?>
                        </span>
                    </div>
                    <div class="shipment-details">
                        <div class="detail-item">
                            <i class="fas fa-calendar-alt"></i>
                            <div>
                                <span class="detail-label">Created</span>
                                <span class="detail-value"><?php echo date('M d, Y', strtotime($shipment['created_at'])); ?></span>
                            </div>
                        </div>
                        <?php if (hasPermission('view_all_shipments') && isset($shipment['username'])): ?>
                        <div class="detail-item">
                            <i class="fas fa-user"></i>
                            <div>
                                <span class="detail-label">Customer</span>
                                <span class="detail-value"><?php echo htmlspecialchars($shipment['username']); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                        <div class="detail-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <span class="detail-label">Origin</span>
                                <span class="detail-value"><?php echo htmlspecialchars($shipment['origin']); ?></span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <i class="fas fa-map-pin"></i>
                            <div>
                                <span class="detail-label">Destination</span>
                                <span class="detail-value"><?php echo htmlspecialchars($shipment['destination']); ?></span>
                            </div>
                        </div>
                        <?php if (!empty($shipment['estimated_delivery'])): ?>
                        <div class="detail-item">
                            <i class="fas fa-truck"></i>
                            <div>
                                <span class="detail-label">Estimated Delivery</span>
                                <span class="detail-value"><?php echo date('M d, Y', strtotime($shipment['estimated_delivery'])); ?></span>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                    <div class="shipment-actions">
                        <a href="../tracking/index.php?tracking_number=<?php echo htmlspecialchars($shipment['tracking_number']); ?>" class="btn-icon track-btn" title="Track Shipment">
                            <i class="fas fa-route"></i> Track
                        </a>
                        <button class="btn-icon share-shipment" data-tracking="<?php echo htmlspecialchars($shipment['tracking_number']); ?>" title="Share">
                            <i class="fas fa-share-alt"></i>
                        </button>
                        <?php if (canUpdateShipment()): ?>
                        <a href="edit-shipment.php?id=<?php echo htmlspecialchars($shipment['id']); ?>" class="btn-icon" title="Edit Shipment">
                            <i class="fas fa-edit"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (canAddTrackingUpdate()): ?>
                        <a href="add-tracking-update.php?shipment_id=<?php echo htmlspecialchars($shipment['id']); ?>" class="btn-icon" title="Add Tracking Update">
                            <i class="fas fa-plus"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="empty-state glass-card">
            <div class="empty-icon">
                <i class="fas fa-box-open"></i>
            </div>
            <h3>No Shipments Yet</h3>
            <p>You don't have any shipments yet. Create your first shipment to get started.</p>
            <a href="create-shipment.php" class="btn primary-btn">Create Shipment</a>
        </div>
    <?php endif; ?>
</div>

<!-- Share Modal -->
<div id="shareModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Share Tracking Link</h3>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <p>Share this tracking link with others to let them track this shipment:</p>

            <div class="share-link-container">
                <input type="text" id="shareLink" readonly>
                <button class="btn primary-btn" id="copyLink">Copy</button>
            </div>

            <p>Or share via:</p>
            <div class="share-options">
                <div class="share-option" id="share-email">
                    <i class="fas fa-envelope"></i>
                    <span>Email</span>
                </div>
                <div class="share-option" id="share-whatsapp">
                    <i class="fab fa-whatsapp"></i>
                    <span>WhatsApp</span>
                </div>
                <div class="share-option" id="share-facebook">
                    <i class="fab fa-facebook"></i>
                    <span>Facebook</span>
                </div>
                <div class="share-option" id="share-twitter">
                    <i class="fab fa-twitter"></i>
                    <span>Twitter</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Share modal elements
        const modal = document.getElementById('shareModal');
        const closeBtn = document.querySelector('.close-modal');
        const shareButtons = document.querySelectorAll('.share-shipment');
        const shareLinkInput = document.getElementById('shareLink');
        const copyLinkBtn = document.getElementById('copyLink');

        // Share options
        const shareEmail = document.getElementById('share-email');
        const shareWhatsApp = document.getElementById('share-whatsapp');
        const shareFacebook = document.getElementById('share-facebook');
        const shareTwitter = document.getElementById('share-twitter');

        // Current tracking number being shared
        let currentTracking = '';

        // Open modal when share button is clicked
        shareButtons.forEach(button => {
            button.addEventListener('click', function() {
                currentTracking = this.getAttribute('data-tracking');
                const trackingUrl = getBasePath() + '/tracking/index.php?tracking_number=' + currentTracking;
                shareLinkInput.value = trackingUrl;
                modal.style.display = 'block';
            });
        });

        // Close modal when X is clicked
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
        });

        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });

        // Copy link to clipboard
        copyLinkBtn.addEventListener('click', function() {
            shareLinkInput.select();
            document.execCommand('copy');
            showNotification('Link copied to clipboard!', 'success');
        });

        // Share via email
        shareEmail.addEventListener('click', function() {
            const subject = 'Track your shipment';
            const body = 'You can track your shipment using this link: ' + shareLinkInput.value;
            window.location.href = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
        });

        // Share via WhatsApp
        shareWhatsApp.addEventListener('click', function() {
            const text = 'Track your shipment: ' + shareLinkInput.value;
            window.open(`https://wa.me/?text=${encodeURIComponent(text)}`, '_blank');
        });

        // Share via Facebook
        shareFacebook.addEventListener('click', function() {
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareLinkInput.value)}`, '_blank');
        });

        // Share via Twitter
        shareTwitter.addEventListener('click', function() {
            const text = 'Track your shipment with Global TransLogix: ';
            window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(shareLinkInput.value)}`, '_blank');
        });

        // Helper function to get base path
        function getBasePath() {
            // Try to extract from meta tag
            const basePathMeta = document.querySelector('meta[name="base-path"]');
            if (basePathMeta && basePathMeta.content) {
                return basePathMeta.content.replace(/\/$/, ''); // Remove trailing slash if present
            }

            // Try to extract from script tags (looking for assets/js paths)
            const scriptTags = document.querySelectorAll('script[src*="assets/js"]');
            if (scriptTags.length > 0) {
                const scriptSrc = scriptTags[0].getAttribute('src');
                const match = scriptSrc.match(/^(.*?)\/assets\/js/);
                if (match && match[1]) {
                    return match[1];
                }
            }

            // Try to extract from the current path
            const pathParts = window.location.pathname.split('/');
            if (pathParts.includes('user')) {
                // Find the index of 'user' and get everything before it
                const userIndex = pathParts.indexOf('user');
                if (userIndex > 0) {
                    return pathParts.slice(0, userIndex).join('/');
                }
            }

            // Default to empty string (root of the domain)
            return '';
        }

        // Show notification function
        function showNotification(message, type = 'info') {
            if (typeof window.showNotification === 'function') {
                window.showNotification(message, type);
            } else {
                alert(message);
            }
        }

        // Filter functionality
        const searchInput = document.getElementById('shipment-search');
        const statusFilter = document.getElementById('status-filter');
        const shipmentCards = document.querySelectorAll('.shipment-card');

        function filterShipments() {
            const searchTerm = searchInput.value.toLowerCase();
            const statusValue = statusFilter.value;

            shipmentCards.forEach(card => {
                const trackingNumber = card.querySelector('h3 a').textContent.toLowerCase();
                const origin = card.querySelector('.detail-value:nth-of-type(2)').textContent.toLowerCase();
                const destination = card.querySelector('.detail-value:nth-of-type(3)').textContent.toLowerCase();
                const status = card.getAttribute('data-status');

                const matchesSearch = trackingNumber.includes(searchTerm) ||
                                     origin.includes(searchTerm) ||
                                     destination.includes(searchTerm);

                const matchesStatus = statusValue === 'all' || status === statusValue;

                if (matchesSearch && matchesStatus) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        searchInput.addEventListener('input', filterShipments);
        statusFilter.addEventListener('change', filterShipments);

        // Search button click
        document.getElementById('search-btn').addEventListener('click', filterShipments);
    });
</script>

<style>
    .page-header {
        margin-bottom: 30px;
        padding-top: 30px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        align-items: center;
    }

    .page-header h1 {
        color: var(--primary-color);
        margin-bottom: 10px;
    }

    .header-actions {
        margin-top: 15px;
    }

    .glass-card {
        background-color: var(--glass-bg);
        border-radius: 16px;
        padding: 25px;
        margin-bottom: 30px;
        backdrop-filter: var(--glass-blur);
        -webkit-backdrop-filter: var(--glass-blur);
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
    }

    .shipments-filters {
        padding: 15px 25px;
    }

    .filter-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: center;
    }

    .search-box {
        flex: 1;
        display: flex;
        min-width: 250px;
    }

    .search-box input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px 0 0 8px;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .search-box button {
        padding: 10px 15px;
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 0 8px 8px 0;
        cursor: pointer;
    }

    .filter-dropdown select {
        padding: 10px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px;
        background-color: rgba(255, 255, 255, 0.8);
        min-width: 150px;
    }

    .shipment-card {
        margin-bottom: 20px;
        padding: 20px;
    }

    .shipment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .shipment-header h3 {
        margin: 0;
        font-size: 1.2rem;
    }

    .shipment-header h3 a.tracking-link {
        color: var(--primary-color);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 5px 10px;
        border-radius: 6px;
        transition: all 0.3s ease;
        background-color: rgba(var(--primary-rgb), 0.1);
    }

    .shipment-header h3 a.tracking-link:hover {
        background-color: rgba(var(--primary-rgb), 0.2);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .shipment-header h3 a.tracking-link i {
        font-size: 0.9em;
    }

    .status-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: capitalize;
    }

    .status-pending {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ff9800;
    }

    .status-in-transit {
        background-color: rgba(33, 150, 243, 0.2);
        color: #2196f3;
    }

    .status-delivered {
        background-color: rgba(76, 175, 80, 0.2);
        color: #4caf50;
    }

    .status-delayed {
        background-color: rgba(244, 67, 54, 0.2);
        color: #f44336;
    }

    .status-cancelled {
        background-color: rgba(158, 158, 158, 0.2);
        color: #9e9e9e;
    }

    .shipment-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-bottom: 15px;
    }

    .detail-item {
        display: flex;
        align-items: flex-start;
        gap: 10px;
    }

    .detail-item i {
        color: var(--primary-color);
        margin-top: 3px;
    }

    .detail-item div {
        display: flex;
        flex-direction: column;
    }

    .detail-label {
        font-size: 0.8rem;
        color: var(--text-secondary);
    }

    .detail-value {
        font-weight: 500;
    }

    .shipment-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-icon {
        background-color: rgba(var(--primary-rgb, 92, 43, 226), 0.1);
        border: none;
        color: var(--primary-color);
        font-size: 1rem;
        cursor: pointer;
        padding: 5px 12px;
        border-radius: 20px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .track-btn {
        background-color: rgba(var(--primary-rgb, 92, 43, 226), 0.2);
        font-weight: 500;
        padding: 8px 15px;
    }

    .btn-icon:hover {
        background-color: var(--primary-color);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
    }

    .empty-icon {
        font-size: 4rem;
        color: var(--text-secondary);
        margin-bottom: 20px;
    }

    .empty-state h3 {
        margin-bottom: 10px;
        color: var(--primary-color);
    }

    .empty-state p {
        color: var(--text-secondary);
        margin-bottom: 20px;
    }

    /* Share Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
    }

    .modal-content {
        background-color: var(--bg-color);
        margin: 10% auto;
        padding: 0;
        width: 90%;
        max-width: 500px;
        border-radius: 16px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        animation: modalFadeIn 0.3s;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 25px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }

    .modal-header h3 {
        margin: 0;
        color: var(--primary-color);
    }

    .close-modal {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--text-secondary);
    }

    .modal-body {
        padding: 25px;
    }

    .share-link-container {
        display: flex;
        margin: 20px 0;
    }

    .share-link-container input {
        flex: 1;
        padding: 12px 15px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 8px 0 0 8px;
        background-color: rgba(255, 255, 255, 0.8);
    }

    .share-link-container button {
        border-radius: 0 8px 8px 0;
    }

    .share-options {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 15px;
        margin-top: 20px;
    }

    .share-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 15px 10px;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.05);
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .share-option:hover {
        background-color: rgba(0, 0, 0, 0.1);
    }

    .share-option i {
        font-size: 1.5rem;
        color: var(--primary-color);
    }

    /* Dark theme adjustments */
    .dark-theme .glass-card {
        background-color: rgba(30, 30, 40, 0.7);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .dark-theme .search-box input,
    .dark-theme .filter-dropdown select {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .dark-theme .share-link-container input {
        background-color: rgba(0, 0, 0, 0.2);
        border-color: rgba(255, 255, 255, 0.1);
        color: white;
    }

    .dark-theme .share-option {
        background-color: rgba(255, 255, 255, 0.05);
    }

    .dark-theme .share-option:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .shipment-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .shipment-details {
            grid-template-columns: 1fr;
        }

        .share-options {
            grid-template-columns: repeat(2, 1fr);
        }

        .modal-content {
            margin: 20% auto;
            width: 95%;
        }
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>

<?php
// Include footer
include_once 'includes/footer.php';
?>
