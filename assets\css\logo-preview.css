/* Logo Preview Styles for Admin */
.logo-preview-container {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    margin-top: 30px;
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.logo-preview-container h3 {
    margin-bottom: 20px;
    color: var(--primary-color);
    text-align: center;
}

.logo-variations {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.logo-variation {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.logo-variation:hover {
    background-color: rgba(92, 43, 226, 0.05);
}

.logo-variation.selected {
    border-color: var(--primary-color);
    background-color: rgba(92, 43, 226, 0.1);
}

.logo-variation .logo-icon {
    width: 30px;
    height: 30px;
}

.logo-variation .logo-text-main {
    font-size: 1.1rem;
}

.logo-variation .logo-text-tagline {
    font-size: 0.7rem;
}

#save-logo {
    display: block;
    margin: 0 auto;
}

/* Dark theme adjustments */
.dark-theme .logo-variation:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .logo-variation.selected {
    background-color: rgba(255, 255, 255, 0.1);
}
