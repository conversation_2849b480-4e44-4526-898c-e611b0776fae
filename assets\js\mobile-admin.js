/**
 * Mobile Admin Dashboard Enhancements
 * Provides mobile-specific functionality for the admin dashboard
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run on mobile devices
    if (window.innerWidth > 768) return;

    initializeMobileAdmin();
});

function initializeMobileAdmin() {
    createMobileAdminHeader();
    setupSidebarToggle();
    convertTablesToCards();
    enhanceModals();
    setupTouchGestures();
    fixButtonClicks();
    preventAutoModals();
    addFloatingActionButton();
}

/**
 * Create mobile admin header
 */
function createMobileAdminHeader() {
    // Check if we're on an admin page
    if (!document.querySelector('.dashboard-overview')) return;

    const header = document.createElement('div');
    header.className = 'mobile-admin-header';
    header.innerHTML = `
        <div class="mobile-admin-title">
            <i class="fas fa-tachometer-alt"></i> Dashboard
        </div>
        <div class="mobile-admin-actions">
            <button class="mobile-admin-btn" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <a href="logout.php" class="mobile-admin-btn">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    `;

    document.body.insertBefore(header, document.body.firstChild);
}

/**
 * Setup sidebar toggle functionality
 */
function setupSidebarToggle() {
    const sidebar = document.querySelector('.dashboard-sidebar');
    const toggleBtn = document.getElementById('mobile-menu-toggle');
    
    if (!sidebar || !toggleBtn) return;

    // Create overlay
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    document.body.appendChild(overlay);

    // Toggle functionality
    function toggleSidebar() {
        sidebar.classList.toggle('active');
        overlay.classList.toggle('active');
        document.body.style.overflow = sidebar.classList.contains('active') ? 'hidden' : '';
    }

    toggleBtn.addEventListener('click', toggleSidebar);
    overlay.addEventListener('click', toggleSidebar);

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sidebar.classList.contains('active')) {
            toggleSidebar();
        }
    });
}

/**
 * Convert tables to mobile-friendly cards
 */
function convertTablesToCards() {
    // Handle both shipment tables and general data tables
    const tableContainers = document.querySelectorAll('.table-container, .table-responsive');

    tableContainers.forEach(container => {
        const table = container.querySelector('.data-table, table');
        if (!table) return;

        const rows = table.querySelectorAll('tbody tr');
        if (rows.length === 0) return;

        // Create mobile cards container
        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'mobile-data-cards';

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length === 0) return;

            const card = document.createElement('div');
            card.className = 'data-card';

            // Get headers for dynamic card creation
            const headers = table.querySelectorAll('thead th');
            const isShipmentTable = headers[0]?.textContent.includes('Tracking');
            const isUserTable = headers[0]?.textContent.includes('Username') || headers[0]?.textContent.includes('User');

            let cardContent = '';

            if (isShipmentTable) {
                // Shipment-specific card layout
                const trackingNumber = cells[0]?.textContent.trim() || '';
                const customer = cells[1]?.textContent.trim() || '';
                const origin = cells[2]?.textContent.trim() || '';
                const destination = cells[3]?.textContent.trim() || '';
                const status = cells[4]?.innerHTML || '';
                const date = cells[5]?.textContent.trim() || '';
                const actions = cells[6]?.innerHTML || '';

                cardContent = `
                    <div class="data-card-header">
                        <div class="data-card-title">#${trackingNumber}</div>
                        <div class="data-card-status">${status}</div>
                    </div>
                    <div class="data-card-body">
                        <div class="data-card-field"><strong>Customer:</strong> ${customer}</div>
                        <div class="data-card-field"><strong>Date:</strong> ${date}</div>
                        <div class="data-card-field"><strong>From:</strong> ${origin}</div>
                        <div class="data-card-field"><strong>To:</strong> ${destination}</div>
                    </div>
                    <div class="data-card-actions">
                        ${actions}
                    </div>
                `;
            } else if (isUserTable) {
                // User-specific card layout
                const username = cells[0]?.textContent.trim() || '';
                const email = cells[1]?.textContent.trim() || '';
                const role = cells[2]?.textContent.trim() || '';
                const status = cells[3]?.textContent.trim() || '';
                const lastLogin = cells[4]?.textContent.trim() || '';
                const actions = cells[5]?.innerHTML || '';

                cardContent = `
                    <div class="data-card-header">
                        <div class="data-card-title">${username}</div>
                        <div class="data-card-status">${status}</div>
                    </div>
                    <div class="data-card-body">
                        <div class="data-card-field"><strong>Email:</strong> ${email}</div>
                        <div class="data-card-field"><strong>Role:</strong> ${role}</div>
                        <div class="data-card-field"><strong>Last Login:</strong> ${lastLogin}</div>
                    </div>
                    <div class="data-card-actions">
                        ${actions}
                    </div>
                `;
            } else {
                // Generic card layout for other tables
                const title = cells[0]?.textContent.trim() || '';
                const actions = cells[cells.length - 1]?.innerHTML || '';

                let bodyContent = '';
                for (let i = 1; i < cells.length - 1; i++) {
                    const headerText = headers[i]?.textContent.trim() || `Field ${i}`;
                    const cellContent = cells[i]?.textContent.trim() || '';
                    bodyContent += `<div class="data-card-field"><strong>${headerText}:</strong> ${cellContent}</div>`;
                }

                cardContent = `
                    <div class="data-card-header">
                        <div class="data-card-title">${title}</div>
                    </div>
                    <div class="data-card-body">
                        ${bodyContent}
                    </div>
                    <div class="data-card-actions">
                        ${actions}
                    </div>
                `;
            }

            card.innerHTML = cardContent;

            // Re-attach event listeners to buttons in the card
            const buttons = card.querySelectorAll('button, .btn');
            buttons.forEach(button => {
                // Copy all event listeners from original button
                const originalButton = row.querySelector(`[onclick="${button.getAttribute('onclick')}"]`);
                if (originalButton && button.getAttribute('onclick')) {
                    button.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        // Execute the onclick function
                        try {
                            eval(button.getAttribute('onclick'));
                        } catch (error) {
                            console.error('Error executing button click:', error);
                        }
                    });
                }
            });

            cardsContainer.appendChild(card);
        });

        // Replace table with cards
        container.style.display = 'none';
        container.parentNode.insertBefore(cardsContainer, container.nextSibling);
    });
}

/**
 * Enhance modals for mobile
 */
function enhanceModals() {
    // Add mobile-friendly modal headers
    const modals = document.querySelectorAll('.modal, .popup-overlay');
    
    modals.forEach(modal => {
        const content = modal.querySelector('.modal-content, .popup-content');
        if (!content) return;

        // Ensure modal has proper mobile structure
        if (!content.querySelector('.modal-header')) {
            const title = content.querySelector('h2, h3, .modal-title');
            const closeBtn = content.querySelector('.close, .modal-close');
            
            if (title) {
                const header = document.createElement('div');
                header.className = 'modal-header';
                header.innerHTML = `
                    <h3 class="modal-title">${title.textContent}</h3>
                    <button class="modal-close" type="button">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                content.insertBefore(header, content.firstChild);
                title.remove();
                if (closeBtn) closeBtn.remove();
            }
        }

        // Add body wrapper if not exists
        const body = content.querySelector('.modal-body');
        if (!body) {
            const bodyContent = Array.from(content.children).filter(child => 
                !child.classList.contains('modal-header') && 
                !child.classList.contains('modal-footer')
            );
            
            if (bodyContent.length > 0) {
                const bodyWrapper = document.createElement('div');
                bodyWrapper.className = 'modal-body';
                bodyContent.forEach(child => bodyWrapper.appendChild(child));
                content.appendChild(bodyWrapper);
            }
        }

        // Enhance form inputs
        const inputs = content.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            if (input.type !== 'hidden') {
                input.style.fontSize = '16px'; // Prevent zoom on iOS
            }
        });
    });
}

/**
 * Setup touch gestures for better mobile interaction
 */
function setupTouchGestures() {
    // Add touch feedback to cards
    const cards = document.querySelectorAll('.stat-card, .action-card, .shipment-card');
    
    cards.forEach(card => {
        card.addEventListener('touchstart', function() {
            this.style.transform = 'scale(0.98)';
        });
        
        card.addEventListener('touchend', function() {
            this.style.transform = '';
        });
        
        card.addEventListener('touchcancel', function() {
            this.style.transform = '';
        });
    });

    // Swipe to refresh stats
    let startY = 0;
    let isRefreshing = false;

    document.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
    });

    document.addEventListener('touchmove', function(e) {
        if (isRefreshing) return;
        
        const currentY = e.touches[0].clientY;
        const diff = currentY - startY;
        
        // Pull to refresh gesture
        if (diff > 100 && window.scrollY === 0) {
            const refreshBtn = document.querySelector('.refresh-stats');
            if (refreshBtn) {
                isRefreshing = true;
                refreshBtn.click();
                
                // Show refresh indicator
                showToast('Refreshing data...', 'info');
                
                setTimeout(() => {
                    isRefreshing = false;
                }, 2000);
            }
        }
    });
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 8px;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => toast.classList.add('show'), 100);
    
    // Hide toast
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}

/**
 * Add floating action button for add actions
 */
function addFloatingActionButton() {
    // Check if we're on a management page
    const isManagePage = window.location.pathname.includes('manage-');
    if (!isManagePage) return;

    // Look for existing add buttons
    const addButton = document.querySelector('[onclick*="addShipmentModal"], [onclick*="addUserModal"], .add-btn, [href*="add-"]');
    if (!addButton) return;

    // Create floating action button
    const fab = document.createElement('button');
    fab.className = 'mobile-fab';
    fab.innerHTML = '<i class="fas fa-plus"></i>';
    fab.setAttribute('aria-label', 'Add New Item');

    // Copy the onclick behavior from the original add button
    if (addButton.hasAttribute('onclick')) {
        fab.setAttribute('onclick', addButton.getAttribute('onclick'));
    } else if (addButton.href) {
        fab.addEventListener('click', function() {
            window.location.href = addButton.href;
        });
    }

    // Add to page
    document.body.appendChild(fab);

    // Hide the original add button on mobile
    addButton.style.display = 'none';
}

/**
 * Prevent auto-opening modals on mobile page load
 */
function preventAutoModals() {
    // Override the automatic modal opening behavior on mobile
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');

    if (action) {
        // Instead of auto-opening modals, show a notification
        const actionMessages = {
            'add': 'Tap the + button to add a new item',
            'edit': 'Find the item in the list and tap Edit',
            'view': 'Find the item in the list and tap View',
            'add_update': 'Find the item in the list and tap Add Update'
        };

        const message = actionMessages[action] || 'Use the buttons below to perform actions';
        showToast(message, 'info');

        // Clear the URL parameters to prevent desktop behavior
        const newUrl = window.location.pathname;
        window.history.replaceState({}, document.title, newUrl);
    }

    // Close any modals that might have auto-opened
    setTimeout(() => {
        const openModals = document.querySelectorAll('.modal[style*="display: block"], .modal.show');
        openModals.forEach(modal => {
            modal.style.display = 'none';
            modal.classList.remove('show');
        });
        document.body.style.overflow = '';
    }, 100);
}

/**
 * Fix button clicks that might be broken by mobile transformations
 */
function fixButtonClicks() {
    // Ensure all buttons and links work properly
    const buttons = document.querySelectorAll('button, .btn, a[href]');

    buttons.forEach(button => {
        // Remove any existing click handlers that might interfere
        button.style.pointerEvents = 'auto';
        button.style.cursor = 'pointer';

        // For buttons with onclick attributes, ensure they work
        if (button.hasAttribute('onclick')) {
            const originalOnclick = button.getAttribute('onclick');
            button.addEventListener('click', function(e) {
                // Don't prevent default for regular links
                if (button.tagName === 'A' && button.getAttribute('href') && !button.getAttribute('href').startsWith('#')) {
                    return; // Let normal navigation happen
                }

                e.preventDefault();
                e.stopPropagation();

                try {
                    eval(originalOnclick);
                } catch (error) {
                    console.error('Error executing onclick:', error);
                }
            });
        }
    });

    // Fix login button specifically
    const loginButton = document.querySelector('#mobile-login-btn, [href*="login"], .login-btn');
    if (loginButton) {
        loginButton.addEventListener('click', function(e) {
            // If it's a popup login, handle it
            if (this.getAttribute('onclick') && this.getAttribute('onclick').includes('showLoginPopup')) {
                e.preventDefault();
                if (typeof showLoginPopup === 'function') {
                    showLoginPopup();
                }
            }
        });
    }
}

/**
 * Handle window resize
 */
window.addEventListener('resize', function() {
    if (window.innerWidth > 768) {
        // Clean up mobile elements when switching to desktop
        const mobileHeader = document.querySelector('.mobile-admin-header');
        const overlay = document.querySelector('.sidebar-overlay');

        if (mobileHeader) mobileHeader.remove();
        if (overlay) overlay.remove();

        document.body.style.overflow = '';
    } else if (window.innerWidth <= 768) {
        // Reinitialize mobile features
        setTimeout(initializeMobileAdmin, 100);
    }
});
