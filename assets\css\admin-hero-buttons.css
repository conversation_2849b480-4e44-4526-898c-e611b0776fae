/* Admin Hero Buttons Enhancement
 * This file improves the styling of buttons in the admin dashboard hero section
 */

/* Base button styles for admin hero section */
.admin-hero .admin-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
    margin-top: 30px;
}

.admin-hero .admin-actions .btn {
    position: relative;
    min-width: 180px;
    padding: 14px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    letter-spacing: 0.5px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
    border: none;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

/* Add icon to buttons if not present */
.admin-hero .admin-actions .btn:not(:has(i))::before {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 10px;
    font-size: 1.1rem;
}

.admin-hero .admin-actions .primary-btn:not(:has(i))::before {
    content: "\f0ae"; /* Tasks icon */
}

.admin-hero .admin-actions .secondary-btn:not(:has(i))::before {
    content: "\f007"; /* User icon */
}

.admin-hero .admin-actions .purple-btn:not(:has(i))::before {
    content: "\f085"; /* Cog icon */
}

.admin-hero .admin-actions .orange-btn:not(:has(i))::before {
    content: "\f0ad"; /* Wrench icon */
}

.admin-hero .admin-actions .teal-btn:not(:has(i))::before {
    content: "\f2f5"; /* Sign out icon */
}

/* Add subtle gradient background to buttons */
.admin-hero .admin-actions .btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 100%);
    z-index: -1;
    transition: opacity 0.3s ease;
    opacity: 0;
}

.admin-hero .admin-actions .btn:hover::after {
    opacity: 1;
}

/* Add subtle glow effect on hover */
.admin-hero .admin-actions .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

/* Add active state */
.admin-hero .admin-actions .btn:active {
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* Button color styles with improved contrast and vibrancy */
.admin-hero .admin-actions .primary-btn {
    background: linear-gradient(135deg, #5c2be2 0%, #4f25c2 100%);
    color: white;
}

.admin-hero .admin-actions .secondary-btn {
    background: linear-gradient(135deg, #00d45f 0%, #00b345 100%);
    color: white;
}

.admin-hero .admin-actions .purple-btn {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    color: white;
}

.admin-hero .admin-actions .orange-btn {
    background: linear-gradient(135deg, #ff7043 0%, #e64a19 100%);
    color: white;
}

.admin-hero .admin-actions .teal-btn {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    color: white;
}

/* Add subtle border glow on hover */
.admin-hero .admin-actions .primary-btn:hover {
    box-shadow: 0 10px 20px rgba(92, 43, 226, 0.3);
}

.admin-hero .admin-actions .secondary-btn:hover {
    box-shadow: 0 10px 20px rgba(0, 212, 95, 0.3);
}

.admin-hero .admin-actions .purple-btn:hover {
    box-shadow: 0 10px 20px rgba(156, 39, 176, 0.3);
}

.admin-hero .admin-actions .orange-btn:hover {
    box-shadow: 0 10px 20px rgba(255, 112, 67, 0.3);
}

.admin-hero .admin-actions .teal-btn:hover {
    box-shadow: 0 10px 20px rgba(0, 188, 212, 0.3);
}

/* Add ripple effect on click */
.admin-hero .admin-actions .btn .ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    z-index: -1;
}

@keyframes ripple {
    to {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* Add subtle pulse animation to primary button to draw attention */
.admin-hero .admin-actions .primary-btn {
    animation: subtle-pulse 2s infinite;
}

@keyframes subtle-pulse {
    0% {
        box-shadow: 0 6px 15px rgba(92, 43, 226, 0.1);
    }
    50% {
        box-shadow: 0 6px 25px rgba(92, 43, 226, 0.3);
    }
    100% {
        box-shadow: 0 6px 15px rgba(92, 43, 226, 0.1);
    }
}

/* Button tooltip styles */
.admin-hero .admin-actions .btn {
    position: relative;
}

.admin-hero .admin-actions .btn .button-tooltip {
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%) translateY(10px);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: normal;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s ease;
    z-index: 10;
}

.admin-hero .admin-actions .btn .button-tooltip::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    border-width: 0 6px 6px 6px;
    border-style: solid;
    border-color: transparent transparent rgba(0, 0, 0, 0.8) transparent;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .admin-hero .admin-actions {
        gap: 15px;
    }

    .admin-hero .admin-actions .btn {
        min-width: 160px;
        padding: 12px 20px;
        font-size: 0.95rem;
    }

    .admin-hero .admin-actions .btn .button-tooltip {
        display: none; /* Hide tooltips on smaller screens */
    }
}

@media (max-width: 768px) {
    .admin-hero .admin-actions {
        flex-direction: column;
        align-items: center;
        gap: 15px;
        width: 100%;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .admin-hero .admin-actions .btn {
        width: 100%;
        min-width: unset;
    }
}
