<?php
/**
 * Debug Geocoding
 * 
 * This script helps debug geocoding issues
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Debug Geocoding';

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-bug"></i> Debug Geocoding</h1>
            <div class="admin-actions">
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Test HTTP Requests</h2>
                </div>
                <div class="card-body">
                    <h3>Testing file_get_contents</h3>
                    <?php
                    try {
                        // Check if allow_url_fopen is enabled
                        echo "<p><strong>allow_url_fopen:</strong> " . (ini_get('allow_url_fopen') ? 'Enabled' : 'Disabled') . "</p>";
                        
                        // Test a simple HTTP request
                        $testUrl = 'https://nominatim.openstreetmap.org/search?q=London&format=json&limit=1';
                        echo "<p><strong>Test URL:</strong> <a href='{$testUrl}' target='_blank'>{$testUrl}</a></p>";
                        
                        // Create a stream context with a user agent
                        $options = [
                            'http' => [
                                'header' => 'User-Agent: TrackingSite/1.0'
                            ]
                        ];
                        $context = stream_context_create($options);
                        
                        // Make the request
                        echo "<p><strong>Making request...</strong></p>";
                        $response = @file_get_contents($testUrl, false, $context);
                        
                        if ($response === false) {
                            echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Request failed: " . error_get_last()['message'] . "</div>";
                        } else {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Request successful!</div>";
                            echo "<p><strong>Response:</strong></p>";
                            echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . (strlen($response) > 1000 ? '...' : '') . "</pre>";
                            
                            // Try to decode the JSON
                            $data = json_decode($response, true);
                            if (json_last_error() !== JSON_ERROR_NONE) {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> JSON decode error: " . json_last_error_msg() . "</div>";
                            } else {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> JSON decode successful!</div>";
                                echo "<p><strong>Decoded data:</strong></p>";
                                echo "<pre>" . print_r($data, true) . "</pre>";
                            }
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Exception: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>Test cURL</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Check if cURL is available
                        echo "<p><strong>cURL extension:</strong> " . (function_exists('curl_version') ? 'Available' : 'Not available') . "</p>";
                        
                        if (function_exists('curl_version')) {
                            // Get cURL version info
                            $curlInfo = curl_version();
                            echo "<p><strong>cURL version:</strong> " . $curlInfo['version'] . "</p>";
                            echo "<p><strong>SSL version:</strong> " . $curlInfo['ssl_version'] . "</p>";
                            
                            // Test a simple HTTP request with cURL
                            $testUrl = 'https://nominatim.openstreetmap.org/search?q=London&format=json&limit=1';
                            echo "<p><strong>Test URL:</strong> <a href='{$testUrl}' target='_blank'>{$testUrl}</a></p>";
                            
                            // Initialize cURL
                            $ch = curl_init($testUrl);
                            
                            // Set cURL options
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_USERAGENT, 'TrackingSite/1.0');
                            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                            
                            // Execute the request
                            echo "<p><strong>Making request...</strong></p>";
                            $response = curl_exec($ch);
                            
                            // Check for errors
                            if ($response === false) {
                                echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> cURL error: " . curl_error($ch) . " (Code: " . curl_errno($ch) . ")</div>";
                            } else {
                                echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> cURL request successful!</div>";
                                echo "<p><strong>Response:</strong></p>";
                                echo "<pre>" . htmlspecialchars(substr($response, 0, 1000)) . (strlen($response) > 1000 ? '...' : '') . "</pre>";
                                
                                // Try to decode the JSON
                                $data = json_decode($response, true);
                                if (json_last_error() !== JSON_ERROR_NONE) {
                                    echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> JSON decode error: " . json_last_error_msg() . "</div>";
                                } else {
                                    echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> JSON decode successful!</div>";
                                    echo "<p><strong>Decoded data:</strong></p>";
                                    echo "<pre>" . print_r($data, true) . "</pre>";
                                }
                            }
                            
                            // Close cURL
                            curl_close($ch);
                        }
                    } catch (Exception $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-exclamation-triangle'></i> Exception: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h2>PHP Info</h2>
                </div>
                <div class="card-body">
                    <?php
                    // Display relevant PHP settings
                    echo "<h3>PHP Settings</h3>";
                    echo "<table class='table'>";
                    echo "<tr><th>Setting</th><th>Value</th></tr>";
                    echo "<tr><td>PHP Version</td><td>" . phpversion() . "</td></tr>";
                    echo "<tr><td>allow_url_fopen</td><td>" . (ini_get('allow_url_fopen') ? 'On' : 'Off') . "</td></tr>";
                    echo "<tr><td>default_socket_timeout</td><td>" . ini_get('default_socket_timeout') . "</td></tr>";
                    echo "<tr><td>max_execution_time</td><td>" . ini_get('max_execution_time') . "</td></tr>";
                    echo "<tr><td>memory_limit</td><td>" . ini_get('memory_limit') . "</td></tr>";
                    echo "<tr><td>display_errors</td><td>" . (ini_get('display_errors') ? 'On' : 'Off') . "</td></tr>";
                    echo "<tr><td>error_reporting</td><td>" . ini_get('error_reporting') . "</td></tr>";
                    echo "</table>";
                    ?>
                </div>
            </div>
        </div>
        
        <div class="admin-actions mt-4">
            <a href="test_geocoding.php" class="btn primary-btn"><i class="fas fa-map-marker-alt"></i> Go to Geocoding Test</a>
            <a href="admin/index.php" class="btn secondary-btn"><i class="fas fa-arrow-left"></i> Return to Dashboard</a>
        </div>
    </div>
</section>

<style>
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left: 4px solid #17a2b8;
    color: #17a2b8;
}

code, pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    padding: 10px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
}

.mt-4 {
    margin-top: 20px;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}

.table th {
    background-color: rgba(0, 0, 0, 0.05);
}
</style>

<?php include_once 'includes/footer.php'; ?>
