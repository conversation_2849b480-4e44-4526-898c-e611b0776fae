// Mobile Sidebar Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const dashboardSidebar = document.querySelector('.dashboard-sidebar');
    const body = document.body;

    // Create overlay element
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    body.appendChild(overlay);

    // Create close button for sidebar
    const closeButton = document.createElement('button');
    closeButton.className = 'sidebar-close';
    closeButton.innerHTML = '<i class="fas fa-times"></i>';
    closeButton.setAttribute('aria-label', 'Close Sidebar');
    closeButton.style.cursor = 'pointer';

    // Add close button to sidebar if it doesn't exist
    if (dashboardSidebar && !dashboardSidebar.querySelector('.sidebar-close')) {
        dashboardSidebar.prepend(closeButton);
    }

    // Toggle sidebar function
    function toggleSidebar() {
        if (dashboardSidebar) {
            dashboardSidebar.classList.toggle('active');
            overlay.classList.toggle('active');
            if (sidebarToggle) {
                sidebarToggle.classList.toggle('active');
            }

            // Prevent body scrolling when sidebar is open
            if (dashboardSidebar.classList.contains('active')) {
                body.style.overflow = 'hidden';
            } else {
                body.style.overflow = '';
            }
        }
    }

    // Event listeners
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }

    // Close sidebar when clicking on overlay
    overlay.addEventListener('click', toggleSidebar);

    // Close sidebar when pressing Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && dashboardSidebar && dashboardSidebar.classList.contains('active')) {
            toggleSidebar();
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 992 && dashboardSidebar && dashboardSidebar.classList.contains('active')) {
            dashboardSidebar.classList.remove('active');
            overlay.classList.remove('active');
            if (sidebarToggle) {
                sidebarToggle.classList.remove('active');
            }
            body.style.overflow = '';
        }
    });
});

// Responsive Table Handling
document.addEventListener('DOMContentLoaded', function() {
    const tables = document.querySelectorAll('.data-table');

    tables.forEach(table => {
        const tableContainer = table.closest('.table-container');
        if (tableContainer) {
            // Add horizontal scroll indicator if table is wider than container
            if (table.offsetWidth > tableContainer.offsetWidth) {
                const scrollIndicator = document.createElement('div');
                scrollIndicator.className = 'scroll-indicator';
                scrollIndicator.innerHTML = '<i class="fas fa-arrows-left-right"></i> Scroll horizontally to see more';
                scrollIndicator.style.textAlign = 'center';
                scrollIndicator.style.padding = '10px';
                scrollIndicator.style.color = 'var(--text-secondary)';
                scrollIndicator.style.fontSize = '0.9rem';

                // Insert after table container
                tableContainer.parentNode.insertBefore(scrollIndicator, tableContainer.nextSibling);

                // Hide indicator after user has scrolled
                tableContainer.addEventListener('scroll', function() {
                    scrollIndicator.style.display = 'none';
                }, { once: true });
            }
        }
    });
});

// Responsive Chart Handling
document.addEventListener('DOMContentLoaded', function() {
    function updateChartOptions() {
        if (typeof Chart !== 'undefined') {
            const chartElements = document.querySelectorAll('canvas[id$="Chart"]');

            chartElements.forEach(canvas => {
                const chartInstance = Chart.getChart(canvas);
                if (chartInstance) {
                    // Adjust options based on screen size
                    if (window.innerWidth <= 768) {
                        // Mobile optimizations
                        if (chartInstance.config.type === 'doughnut' || chartInstance.config.type === 'pie') {
                            chartInstance.options.plugins.legend.position = 'bottom';
                        }

                        // Reduce padding
                        chartInstance.options.layout = chartInstance.options.layout || {};
                        chartInstance.options.layout.padding = 10;

                        // Simplify tooltips
                        chartInstance.options.plugins.tooltip = chartInstance.options.plugins.tooltip || {};
                        chartInstance.options.plugins.tooltip.enabled = true;
                        chartInstance.options.plugins.tooltip.mode = 'nearest';

                        // Update and redraw
                        chartInstance.update();
                    }
                }
            });
        }
    }

    // Run on load and resize
    updateChartOptions();
    window.addEventListener('resize', updateChartOptions);
});

