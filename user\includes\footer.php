    <!-- Theme Toggle and Mobile Menu Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }

            // Theme Toggle Functionality
            const themeToggle = document.getElementById('theme-toggle');
            const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
            const body = document.body;

            function toggleTheme() {
                body.classList.toggle('dark-theme');

                // Update header theme toggle icon (Font Awesome)
                if (themeToggle) {
                    const headerIcon = themeToggle.querySelector('i');
                    if (headerIcon) {
                        if (body.classList.contains('dark-theme')) {
                            headerIcon.classList.replace('fa-moon', 'fa-sun');
                        } else {
                            headerIcon.classList.replace('fa-sun', 'fa-moon');
                        }
                    }
                }

                // Update mobile theme toggle icon (Feather)
                if (mobileThemeToggle) {
                    const mobileIcon = mobileThemeToggle.querySelector('i');
                    if (mobileIcon) {
                        if (body.classList.contains('dark-theme')) {
                            mobileIcon.setAttribute('data-feather', 'sun');
                        } else {
                            mobileIcon.setAttribute('data-feather', 'moon');
                        }
                        // Re-render the specific icon
                        if (typeof feather !== 'undefined') {
                            feather.replace();
                        }
                    }
                }

                // Save theme preference
                if (body.classList.contains('dark-theme')) {
                    document.cookie = "theme=dark; path=/; max-age=31536000"; // 1 year
                } else {
                    document.cookie = "theme=light; path=/; max-age=31536000"; // 1 year
                }
            }

            // Add event listeners to both theme toggles
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleTheme);
            }
            if (mobileThemeToggle) {
                mobileThemeToggle.addEventListener('click', toggleTheme);
            }

            // Mobile menu functionality removed - using bottom navigation instead

            // Mobile logo now uses direct HTML - no JavaScript needed
        });
    </script>

    <!-- Bottom Navigation for Mobile -->
    <?php echo generateBottomNav(); ?>

    <!-- Fixed Mobile Logo (only on mobile) -->
    <div class="mobile-logo-container">
        <div class="logo mobile-logo">
            <a href="<?php echo SITE_URL; ?>" class="site-logo">
                <div class="logo-icon">
                    <div class="logo-icon-bg"></div>
                    <i class="fas fa-map-marker-alt logo-icon-inner"></i>
                </div>
            </a>
        </div>
    </div>

    <!-- Notification System -->
    <script src="<?php echo SITE_URL; ?>/assets/js/notifications.js"></script>
    <?php displayNotifications(); ?>

    <!-- Logo Generator -->
    <script src="<?php echo SITE_URL; ?>/assets/js/logo-generator.js"></script>

    <!-- Mobile Enhancements -->
    <script src="<?php echo SITE_URL; ?>/assets/js/mobile-enhancements.js"></script>

    <!-- Mobile Navigation Effects -->
    <script src="<?php echo SITE_URL; ?>/assets/js/mobile-nav-effects.js"></script>

    <?php if(isset($additionalJs)): ?>
        <?php foreach($additionalJs as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
</body>
</html>