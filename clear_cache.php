<?php
// Clear PHP session
session_start();
session_destroy();

// Clear PHP opcache if enabled
if (function_exists('opcache_reset')) {
    opcache_reset();
}

// Clear APC cache if enabled
if (function_exists('apc_clear_cache')) {
    apc_clear_cache();
    apc_clear_cache('user');
    apc_clear_cache('opcode');
}

echo "<h1>Cache Cleared</h1>";
echo "<p>PHP session, opcache, and APC cache (if available) have been cleared.</p>";
echo "<p><a href='admin/index.php'>Go to Dashboard</a></p>";
?>
