<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TransLogix Global - Documentation</title>

    <!-- Using existing CSS files -->
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="page-styles.css">
    <link rel="stylesheet" href="assets/css/inner-pages.css">
    <link rel="stylesheet" href="assets/css/enhanced-design.css">
    <link rel="stylesheet" href="assets/css/textures.css">
    <link rel="stylesheet" href="assets/css/enhanced-cards.css">
    <link rel="stylesheet" href="assets/css/notifications.css">
    <link rel="stylesheet" href="assets/css/logo.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdn.jsdelivr.net/npm/feather-icons@4.29.0/dist/feather.min.js"></script>

    <!-- PDF-specific meta tags -->
    <meta name="author" content="Tembi Kelvin (defabrika)">
    <meta name="description" content="TransLogix Global - A comprehensive logistics and shipment tracking platform documentation">
    <meta name="keywords" content="logistics, tracking, shipment, documentation, TransLogix Global">

    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,300;1,400;1,500;1,700;1,900&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

        :root {
            /* Primary color palette */
            --primary-color: #4a6cf7;
            --primary-light: #6a8bff;
            --primary-dark: #3a4dc3;
            --secondary-color: #ff6b6b;
            --secondary-light: #ff8e8e;
            --secondary-dark: #e74c3c;
            --accent-color: #ffbe0b;
            --accent-light: #ffd44c;
            --accent-dark: #e6a800;

            /* Text colors */
            --text-color: #333;
            --text-secondary: #555;
            --text-light: #777;
            --text-heading: #222;

            /* Background colors */
            --bg-primary: #fff;
            --bg-secondary: #f9f9f9;
            --bg-tertiary: #f2f2f2;
            --bg-accent: rgba(74, 108, 247, 0.05);
            --bg-accent-secondary: rgba(255, 107, 107, 0.05);

            /* Paper-specific colors */
            --paper-color: #fff;
            --paper-edge: #f0f0f0;
            --paper-shadow: rgba(0, 0, 0, 0.1);
            --chapter-marker: var(--secondary-color);
            --paper-border: #e0e0e0;

            /* Spacing variables */
            --spacing-xs: 0.5rem;
            --spacing-sm: 1rem;
            --spacing-md: 1.5rem;
            --spacing-lg: 2rem;
            --spacing-xl: 3rem;

            /* Typography */
            --font-heading: 'Playfair Display', Georgia, serif;
            --font-body: 'Roboto', Arial, sans-serif;
            --font-accent: 'Poppins', sans-serif;
            --line-height-body: 1.7;
            --line-height-heading: 1.3;
        }

        body {
            font-family: var(--font-body);
            line-height: var(--line-height-body);
            color: var(--text-color);
            background-color: var(--bg-tertiary);
            margin: 0;
            padding: 0;
            font-size: 16px;
            background-image: linear-gradient(45deg, var(--bg-accent) 25%, transparent 25%, transparent 75%, var(--bg-accent) 75%, var(--bg-accent)),
                          linear-gradient(45deg, var(--bg-accent) 25%, transparent 25%, transparent 75%, var(--bg-accent) 75%, var(--bg-accent));
            background-size: 60px 60px;
            background-position: 0 0, 30px 30px;
        }

        /* Document container styles */
        .book-container {
            max-width: 1000px;
            margin: 2rem auto;
            padding: 0;
            position: relative;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: transparent;
        }

        /* Cover page styles */
        .cover-page {
            min-height: 29.7cm; /* A4 height */
            width: 21cm; /* A4 width */
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background: #1a1a2e; /* Solid background color */
            color: white;
            text-align: center;
            padding: 0;
            position: relative;
            overflow: hidden;
            margin: 2rem auto;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
            border: 1px solid var(--paper-border);
            position: relative;
        }

        /* Book spine effect */
        .cover-page::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 40px;
            height: 100%;
            background: linear-gradient(to right, #0a0a14 0%, #16213e 100%);
            box-shadow: inset -5px 0 10px rgba(0, 0, 0, 0.5);
            z-index: 1;
        }

        /* Book cover texture */
        .cover-page::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('data:image/png;base64,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');
            opacity: 0.05;
            z-index: 0;
        }

        .cover-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('screenshots/ScreenshotGlobal 4.png');
            background-size: cover;
            background-position: center;
            opacity: 0.2;
            z-index: 1;
            animation: pulse 8s infinite alternate;
        }

        @keyframes pulse {
            0% { opacity: 0.15; }
            100% { opacity: 0.25; }
        }

        .cover-content {
            position: relative;
            z-index: 2;
            max-width: 900px;
            padding: 3rem;
            margin: 2rem 2rem 2rem 5rem; /* Extra margin on left for spine */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 80%;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .cover-logo {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            color: white;
            text-shadow: 0 0 15px rgba(255, 107, 107, 0.8);
            animation: glow 3s infinite alternate;
            background: rgba(0, 0, 0, 0.2);
            width: 100px;
            height: 100px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.1);
            margin-bottom: 3rem;
        }

        @keyframes glow {
            0% { text-shadow: 0 0 15px rgba(255, 107, 107, 0.8); box-shadow: 0 0 20px rgba(255, 107, 107, 0.4); }
            100% { text-shadow: 0 0 30px rgba(255, 190, 11, 0.9); box-shadow: 0 0 40px rgba(255, 190, 11, 0.5); }
        }

        .cover-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: white;
            text-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            font-family: var(--font-heading);
            letter-spacing: -1px;
            line-height: 1.1;
            text-transform: uppercase;
            position: relative;
            padding-bottom: 1rem;
        }

        .cover-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 25%;
            width: 50%;
            height: 3px;
            background: var(--secondary-color);
        }

        .cover-subtitle {
            font-size: 1.8rem;
            font-weight: 300;
            margin-bottom: 2rem;
            color: rgba(255, 255, 255, 0.9);
            font-family: var(--font-accent);
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .cover-description {
            font-size: 1.2rem;
            max-width: 800px;
            margin: 0 auto 3rem;
            line-height: 1.8;
            font-family: var(--font-body);
            background: rgba(0, 0, 0, 0.2);
            padding: 1.5rem;
            border-radius: 5px;
            border-left: 3px solid var(--secondary-color);
        }

        .cover-cta {
            display: inline-block;
            padding: 1.2rem 2.5rem;
            background: linear-gradient(to right, #4a6cf7, #6a11cb);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            margin-top: 2rem;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }

        .cover-cta:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, #6a11cb, #4a6cf7);
            z-index: -1;
            transition: transform 0.6s;
            transform: scaleX(0);
            transform-origin: right;
        }

        .cover-cta:hover:before {
            transform: scaleX(1);
            transform-origin: left;
        }

        .cover-cta:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .cover-footer {
            position: absolute;
            bottom: 2rem;
            left: 0;
            width: 100%;
            text-align: center;
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            z-index: 2;
            font-family: var(--font-accent);
            padding: 1rem;
        }

        .cover-footer p {
            margin: 0.3rem 0;
        }

        .cover-footer p:last-child {
            font-size: 0.8rem;
            margin-top: 0.5rem;
        }

        .cover-contact {
            margin-top: 1rem;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.6);
        }

        /* Navigation styles */
        .presentation-nav {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            z-index: 100;
            display: flex;
            gap: 1rem;
        }

        .nav-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-button i,
        .nav-button [data-feather] {
            width: 20px;
            height: 20px;
            stroke-width: 2;
            color: var(--primary-color);
            stroke: var(--primary-color);
        }

        .nav-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        /* Responsive adjustments */
        /* Content Page Styles - A4 Paper Look */
        .content-page {
            min-height: 29.7cm; /* A4 height */
            width: 21cm; /* A4 width */
            padding: 2.5cm 2cm;
            margin: 2rem auto;
            position: relative;
            background: var(--paper-color);
            color: var(--text-color);
            counter-increment: page;
            box-shadow: 0 5px 25px var(--paper-shadow);
            border: 1px solid var(--paper-border);
            box-sizing: border-box;
        }

        /* Special styling for table of contents page */
        #table-of-contents.content-page {
            padding: 2cm 1.5cm;
        }

        /* Page number effect */
        .content-page::after {
            content: counter(page);
            position: absolute;
            bottom: 1cm;
            right: 1cm;
            font-family: var(--font-heading);
            font-size: 0.9rem;
            color: var(--text-light);
            font-style: italic;
        }

        /* Paper texture effect */
        .content-page:not(.cover-page)::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-image: url('data:image/png;base64,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');
            opacity: 0.03;
            pointer-events: none;
            z-index: -1;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--primary-color);
            position: relative;
            background: linear-gradient(90deg, var(--bg-accent) 0%, transparent 100%);
            padding: var(--spacing-md) var(--spacing-lg);
            margin-left: -var(--spacing-lg);
            margin-right: -var(--spacing-lg);
            border-radius: 5px;
        }

        /* Chapter marker */
        .page-header::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 5px;
            background-color: var(--chapter-marker);
        }

        .page-header h2 {
            font-family: var(--font-heading);
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-heading);
            margin: 0;
            letter-spacing: -0.5px;
            text-shadow: 1px 1px 0 rgba(255,255,255,0.8);
        }

        .page-indicator {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.2rem;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
            font-family: var(--font-accent);
        }

        /* Table of Contents Styles */
        .toc-container {
            column-count: 2;
            column-gap: 2rem;
            margin: 0;
            padding: 0;
        }

        .toc-section {
            break-inside: avoid;
            margin-bottom: 1rem;
            position: relative;
            padding-left: 0.5rem;
            border-left: 1px solid var(--primary-color);
        }

        .toc-section:nth-child(3n+1) {
            border-left-color: var(--primary-color);
        }

        .toc-section:nth-child(3n+2) {
            border-left-color: var(--secondary-color);
        }

        .toc-section:nth-child(3n+3) {
            border-left-color: var(--accent-color);
        }

        .toc-section h3 {
            font-family: var(--font-heading);
            color: var(--text-heading);
            font-size: 0.95rem;
            margin-top: 0;
            margin-bottom: 0.3rem;
            padding-bottom: 2px;
            border-bottom: 1px dotted var(--paper-edge);
            display: flex;
            align-items: center;
        }

        .toc-section h3 i {
            margin-right: 0.3rem;
            color: inherit;
            font-size: 0.8rem;
        }

        .toc-section:nth-child(3n+1) h3 i {
            color: var(--primary-color);
        }

        .toc-section:nth-child(3n+2) h3 i {
            color: var(--secondary-color);
        }

        .toc-section:nth-child(3n+3) h3 i {
            color: var(--accent-color);
        }

        .toc-section h4 {
            font-family: var(--font-accent);
            color: var(--text-heading);
            font-size: 0.8rem;
            margin-top: 0.3rem;
            margin-bottom: 0.1rem;
            font-weight: 600;
        }

        .toc-list {
            list-style: none;
            padding-left: 0.3rem;
            margin-bottom: 0.5rem;
            margin-top: 0.1rem;
            position: relative;
        }

        .toc-list li {
            margin-bottom: 0;
            position: relative;
            padding-left: 0.5rem;
            display: flex;
            align-items: baseline;
            font-size: 0.75rem;
            line-height: 1.4;
        }

        .toc-list li::before {
            content: '·';
            position: absolute;
            left: 0;
            font-size: 0.8rem;
            line-height: 1;
        }

        .toc-section:nth-child(3n+1) .toc-list li::before {
            color: var(--primary-color);
        }

        .toc-section:nth-child(3n+2) .toc-list li::before {
            color: var(--secondary-color);
        }

        .toc-section:nth-child(3n+3) .toc-list li::before {
            color: var(--accent-color);
        }

        .toc-list a {
            color: var(--text-color);
            text-decoration: none;
            transition: all 0.2s ease;
            flex-grow: 1;
            display: flex;
            align-items: baseline;
            font-family: var(--font-body);
        }

        .toc-list a {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .toc-list a::after {
            content: '';
            flex-grow: 1;
            height: 1px;
            background-image: linear-gradient(to right, var(--text-light) 33%, transparent 0%);
            background-position: bottom;
            background-size: 3px 1px;
            background-repeat: repeat-x;
            margin: 0 0.5rem;
        }

        .toc-list a span {
            color: var(--text-light);
            font-family: var(--font-accent);
            font-weight: 600;
            font-size: 0.75rem;
            background-color: var(--bg-secondary);
            padding: 0.1rem 0.4rem;
            border-radius: 3px;
            min-width: 1.5rem;
            text-align: center;
            display: inline-block;
            order: 2;
        }

        .toc-section:nth-child(3n+1) .toc-list a:hover {
            color: var(--primary-color);
        }

        .toc-section:nth-child(3n+2) .toc-list a:hover {
            color: var(--secondary-color);
        }

        .toc-section:nth-child(3n+3) .toc-list a:hover {
            color: var(--accent-color);
        }

        .page-content {
            max-width: 1100px;
            margin: 0 auto;
            position: relative;
            counter-reset: section;
        }

        .page-content h3 {
            font-family: var(--font-heading);
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text-heading);
            margin-top: var(--spacing-xl);
            margin-bottom: var(--spacing-md);
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: 5px;
            position: relative;
            counter-increment: section;
            background: linear-gradient(90deg, var(--bg-accent) 0%, transparent 100%);
            border-left: 5px solid var(--primary-color);
        }

        .page-content h3::before {
            content: counter(section) ".";
            margin-right: var(--spacing-xs);
            color: var(--primary-color);
            font-weight: 900;
        }

        .page-content h4 {
            font-family: var(--font-heading);
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--text-heading);
            margin-top: var(--spacing-lg);
            margin-bottom: var(--spacing-sm);
            border-bottom: 2px solid var(--bg-accent-secondary);
            padding-bottom: var(--spacing-xs);
            display: inline-block;
        }

        .page-content h5 {
            font-family: var(--font-accent);
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--secondary-color);
            margin-top: var(--spacing-md);
            margin-bottom: var(--spacing-xs);
            position: relative;
            padding-left: var(--spacing-md);
        }

        .page-content h5::before {
            content: '•';
            position: absolute;
            left: 0;
            color: var(--secondary-color);
        }

        .page-content p {
            font-size: 1.05rem;
            line-height: var(--line-height-body);
            margin-bottom: var(--spacing-md);
            text-align: justify;
            hyphens: auto;
        }

        /* Only apply drop cap to the first paragraph of the first section */
        .content-page:nth-of-type(3) .page-content p:first-of-type:first-letter {
            font-family: var(--font-heading);
            font-size: 3.5em;
            font-weight: 700;
            color: var(--primary-color);
            float: left;
            line-height: 0.8;
            margin-right: 0.2em;
            margin-top: 0.1em;
            padding: 0.1em 0.2em;
            background-color: var(--bg-accent);
            border-radius: 5px;
        }

        /* Audience Grid */
        .audience-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin-bottom: var(--spacing-lg);
        }

        .audience-item {
            background-color: var(--bg-secondary);
            padding: 1rem;
            border-radius: 5px;
            border-left: 3px solid var(--primary-color);
        }

        .audience-item:nth-child(2) {
            border-left-color: var(--secondary-color);
        }

        .audience-item:nth-child(3) {
            border-left-color: var(--accent-color);
        }

        .audience-item:nth-child(4) {
            border-left-color: var(--primary-light);
        }

        .audience-item h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
            color: var(--text-heading);
        }

        .audience-item p {
            margin: 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .page-content ul, .page-content ol {
            margin-bottom: var(--spacing-md);
            padding-left: var(--spacing-lg);
        }

        .page-content li {
            margin-bottom: var(--spacing-xs);
        }

        .page-content blockquote {
            font-family: var(--font-heading);
            font-style: italic;
            border-left: 3px solid var(--secondary-color);
            padding: var(--spacing-md) var(--spacing-lg);
            margin: var(--spacing-lg) 0;
            background-color: var(--bg-accent-secondary);
            position: relative;
            border-radius: 0 8px 8px 0;
            box-shadow: 0 3px 10px rgba(0,0,0,0.05);
        }

        .page-content blockquote::before {
            content: '"';
            font-size: 4rem;
            position: absolute;
            left: var(--spacing-xs);
            top: -20px;
            color: var(--secondary-color);
            opacity: 0.2;
        }

        .page-content code {
            font-family: 'Courier New', monospace;
            background-color: var(--bg-secondary);
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 0.9em;
        }

        .page-content pre {
            background-color: var(--bg-secondary);
            padding: var(--spacing-md);
            border-radius: 5px;
            overflow-x: auto;
            margin-bottom: var(--spacing-md);
            border-left: 3px solid var(--secondary-color);
        }

        .page-footer {
            margin-top: var(--spacing-xl);
            padding-top: var(--spacing-md);
            border-top: 1px solid var(--paper-edge);
            font-family: var(--font-accent);
            font-size: 0.9rem;
            color: var(--text-light);
            background: linear-gradient(90deg, transparent 0%, var(--bg-accent) 100%);
            padding: var(--spacing-md);
            border-radius: 5px;
        }

        .page-nav {
            display: flex;
            justify-content: space-between;
        }

        .page-nav-link {
            display: inline-flex;
            align-items: center;
            padding: var(--spacing-xs) var(--spacing-md);
            color: white;
            background-color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border-radius: 30px;
            font-family: var(--font-accent);
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }

        .page-nav-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
            background-color: var(--primary-light);
        }

        .page-nav-link i,
        .page-nav-link [data-feather] {
            margin: 0 var(--spacing-xs);
            width: 16px;
            height: 16px;
            stroke-width: 2;
            vertical-align: middle;
        }

        /* Executive Summary Styles */
        .executive-summary-grid {
            display: grid;
            grid-template-columns: 3fr 2fr;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .summary-highlights {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .highlight-card {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .highlight-card:hover {
            transform: translateY(-5px);
        }

        .highlight-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .highlight-icon i,
        .highlight-icon [data-feather] {
            width: 28px;
            height: 28px;
            stroke: var(--primary-color);
            color: var(--primary-color);
            stroke-width: 2;
        }

        .highlight-content h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .key-metrics {
            margin-top: 3rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .metric-item {
            text-align: center;
            padding: 1.5rem;
            background: linear-gradient(135deg, rgba(74, 108, 247, 0.1) 0%, rgba(74, 108, 247, 0.2) 100%);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .metric-item:hover {
            transform: translateY(-5px);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .metric-label {
            font-size: 1rem;
            color: var(--text-secondary);
        }

        /* Market Analysis Styles */
        .market-stats-container {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .market-stat {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .market-stat:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .challenges-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .challenge-item {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .challenge-item:hover {
            transform: translateY(-5px);
        }

        .challenge-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .challenge-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .challenge-icon i,
        .challenge-icon [data-feather] {
            width: 24px;
            height: 24px;
            stroke: var(--primary-color);
            color: var(--primary-color);
            stroke-width: 2;
        }

        .challenge-header h4 {
            margin: 0;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .solution {
            margin-top: 1rem;
            padding: 1rem;
            background: rgba(74, 108, 247, 0.1);
            border-radius: 5px;
            font-size: 0.95rem;
        }

        .market-segments {
            display: flex;
            flex-direction: column;
            margin: 1.5rem 0;
            background: var(--bg-secondary);
            padding: 1.5rem;
            border-radius: 10px;
            border-left: 4px solid var(--primary-color);
        }

        .segment {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-bottom: 1px solid var(--paper-edge);
            margin-bottom: 1rem;
        }

        .segment-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .segment-icon i,
        .segment-icon [data-feather] {
            width: 30px;
            height: 30px;
            stroke: var(--primary-color);
            color: var(--primary-color);
            stroke-width: 2;
        }

        .segment-content h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .segment-value {
            display: inline-block;
            margin-left: auto;
            padding: 0.3rem 0.8rem;
            background: rgba(74, 108, 247, 0.1);
            color: var(--primary-color);
            border-radius: 50px;
            font-weight: 600;
            font-size: 0.8rem;
        }

        /* Product Showcase Styles */
        .tab-navigation {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding-bottom: 1rem;
        }

        .tab-button {
            padding: 0.75rem 1.5rem;
            background: none;
            border: none;
            color: var(--text-secondary);
            font-size: 1.1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-button:after {
            content: '';
            position: absolute;
            bottom: -1rem;
            left: 0;
            width: 100%;
            height: 3px;
            background: var(--primary-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .tab-button.active {
            color: var(--primary-color);
        }

        .tab-button.active:after {
            transform: scaleX(1);
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .showcase-item {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-bottom: 3rem;
            align-items: center;
        }

        .showcase-item.reverse {
            grid-template-columns: 1fr 1fr;
            direction: rtl;
        }

        .showcase-item.reverse .showcase-description {
            direction: ltr;
        }

        .showcase-image {
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .showcase-image img {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.5s ease;
        }

        .showcase-item:hover .showcase-image img {
            transform: scale(1.05);
        }

        .showcase-description h4 {
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 1rem;
        }

        .feature-list {
            list-style: none;
            padding: 0;
            margin: 1.5rem 0;
        }

        .feature-list li {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 0.75rem;
        }

        .feature-list li i {
            color: var(--primary-color);
            margin-top: 0.25rem;
        }

        .business-value {
            margin-top: 1.5rem;
            padding: 1rem;
            background: rgba(74, 108, 247, 0.1);
            border-radius: 5px;
            font-size: 0.95rem;
        }

        /* Business Model Styles */
        .revenue-streams {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .revenue-stream-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .revenue-stream-item:hover {
            transform: translateY(-5px);
        }

        .revenue-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .revenue-icon i,
        .revenue-icon [data-feather] {
            width: 30px;
            height: 30px;
            stroke: var(--primary-color);
            color: var(--primary-color);
            stroke-width: 2;
        }

        .revenue-content h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .revenue-metrics {
            display: flex;
            gap: 1.5rem;
            margin-top: 1rem;
        }

        .metric {
            display: flex;
            flex-direction: column;
        }

        .metric-label {
            font-size: 0.85rem;
            color: var(--text-secondary);
        }

        .metric-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-color);
        }

        .projections-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .projection-item {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .projection-item:hover {
            transform: translateY(-5px);
        }

        .projection-item h4 {
            margin-top: 0;
            margin-bottom: 1rem;
            color: var(--primary-color);
            font-size: 1.3rem;
            text-align: center;
        }

        .projection-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .projection-metric {
            display: flex;
            flex-direction: column;
            margin-bottom: 0.75rem;
        }

        .investment-allocation {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .allocation-item {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .allocation-item:hover {
            transform: translateY(-5px);
        }

        .allocation-percentage {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .allocation-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .allocation-description {
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .returns-list {
            list-style: none;
            padding: 0;
            margin: 1.5rem 0;
        }

        .returns-list li {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .returns-list li i {
            color: var(--primary-color);
            margin-top: 0.25rem;
        }

        /* Contact Styles */
        .contact-details {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
            margin: 2rem 0;
            background: var(--bg-secondary);
            padding: 2rem;
            border-radius: 10px;
            border-left: 4px solid var(--primary-color);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 0.5rem;
            border-bottom: 1px dotted var(--paper-edge);
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .contact-icon i,
        .contact-icon [data-feather] {
            width: 30px;
            height: 30px;
            stroke: var(--primary-color);
            color: var(--primary-color);
            stroke-width: 2;
        }

        .contact-info {
            text-align: center;
            padding: 0.5rem;
        }

        .contact-info h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
            font-size: 1rem;
            font-weight: 500;
        }

        .contact-info p {
            margin: 0;
            font-size: 0.9rem;
            color: var(--text-secondary);
        }

        .contact-info a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
            font-size: 0.9rem;
        }

        .contact-info a:hover {
            text-decoration: underline;
        }

        .contact-intro {
            margin-bottom: 2rem;
            position: relative;
        }

        .contact-intro h3,
        .cta-section h3 {
            display: flex;
            align-items: center;
            color: var(--primary-color);
            font-size: 1.4rem;
            font-weight: 600;
        }

        .cta-section {
            text-align: center;
            margin: 3rem 0;
            padding: 2rem;
            background: rgba(74, 108, 247, 0.1);
            border-radius: 10px;
            border-left: 4px solid var(--primary-color);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .cta-section h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
            justify-content: center;
        }

        .cta-section p {
            font-size: 1rem;
            max-width: 700px;
            margin: 0 auto 1.5rem;
            color: var(--text-color);
        }

        .cta-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .contact-info-box {
            background: rgba(74, 108, 247, 0.05);
            border-left: 3px solid var(--primary-color);
            padding: 1rem 1.5rem;
            margin: 1rem 0;
            border-radius: 5px;
        }

        .contact-info-box p {
            margin: 0.5rem 0;
        }

        .contact-info-box a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .contact-info-box a:hover {
            text-decoration: underline;
        }

        .cta-button {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            margin: 0 0.5rem;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
        }

        .cta-button.secondary {
            background: transparent;
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        /* Responsive Styles */
        @media (max-width: 1200px) {
            .executive-summary-grid,
            .showcase-item,
            .showcase-item.reverse {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .showcase-item.reverse {
                direction: ltr;
            }

            .metrics-grid,
            .market-stats-container,
            .market-segments,
            .revenue-streams,
            .projections-grid,
            .investment-allocation,
            .contact-details {
                grid-template-columns: repeat(2, 1fr);
            }

            .challenges-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Print styles for PDF conversion */
        @media print {
            /* Preserve exact appearance */
            * {
                -webkit-print-color-adjust: exact !important; /* Chrome, Safari */
                print-color-adjust: exact !important; /* Standard */
            }
            body {
                background-color: white;
                background-image: none;
                margin: 0;
                padding: 0;
                font-size: 11pt; /* Standard document font size */
                line-height: 1.5; /* Improved line spacing for readability */
            }

            .book-container {
                box-shadow: none;
                max-width: 100%;
                margin: 0;
                padding: 0;
            }

            .presentation-nav {
                display: none;
            }

            .content-page {
                page-break-after: always;
                min-height: auto;
                padding: 0; /* Remove padding as margins are set in @page */
                margin: 0;
                box-shadow: none;
                border: none;
                width: 100%;
                box-sizing: border-box;
                orphans: 3; /* Prevent single lines at bottom of page */
                widows: 3; /* Prevent single lines at top of page */
            }

            /* Table of contents page needs special handling */
            #table-of-contents {
                page-break-before: always; /* Ensure it starts on a new page after cover */
            }

            .page-content {
                max-width: 1100px;
                margin: 0 auto;
                position: relative;
                counter-reset: section;
            }

            .page-content h3 {
                font-family: var(--font-heading);
                font-size: 1.8rem;
                font-weight: 700;
                color: var(--text-heading);
                margin-top: var(--spacing-xl);
                margin-bottom: var(--spacing-md);
                padding: var(--spacing-sm) var(--spacing-md);
                border-radius: 5px;
                position: relative;
                counter-increment: section;
                background: linear-gradient(90deg, var(--bg-accent) 0%, transparent 100%);
                border-left: 5px solid var(--primary-color);
            }

            .page-content h3::before {
                content: counter(section) ".";
                margin-right: var(--spacing-xs);
                color: var(--primary-color);
                font-weight: 900;
            }

            .page-content h4 {
                font-family: var(--font-heading);
                font-size: 1.4rem;
                font-weight: 600;
                color: var(--text-heading);
                margin-top: var(--spacing-lg);
                margin-bottom: var(--spacing-sm);
                border-bottom: 2px solid var(--bg-accent-secondary);
                padding-bottom: var(--spacing-xs);
                display: inline-block;
            }

            .page-content h5 {
                font-family: var(--font-accent);
                font-size: 1.2rem;
                font-weight: 600;
                color: var(--secondary-color);
                margin-top: var(--spacing-md);
                margin-bottom: var(--spacing-xs);
                position: relative;
                padding-left: var(--spacing-md);
            }

            .page-content h5::before {
                content: '•';
                position: absolute;
                left: 0;
                color: var(--secondary-color);
            }

            .page-content p {
                font-size: 1.05rem;
                line-height: var(--line-height-body);
                margin-bottom: var(--spacing-md);
                text-align: justify;
                hyphens: auto;
            }

            /* Only apply drop cap to the first paragraph of the first section */
            .content-page:nth-of-type(3) .page-content p:first-of-type:first-letter {
                font-family: var(--font-heading);
                font-size: 3.5em;
                font-weight: 700;
                color: var(--primary-color);
                float: left;
                line-height: 0.8;
                margin-right: 0.2em;
                margin-top: 0.1em;
                padding: 0.1em 0.2em;
                background-color: var(--bg-accent);
                border-radius: 5px;
            }

            /* Audience Grid */
            .audience-grid {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 1.5rem;
                margin-bottom: var(--spacing-lg);
            }

            .audience-item {
                background-color: var(--bg-secondary);
                padding: 1rem;
                border-radius: 5px;
                border-left: 3px solid var(--primary-color);
            }

            .audience-item:nth-child(2) {
                border-left-color: var(--secondary-color);
            }

            .audience-item:nth-child(3) {
                border-left-color: var(--accent-color);
            }

            .audience-item:nth-child(4) {
                border-left-color: var(--primary-light);
            }

            .audience-item h4 {
                margin-top: 0;
                margin-bottom: 0.5rem;
                font-size: 1.1rem;
                color: var(--text-heading);
            }

            .audience-item p {
                margin: 0;
                font-size: 0.9rem;
                line-height: 1.4;
            }

            .page-content ul, .page-content ol {
                margin-bottom: var(--spacing-md);
                padding-left: var(--spacing-lg);
            }

            .page-content li {
                margin-bottom: var(--spacing-xs);
            }

            .page-content blockquote {
                font-family: var(--font-heading);
                font-style: italic;
                border-left: 3px solid var(--secondary-color);
                padding: var(--spacing-md) var(--spacing-lg);
                margin: var(--spacing-lg) 0;
                background-color: var(--bg-accent-secondary);
                position: relative;
                border-radius: 0 8px 8px 0;
                box-shadow: 0 3px 10px rgba(0,0,0,0.05);
            }

            .page-content blockquote::before {
                content: '"';
                font-size: 4rem;
                position: absolute;
                left: var(--spacing-xs);
                top: -20px;
                color: var(--secondary-color);
                opacity: 0.2;
            }

            .page-content code {
                font-family: 'Courier New', monospace;
                background-color: var(--bg-secondary);
                padding: 2px 5px;
                border-radius: 3px;
                font-size: 0.9em;
            }

            .page-content pre {
                background-color: var(--bg-secondary);
                padding: var(--spacing-md);
                border-radius: 5px;
                overflow-x: auto;
                margin-bottom: var(--spacing-md);
                border-left: 3px solid var(--secondary-color);
            }

            .page-footer {
                margin-top: var(--spacing-xl);
                padding-top: var(--spacing-md);
                border-top: 1px solid var(--paper-edge);
                font-family: var(--font-accent);
                font-size: 0.9rem;
                color: var(--text-light);
                background: linear-gradient(90deg, transparent 0%, var(--bg-accent) 100%);
                padding: var(--spacing-md);
                border-radius: 5px;
            }

            .page-nav {
                display: flex;
                justify-content: space-between;
            }

            .page-nav-link {
                display: inline-flex;
                align-items: center;
                padding: var(--spacing-xs) var(--spacing-md);
                color: white;
                background-color: var(--primary-color);
                text-decoration: none;
                font-weight: 500;
                transition: all 0.3s ease;
                border-radius: 30px;
                font-family: var(--font-accent);
                box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            }

            .page-nav-link:hover {
                transform: translateY(-3px);
                box-shadow: 0 5px 15px rgba(0,0,0,0.15);
                background-color: var(--primary-light);
            }

            .page-nav-link i,
            .page-nav-link [data-feather] {
                margin: 0 var(--spacing-xs);
                width: 16px;
                height: 16px;
                stroke-width: 2;
                vertical-align: middle;
            }

            /* Executive Summary Styles */
            .executive-summary-grid {
                display: grid;
                grid-template-columns: 3fr 2fr;
                gap: 2rem;
                margin-bottom: 2rem;
            }

            .summary-highlights {
                display: grid;
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .highlight-card {
                display: flex;
                align-items: flex-start;
                gap: 1rem;
                padding: 1.5rem;
                background: rgba(255, 255, 255, 0.7);
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
                transition: transform 0.3s ease;
            }

            .highlight-card:hover {
                transform: translateY(-5px);
            }

            .highlight-icon {
                width: 40px;
                height: 40px;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .highlight-icon i,
            .highlight-icon [data-feather] {
                width: 28px;

                margin-top: 0.5cm; /* Add space at the top of content */
            }

            .cover-page {
                height: auto;
                min-height: 29.7cm; /* A4 height */
                margin: 0;
                box-shadow: none;
                border: none;
                width: 100%;
                background: #1a1a2e !important; /* Ensure background color is preserved */
                print-color-adjust: exact; /* Force background color in print */
                -webkit-print-color-adjust: exact; /* For Safari */
            }

            .cover-overlay {
                opacity: 0.1 !important; /* Adjust overlay for better print visibility */
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }

            .cover-page::before {
                display: none; /* Remove book spine effect in PDF */
            }

            .page-nav {
                display: none; /* Hide navigation in PDF */
            }

            a {
                text-decoration: none;
                color: inherit;
            }

            /* Ensure all images are properly displayed */
            img {
                max-width: 100%;
                page-break-inside: avoid;
                margin: 0.5cm 0; /* Add space around images */
            }

            /* Proper heading formatting */
            h1, h2, h3, h4, h5, h6 {
                page-break-after: avoid; /* Don't break page after headings */
                page-break-inside: avoid; /* Don't split headings */
                break-after: avoid; /* Modern browsers */
                break-inside: avoid; /* Modern browsers */
            }

            /* Ensure headings don't appear at bottom of page */
            h1, h2 {
                margin-top: 1.5cm; /* Space before major headings */
                margin-bottom: 0.5cm; /* Space after major headings */
                page-break-before: always; /* Major headings start on new page */
                break-before: always; /* Modern browsers */
            }

            /* Force specific headings to start on a new page */
            .target-market h3, .market-challenges h3 {
                page-break-before: always; /* Force new page */
                break-before: always; /* Modern browsers */
                margin-top: 1.5cm; /* Space before heading */
            }

            h3 {
                margin-top: 1cm; /* Space before section headings */
                margin-bottom: 0.4cm; /* Space after section headings */
            }

            h4, h5, h6 {
                margin-top: 0.8cm; /* Space before subsection headings */
                margin-bottom: 0.3cm; /* Space after subsection headings */
            }

            /* Ensure tables don't break across pages */
            table {
                page-break-inside: avoid;
                break-inside: avoid;
                margin: 0.5cm 0; /* Add space around tables */
            }

            /* Ensure content doesn't get cut off */
            p, blockquote, ul, ol, dl, pre {
                page-break-inside: avoid;
                break-inside: avoid-page;
                orphans: 3; /* Prevent single lines at bottom of page */
                widows: 3; /* Prevent single lines at top of page */
            }

            /* Add space between paragraphs */
            p {
                margin-bottom: 0.3cm;
            }

            /* Ensure list items stay together */
            li {
                page-break-inside: avoid;
                break-inside: avoid;
            }

            /* Add space between list items */
            li + li {
                margin-top: 0.2cm;
            }

            /* Ensure proper spacing for lists */
            ul, ol {
                margin: 0.3cm 0;
                padding-left: 1cm;
            }

            /* Reset drop cap for PDF */
            .content-page:nth-of-type(3) .page-content p:first-of-type:first-letter {
                font-size: inherit;
                font-weight: inherit;
                color: inherit;
                float: none;
                margin: 0;
                padding: 0;
                background-color: transparent;
            }

            /* Ensure section dividers don't break */
            .section-divider {
                page-break-inside: avoid;
                break-inside: avoid;
                margin: 0.5cm 0;
            }

            /* Format blockquotes properly */
            blockquote {
                margin: 0.5cm 1cm;
                padding: 0.3cm;
                border-left: 3pt solid #4a6cf7;
                background-color: #f8f9fa;
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }

            /* Ensure proper page header spacing */
            .page-header {
                margin-bottom: 1cm;
            }

            /* Ensure cards and grids format properly */
            .card, .grid-item, .highlight-card, .metric-item, .audience-item, .challenge-item, .segment {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 0.4cm;
                display: block;
                visibility: visible;
                opacity: 1;
            }

            /* Preserve grid layouts */
            .grid, .card-grid, .metrics-grid, .audience-grid, .challenges-grid, .segments-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
                margin-bottom: 1cm;
            }

            /* Product Showcase specific styles for printing */
            /* Tab navigation styles */

            /* Show all tab content in print */
            .tab-pane {
                display: block !important;
                opacity: 1 !important;
                visibility: visible !important;
                margin-bottom: 2cm; /* Add space between tab sections */
                page-break-after: always; /* Force page break after each tab section */
            }

            /* Keep tab navigation visible but styled for print */
            .tab-navigation {
                display: flex !important;
                justify-content: center;
                margin-bottom: 1cm;
                page-break-after: avoid;
                break-after: avoid;
            }

            .tab-button {
                background-color: #f0f0f0 !important;
                color: #333 !important;
                border: 1px solid #ddd !important;
                padding: 0.5rem 1rem;
                margin: 0 0.25rem;
                border-radius: 4px;
                font-weight: bold;
            }

            .tab-button.active {
                background-color: #4a6cf7 !important;
                color: white !important;
                border-color: #4a6cf7 !important;
            }

            .showcase-item {
                page-break-inside: avoid; /* Keep showcase items together if possible */
                break-inside: avoid;
                margin-bottom: 1cm; /* Add space between showcase items */
                display: grid;
                grid-template-columns: 1fr 1fr; /* Match the original grid layout */
                gap: 2rem;
                align-items: center;
            }

            .showcase-item.reverse {
                direction: rtl; /* Match the original RTL direction */
            }

            .showcase-item.reverse .showcase-description {
                direction: ltr; /* Reset direction for the description */
            }

            .showcase-description {
                width: 100%; /* Full width within its grid cell */
                margin-bottom: 0;
            }

            .showcase-image {
                width: 100%; /* Full width within its grid cell */
                margin-bottom: 0;
                page-break-inside: avoid; /* Don't split the image */
                break-inside: avoid;
            }

            .showcase-image img {
                max-width: 100%;
                height: auto;
                display: block;
                margin: 0 auto;
                object-fit: contain; /* Maintain aspect ratio */
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Preserve shadow effect */
                border-radius: 8px; /* Preserve rounded corners */
            }

            /* Special handling for mobile view image to limit its size */
            #mobile-tab .showcase-image img {
                max-height: 16cm; /* Limit height to fit on at most 2 pages */
                object-fit: cover; /* Show only part of the image if needed */
                object-position: top; /* Show the top part of the image */
            }

            /* Additional showcase styles */

            /* Ensure all feature lists print properly */
            .feature-list {
                break-inside: avoid; /* Keep feature lists together */
                page-break-inside: avoid;
                margin: 0.3cm 0;
            }

            /* Ensure business value sections print properly */
            .business-value {
                break-inside: avoid; /* Keep business value sections together */
                page-break-inside: avoid;
                margin-top: 0.3cm;
                padding: 0.2cm;
                background-color: #f8f9fa;
                border-left: 3pt solid #4a6cf7;
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }

            /* Business model specific styles */
            .revenue-streams, .projections-grid, .investment-allocation {
                display: block; /* Convert to block for better print layout */
            }

            .revenue-stream-item, .projection-item, .allocation-item {
                page-break-inside: avoid;
                break-inside: avoid;
                margin-bottom: 0.5cm;
            }
        }

        /* PDF-specific styles */
        @page {
            size: A4;
            margin: 2.5cm 2cm 2cm 2cm; /* Increased top margin, reduced side margins */
        }

        /* Remove margins for cover page */
        @page :first {
            margin: 0;
        }

        /* Remove header and footer with directory and time */
        @page {
            @top-left { content: none; }
            @top-center { content: none; }
            @top-right { content: none; }
            @bottom-left { content: none; }
            @bottom-center { content: none; }
            @bottom-right { content: none; }
        }

        /* Button styles */
        .print-button:hover {
            background-color: #3a4dc3 !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
            transition: all 0.3s ease;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            :root {
                --spacing-xl: 2rem;
                --spacing-lg: 1.5rem;
                --spacing-md: 1rem;
                --spacing-sm: 0.75rem;
                --spacing-xs: 0.5rem;
            }

            .cover-title {
                font-size: 2.5rem;
            }

            .cover-subtitle {
                font-size: 1.4rem;
            }

            .cover-description {
                font-size: 1rem;
            }

            .page-header h2 {
                font-size: 2rem;
            }

            .page-header::before {
                transform: translateX(-10px);
            }

            .metrics-grid,
            .market-stats-container,
            .market-segments,
            .revenue-streams,
            .projections-grid,
            .investment-allocation,
            .contact-details {
                grid-template-columns: 1fr;
            }

            .toc-container {
                column-count: 1;
            }

            .tab-navigation {
                flex-wrap: wrap;
            }

            .tab-button {
                padding: 0.5rem 1rem;
                font-size: 1rem;
            }

            .cover-content {
                padding: 2rem 1rem;
                max-width: 90%;
            }

            .content-page {
                padding: var(--spacing-md);
                width: 100%;
                min-height: auto;
                margin: 1rem 0;
                box-sizing: border-box;
            }

            .page-content p {
                text-align: left;
                hyphens: none;
            }

            .audience-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .page-content h3 {
                font-size: 1.6rem;
                padding: var(--spacing-xs) var(--spacing-sm);
            }

            .page-content h4 {
                font-size: 1.3rem;
            }

            .page-content ul, .page-content ol {
                padding-left: var(--spacing-md);
            }

            pre {
                max-width: 100%;
                overflow-x: auto;
                white-space: pre-wrap;
                word-wrap: break-word;
            }

            code {
                font-size: 0.85rem;
            }

            .presentation-nav {
                display: none;
            }
        }
    </style>
</head>
<body>
    <script>
        // Initialize Feather Icons as soon as possible
        if (typeof feather !== 'undefined') {
            feather.replace();
        }
    </script>
    <div class="book-container">
        <!-- Cover Page -->
        <section class="cover-page" id="cover">
            <div class="cover-overlay"></div>
            <div class="cover-content">
                <div class="cover-logo">
                    <i data-feather="globe"></i>
                </div>
                <h1 class="cover-title">TransLogix Global</h1>
                <h2 class="cover-subtitle">Documentation</h2>
                <p class="cover-description">
                    A comprehensive logistics and shipment tracking platform designed to streamline operations,
                    enhance customer experience, and drive business growth in the global shipping industry.
                </p>
                <a href="#table-of-contents" class="cover-cta">View Documentation <i class="fas fa-arrow-right"></i></a>
            </div>
            <div class="cover-footer">
                <p>Version 1.0 | 2025</p>
                <p>Developed by Tembi Kelvin (defabrika)</p>
                <div class="cover-contact">Contact: +237 676 428 445 | Email: <EMAIL></div>
            </div>
        </section>

        <!-- Table of Contents -->
        <section class="content-page" id="table-of-contents">
            <div class="page-header">
                <h2>Table of Contents</h2>
                <div class="page-indicator"><i class="fas fa-list-ul"></i></div>
            </div>

            <div class="page-content">
                <div class="toc-container">
                    <div class="toc-section">
                        <h3><i class="fas fa-clipboard"></i> Executive Summary</h3>
                        <ul class="toc-list">
                            <li><a href="#executive-summary">Overview<span>1</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-chart-line"></i> Market Analysis</h3>
                        <ul class="toc-list">
                            <li><a href="#market-analysis">Market Overview<span>2</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-book"></i> Introduction</h3>
                        <ul class="toc-list">
                            <li><a href="#introduction">Project Overview<span>3</span></a></li>
                            <li><a href="#introduction">Purpose and Goals<span>3</span></a></li>
                            <li><a href="#introduction">Target Audience<span>4</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-list"></i> Features</h3>
                        <h4>Public Website</h4>
                        <ul class="toc-list">
                            <li><a href="#features">Responsive Landing Page<span>5</span></a></li>
                            <li><a href="#features">Shipment Tracking with Interactive Map<span>5</span></a></li>
                            <li><a href="#features">Detailed Tracking Timeline<span>6</span></a></li>
                            <li><a href="#features">Light and Dark Theme Support<span>6</span></a></li>
                        </ul>

                        <h4>Admin CMS</h4>
                        <ul class="toc-list">
                            <li><a href="#features">Secure Login System<span>7</span></a></li>
                            <li><a href="#features">Dashboard with Shipment Statistics<span>7</span></a></li>
                            <li><a href="#features">Shipment Management<span>8</span></a></li>
                            <li><a href="#features">Tracking Updates Management<span>8</span></a></li>
                            <li><a href="#features">User Management<span>9</span></a></li>
                            <li><a href="#features">System Settings Customization<span>9</span></a></li>
                            <li><a href="#features">Maintenance Tools<span>10</span></a></li>
                            <li><a href="#features">Alert System<span>11</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-check-square"></i> Requirements</h3>
                        <h4>Software Requirements</h4>
                        <ul class="toc-list">
                            <li><a href="#requirements">PHP Version<span>11</span></a></li>
                            <li><a href="#requirements">MySQL Version<span>11</span></a></li>
                            <li><a href="#requirements">Web Server<span>11</span></a></li>
                            <li><a href="#requirements">PHP Extensions<span>12</span></a></li>
                        </ul>

                        <h4>Hardware Requirements</h4>
                        <ul class="toc-list">
                            <li><a href="#requirements">Minimum Specifications<span>12</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-download"></i> Installation</h3>
                        <ul class="toc-list">
                            <li><a href="#installation">Prerequisites<span>13</span></a></li>
                            <li><a href="#installation">Step-by-Step Instructions<span>13</span></a></li>
                            <li><a href="#installation">Configuration<span>14</span></a></li>
                            <li><a href="#installation">Troubleshooting<span>14</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-wrench"></i> Configuration</h3>
                        <ul class="toc-list">
                            <li><a href="#configuration-setup">Configuration Setup<span>15</span></a></li>
                            <li><a href="#configuration-setup">Troubleshooting<span>15</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-laptop"></i> Product Showcase</h3>
                        <ul class="toc-list">
                            <li><a href="#product-showcase">Platform Overview<span>16</span></a></li>
                            <li><a href="#product-showcase">Shipment Tracking<span>16</span></a></li>
                            <li><a href="#product-showcase">User Dashboard<span>17</span></a></li>
                            <li><a href="#product-showcase">Admin Interface<span>17</span></a></li>
                            <li><a href="#product-showcase">Mobile Experience<span>18</span></a></li>
                            <li><a href="#product-showcase">Key Pages<span>18</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-chart-line"></i> Business Model</h3>
                        <ul class="toc-list">
                            <li><a href="#business-model">Revenue Model<span>19</span></a></li>
                            <li><a href="#business-model">Financial Projections<span>20</span></a></li>
                            <li><a href="#business-model">Investment Opportunity<span>20</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-bug"></i> Troubleshooting</h3>
                        <ul class="toc-list">
                            <li><a href="#installation-troubleshooting">Installation Issues<span>21</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-users"></i> Credits</h3>
                        <ul class="toc-list">
                            <li><a href="#credits">Author Information<span>33</span></a></li>
                            <li><a href="#credits">Contributors<span>33</span></a></li>
                        </ul>
                    </div>

                    <div class="toc-section">
                        <h3><i class="fas fa-envelope"></i> Contact</h3>
                        <ul class="toc-list">
                            <li><a href="#contact">Contact Information<span>34</span></a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#cover" class="page-nav-link"><i data-feather="arrow-left"></i> Cover</a>
                    <a href="#executive-summary" class="page-nav-link">Executive Summary <i data-feather="arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Executive Summary Page -->
        <section class="content-page" id="executive-summary">
            <div class="page-header">
                <h2>Executive Summary</h2>
                <div class="page-indicator">1</div>
            </div>

            <div class="page-content">
                <div class="executive-summary-grid">
                    <div class="summary-text">
                        <h3>The Opportunity</h3>
                        <p>
                            The global logistics market is projected to reach <strong>$12.68 trillion by 2025</strong>, growing at a CAGR of 6.5%.
                            Despite this growth, the industry faces significant challenges in transparency, efficiency, and customer experience.
                        </p>
                        <p>
                            TransLogix Global addresses these challenges with a comprehensive logistics management platform that provides
                            real-time tracking, streamlined operations, and enhanced customer engagement for businesses of all sizes.
                        </p>

                        <h3>Our Solution</h3>
                        <p>
                            TransLogix Global is a state-of-the-art logistics management system that combines powerful tracking capabilities,
                            intuitive user interfaces, and advanced analytics to transform how businesses manage their shipping operations.
                        </p>
                        <p>
                            Our platform offers a complete solution for logistics companies, e-commerce businesses, and enterprises with
                            shipping needs, providing end-to-end visibility and control over the entire supply chain.
                        </p>

                        <h3>Market Potential</h3>
                        <p>
                            With e-commerce sales projected to exceed <strong>$6.3 trillion by 2024</strong> and the increasing demand for
                            transparent, efficient logistics solutions, TransLogix Global is positioned to capture a significant share of this
                            rapidly growing market.
                        </p>
                        <p>
                            Our target market includes logistics providers, e-commerce platforms, retail chains, and manufacturing companies
                            seeking to optimize their shipping operations and enhance customer satisfaction.
                        </p>
                    </div>

                    <div class="summary-highlights">
                        <div class="highlight-card">
                            <div class="highlight-icon">
                                <i data-feather="trending-up"></i>
                            </div>
                            <div class="highlight-content">
                                <h4>Rapid Growth Potential</h4>
                                <p>Positioned in a $12.68T market growing at 6.5% annually</p>
                            </div>
                        </div>

                        <div class="highlight-card">
                            <div class="highlight-icon">
                                <i data-feather="users"></i>
                            </div>
                            <div class="highlight-content">
                                <h4>Diverse Customer Base</h4>
                                <p>Applicable to logistics companies, e-commerce, and enterprises</p>
                            </div>
                        </div>

                        <div class="highlight-card">
                            <div class="highlight-icon">
                                <i data-feather="code"></i>
                            </div>
                            <div class="highlight-content">
                                <h4>Cutting-Edge Technology</h4>
                                <p>Modern architecture with real-time tracking and analytics</p>
                            </div>
                        </div>

                        <div class="highlight-card">
                            <div class="highlight-icon">
                                <i data-feather="trending-up"></i>
                            </div>
                            <div class="highlight-content">
                                <h4>Scalable Business Model</h4>
                                <p>SaaS model with recurring revenue and high margins</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="key-metrics">
                    <h3>Key Investment Highlights</h3>
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-value">$12.68T</div>
                            <div class="metric-label">Total Addressable Market by 2025</div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-value">6.5%</div>
                            <div class="metric-label">Annual Market Growth Rate</div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-value">85%</div>
                            <div class="metric-label">Gross Margin Potential</div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-value">24 mo</div>
                            <div class="metric-label">Projected Break-Even</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#table-of-contents" class="page-nav-link"><i data-feather="arrow-left"></i> Table of Contents</a>
                    <a href="#market-analysis" class="page-nav-link">Market Analysis <i data-feather="arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Market Analysis Page -->
        <section class="content-page" id="market-analysis">
            <div class="page-header">
                <h2>Market Analysis</h2>
                <div class="page-indicator">2</div>
            </div>

            <div class="page-content">
                <div class="market-overview">
                    <h3>Global Logistics Market Overview</h3>
                    <p>
                        The global logistics market is experiencing unprecedented growth, driven by the expansion of e-commerce,
                        globalization of supply chains, and increasing consumer expectations for fast, transparent delivery services.
                    </p>

                    <div class="market-stats-container">
                        <div class="market-stat">
                            <div class="stat-value">$12.68T</div>
                            <div class="stat-label">Global Logistics Market Size by 2025</div>
                        </div>

                        <div class="market-stat">
                            <div class="stat-value">6.5%</div>
                            <div class="stat-label">CAGR (2020-2025)</div>
                        </div>

                        <div class="market-stat">
                            <div class="stat-value">$6.3T</div>
                            <div class="stat-label">Global E-commerce Sales by 2024</div>
                        </div>

                        <div class="market-stat">
                            <div class="stat-value">73%</div>
                            <div class="stat-label">Customers Demanding Real-time Tracking</div>
                        </div>
                    </div>
                </div>

                <div class="market-challenges">
                    <h3>Industry Challenges & Our Solutions</h3>

                    <div class="challenges-grid">
                        <div class="challenge-item">
                            <div class="challenge-header">
                                <div class="challenge-icon"><i data-feather="eye-off"></i></div>
                                <h4>Lack of Visibility</h4>
                            </div>
                            <p>Limited visibility across the supply chain leads to inefficiencies and customer dissatisfaction.</p>
                            <div class="solution">
                                <strong>Our Solution:</strong> Real-time tracking with interactive maps and detailed status updates provides complete transparency.
                            </div>
                        </div>

                        <div class="challenge-item">
                            <div class="challenge-header">
                                <div class="challenge-icon"><i data-feather="settings"></i></div>
                                <h4>Operational Inefficiency</h4>
                            </div>
                            <p>Manual processes and disconnected systems create bottlenecks and increase operational costs.</p>
                            <div class="solution">
                                <strong>Our Solution:</strong> Streamlined workflows and automation reduce manual effort and minimize errors.
                            </div>
                        </div>

                        <div class="challenge-item">
                            <div class="challenge-header">
                                <div class="challenge-icon"><i data-feather="user-x"></i></div>
                                <h4>Poor Customer Experience</h4>
                            </div>
                            <p>Customers expect Amazon-like tracking experiences that many logistics providers cannot deliver.</p>
                            <div class="solution">
                                <strong>Our Solution:</strong> Customer-facing tracking portal with intuitive interfaces and proactive notifications.
                            </div>
                        </div>

                        <div class="challenge-item">
                            <div class="challenge-header">
                                <div class="challenge-icon"><i data-feather="bar-chart-2"></i></div>
                                <h4>Limited Analytics</h4>
                            </div>
                            <p>Lack of actionable insights prevents optimization and strategic decision-making.</p>
                            <div class="solution">
                                <strong>Our Solution:</strong> Comprehensive analytics dashboard with performance metrics and business intelligence.
                            </div>
                        </div>
                    </div>
                </div>

                <div class="target-market">
                    <h3>Target Market Segments</h3>

                    <div class="market-segments">
                        <div class="segment">
                            <div class="segment-icon"><i data-feather="truck"></i></div>
                            <div class="segment-content">
                                <h4>Logistics Providers</h4>
                                <p>Third-party logistics (3PL) companies, freight forwarders, and shipping carriers seeking to enhance their service offerings.</p>
                                <div class="segment-value">$5.2T Market</div>
                            </div>
                        </div>

                        <div class="segment">
                            <div class="segment-icon"><i data-feather="shopping-cart"></i></div>
                            <div class="segment-content">
                                <h4>E-commerce Platforms</h4>
                                <p>Online retailers and marketplaces looking to improve delivery transparency and customer satisfaction.</p>
                                <div class="segment-value">$4.1T Market</div>
                            </div>
                        </div>

                        <div class="segment">
                            <div class="segment-icon"><i data-feather="box"></i></div>
                            <div class="segment-content">
                                <h4>Manufacturing & Distribution</h4>
                                <p>Companies with complex supply chains requiring end-to-end visibility and operational efficiency.</p>
                                <div class="segment-value">$3.4T Market</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#table-of-contents" class="page-nav-link"><i class="fas fa-arrow-left"></i> Table of Contents</a>
                    <a href="#introduction" class="page-nav-link">Introduction <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Introduction -->
        <section class="content-page" id="introduction">
            <div class="page-header">
                <h2>Introduction</h2>
                <div class="page-indicator">3</div>
            </div>

            <div class="page-content">
                <h3 id="project-overview">Project Overview</h3>
                <p>
                    TransLogix Global is a comprehensive logistics and shipment tracking platform designed to streamline operations, enhance customer experience, and drive business growth in the global shipping industry. The system provides end-to-end visibility of shipments with interactive maps, detailed tracking timelines, and robust management tools for logistics providers and customers alike.
                </p>
                <p>
                    Built with modern web technologies, TransLogix Global offers a responsive, user-friendly interface that works seamlessly across desktop and mobile devices. The platform combines powerful backend functionality with an intuitive frontend design to deliver a complete solution for the logistics sector.
                </p>

                <h3 id="purpose-goals">Purpose and Goals</h3>
                <p>
                    The primary purpose of TransLogix Global is to address the key challenges faced by logistics providers and their customers in today's fast-paced shipping environment. The platform aims to:
                </p>
                <ul>
                    <li>Provide real-time visibility into shipment status and location</li>
                    <li>Streamline logistics operations through automation and efficient workflows</li>
                    <li>Enhance customer satisfaction with transparent, accessible tracking information</li>
                    <li>Reduce operational costs and improve resource allocation</li>
                    <li>Generate actionable insights through comprehensive analytics</li>
                    <li>Scale with businesses as they grow and expand their operations</li>
                </ul>

                <h3 id="target-audience">Target Audience</h3>
                <p>
                    TransLogix Global is designed to serve multiple stakeholders in the logistics ecosystem:
                </p>
                <div class="audience-grid">
                    <div class="audience-item">
                        <h4>Logistics Providers</h4>
                        <p>Third-party logistics companies (3PLs), freight forwarders, shipping carriers, and courier services.</p>
                    </div>
                    <div class="audience-item">
                        <h4>E-commerce Businesses</h4>
                        <p>Online retailers and marketplaces seeking to provide transparent shipping information to customers.</p>
                    </div>
                    <div class="audience-item">
                        <h4>Enterprise Shippers</h4>
                        <p>Manufacturing, distribution, and retail companies with complex supply chains.</p>
                    </div>
                    <div class="audience-item">
                        <h4>End Customers</h4>
                        <p>Recipients who want to track packages in real-time and receive delivery notifications.</p>
                    </div>
                </div>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#market-analysis" class="page-nav-link"><i class="fas fa-arrow-left"></i> Market Analysis</a>
                    <a href="#features" class="page-nav-link">Features <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Features -->
        <section class="content-page" id="features">
            <div class="page-header">
                <h2>Features</h2>
                <div class="page-indicator">4</div>
            </div>

            <div class="page-content">
                <h3>Public Website</h3>

                <h4 id="responsive-landing">Responsive Landing Page</h4>
                <p>
                    The TransLogix Global platform features a modern, responsive landing page that adapts seamlessly to any device size. Key elements include:
                </p>
                <ul>
                    <li>Intuitive navigation with clear call-to-action buttons</li>
                    <li>Quick shipment tracking search functionality</li>
                    <li>Service highlights and value proposition</li>
                    <li>Responsive design that works on desktop, tablet, and mobile devices</li>
                    <li>Fast loading times and optimized performance</li>
                    <li>Light and dark theme support for user preference</li>
                </ul>

                <h4 id="shipment-tracking">Shipment Tracking with Interactive Map</h4>
                <p>
                    The core tracking functionality provides customers with detailed visibility into their shipments:
                </p>
                <ul>
                    <li>Real-time location tracking displayed on an interactive map</li>
                    <li>Animated path visualization showing the complete route</li>
                    <li>Multiple map views (street, satellite, hybrid)</li>
                    <li>Detailed package information and delivery instructions</li>
                    <li>Estimated delivery time with countdown timer</li>
                    <li>One-click sharing of tracking information via email or social media</li>
                </ul>

                <h4 id="tracking-timeline">Detailed Tracking Timeline</h4>
                <p>
                    Each shipment includes a comprehensive timeline of events:
                </p>
                <ul>
                    <li>Chronological display of all shipment milestones</li>
                    <li>Timestamp and location information for each event</li>
                    <li>Status indicators with color-coding for quick reference</li>
                    <li>Detailed descriptions of each tracking event</li>
                    <li>Future milestones with estimated times</li>
                    <li>Historical data retention for completed shipments</li>
                </ul>

                <h4 id="theme-support">Light and Dark Theme Support</h4>
                <p>
                    The platform offers flexible visual options to suit user preferences:
                </p>
                <ul>
                    <li>Toggle between light and dark themes</li>
                    <li>System preference detection for automatic theme selection</li>
                    <li>Consistent design language across both themes</li>
                    <li>Optimized contrast ratios for accessibility</li>
                    <li>Reduced eye strain in low-light environments with dark theme</li>
                    <li>Persistent theme selection saved to user preferences</li>
                </ul>

                <h3>Admin CMS</h3>

                <h4 id="login-system">Secure Login System</h4>
                <p>
                    The administrative backend features a robust security system:
                </p>
                <ul>
                    <li>Secure authentication with password hashing</li>
                    <li>Role-based access control for different user types</li>
                    <li>Two-factor authentication support</li>
                    <li>Session management with automatic timeouts</li>
                    <li>Failed login attempt monitoring and lockout</li>
                    <li>Secure password reset functionality</li>
                </ul>

                <h4 id="dashboard-stats">Dashboard with Shipment Statistics</h4>
                <p>
                    The admin dashboard provides a comprehensive overview of operations:
                </p>
                <ul>
                    <li>Real-time metrics and key performance indicators</li>
                    <li>Visual charts and graphs for data visualization</li>
                    <li>Shipment status distribution</li>
                    <li>Delivery performance metrics</li>
                    <li>Volume trends and forecasting</li>
                    <li>Customizable dashboard widgets</li>
                </ul>

                <h4 id="shipment-management">Shipment Management</h4>
                <p>
                    Comprehensive tools for managing all aspects of shipments:
                </p>
                <ul>
                    <li>Create, view, update, and delete shipment records</li>
                    <li>Bulk operations for efficient management</li>
                    <li>Advanced search and filtering capabilities</li>
                    <li>Detailed shipment information editing</li>
                    <li>Package image upload and management</li>
                    <li>Customer and receiver information management</li>
                </ul>

                <h4 id="tracking-updates">Tracking Updates Management</h4>
                <p>
                    Tools for maintaining accurate tracking information:
                </p>
                <ul>
                    <li>Manual and automated tracking updates</li>
                    <li>Batch update capabilities for multiple shipments</li>
                    <li>Custom status creation and management</li>
                    <li>Location data integration with mapping services</li>
                    <li>Event timestamp management</li>
                    <li>Notification triggers for status changes</li>
                </ul>

                <h4 id="user-management">User Management</h4>
                <p>
                    Administrative tools for managing system users:
                </p>
                <ul>
                    <li>User creation, editing, and deactivation</li>
                    <li>Role and permission assignment</li>
                    <li>User activity monitoring and logs</li>
                    <li>Password management and reset</li>
                    <li>Department and team organization</li>
                    <li>Access control and restriction settings</li>
                </ul>

                <h4 id="system-settings">System Settings Customization</h4>
                <p>
                    Configuration options for tailoring the system:
                </p>
                <ul>
                    <li>Company information and branding settings</li>
                    <li>Email notification templates</li>
                    <li>Default system preferences</li>
                    <li>API integration configuration</li>
                    <li>Maintenance mode controls</li>
                    <li>System backup and restore options</li>
                    <li>Geocoding settings for map functionality</li>
                </ul>

                <h4 id="maintenance-tools">Maintenance Tools</h4>
                <p>
                    Comprehensive tools for system maintenance and optimization:
                </p>
                <ul>
                    <li>Maintenance mode toggle for system updates</li>
                    <li>Cleanup tools for removing redundant files</li>
                    <li>Database optimization utilities</li>
                    <li>Legacy tools management for backward compatibility</li>
                    <li>System diagnostics and health checks</li>
                    <li>Error logging and monitoring</li>
                    <li>Cache management and performance optimization</li>
                </ul>

                <h4 id="alert-system">Alert System</h4>
                <p>
                    Comprehensive notification system for keeping users informed:
                </p>
                <ul>
                    <li>Real-time alerts for critical events</li>
                    <li>Email notifications for status changes</li>
                    <li>Custom alert rules and triggers</li>
                    <li>Delivery exception notifications</li>
                    <li>System status and maintenance alerts</li>
                    <li>User-configurable notification preferences</li>
                </ul>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#introduction" class="page-nav-link"><i class="fas fa-arrow-left"></i> Introduction</a>
                    <a href="#requirements" class="page-nav-link">Requirements <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Requirements -->
        <section class="content-page" id="requirements">
            <div class="page-header">
                <h2>Requirements</h2>
                <div class="page-indicator">5</div>
            </div>

            <div class="page-content">
                <h3>Software Requirements</h3>

                <h4 id="php-version">PHP Version</h4>
                <p>
                    TransLogix Global requires PHP 7.4 or higher for optimal performance and security. The system leverages several modern PHP features including:
                </p>
                <ul>
                    <li>Type declarations for improved code reliability</li>
                    <li>Arrow functions for cleaner callback syntax</li>
                    <li>Null coalescing assignment operator (??=)</li>
                    <li>Improved error handling and exception management</li>
                </ul>
                <p>
                    PHP 8.0+ is recommended for best performance and access to the latest language features, though the system is designed to be compatible with PHP 7.4 as a minimum requirement.
                </p>

                <h4 id="mysql-version">MySQL Version</h4>
                <p>
                    The platform requires MySQL 5.7 or higher, with MySQL 8.0+ recommended. Key database features utilized include:
                </p>
                <ul>
                    <li>JSON data type support for flexible data storage</li>
                    <li>Full-text indexing for efficient search functionality</li>
                    <li>InnoDB storage engine for transaction support and referential integrity</li>
                    <li>UTF-8mb4 character set for complete Unicode support</li>
                </ul>

                <h4 id="web-server">Web Server</h4>
                <p>
                    TransLogix Global is compatible with the following web servers:
                </p>
                <ul>
                    <li>Apache 2.4+ (recommended) with mod_rewrite enabled</li>
                    <li>Nginx 1.18+ with proper rewrite rules configured</li>
                    <li>Microsoft IIS 10+ with URL Rewrite module</li>
                </ul>
                <p>
                    The system includes configuration files for Apache (.htaccess) by default. For other web servers, additional configuration may be required.
                </p>

                <h4 id="php-extensions">PHP Extensions</h4>
                <p>
                    The following PHP extensions are required for full functionality:
                </p>
                <ul>
                    <li>PDO and PDO_MySQL for database connectivity</li>
                    <li>JSON for data processing</li>
                    <li>cURL for API integrations</li>
                    <li>GD or Imagick for image processing</li>
                    <li>OpenSSL for secure communications</li>
                    <li>Mbstring for proper UTF-8 string handling</li>
                    <li>XML for data parsing and API responses</li>
                    <li>Fileinfo for file type detection</li>
                </ul>

                <h3>Hardware Requirements</h3>

                <h4 id="hardware-specs">Minimum Specifications</h4>
                <p>
                    For optimal performance, the following hardware specifications are recommended:
                </p>

                <h5>Web Server</h5>
                <ul>
                    <li>CPU: Dual-core processor, 2.0 GHz or higher</li>
                    <li>RAM: 4GB minimum, 8GB recommended</li>
                    <li>Storage: 20GB SSD minimum</li>
                    <li>Network: 100 Mbps connection</li>
                </ul>

                <h5>Database Server</h5>
                <ul>
                    <li>CPU: Quad-core processor, 2.5 GHz or higher</li>
                    <li>RAM: 8GB minimum, 16GB recommended</li>
                    <li>Storage: 50GB SSD minimum with appropriate IOPS</li>
                    <li>Network: 1 Gbps connection recommended</li>
                </ul>

                <p>
                    For small to medium deployments, the web and database servers can be combined on a single machine with appropriate resources. For larger deployments with high traffic volumes, separate servers are recommended for optimal performance and scalability.
                </p>

                <h5>Client Requirements</h5>
                <p>
                    End users can access the system using any modern web browser, including:
                </p>
                <ul>
                    <li>Google Chrome (latest 2 versions)</li>
                    <li>Mozilla Firefox (latest 2 versions)</li>
                    <li>Microsoft Edge (Chromium-based, latest 2 versions)</li>
                    <li>Safari (latest 2 versions)</li>
                    <li>Opera (latest 2 versions)</li>
                </ul>
                <p>
                    The system is designed to be responsive and works on desktop computers, tablets, and mobile devices with appropriate screen sizes.
                </p>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#features" class="page-nav-link"><i class="fas fa-arrow-left"></i> Features</a>
                    <a href="#installation" class="page-nav-link">Installation <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Installation -->
        <section class="content-page" id="installation">
            <div class="page-header">
                <h2>Installation</h2>
                <div class="page-indicator">6</div>
            </div>

            <div class="page-content">
                <h3 id="prerequisites">Prerequisites</h3>
                <p>
                    Before beginning the installation process, ensure that you have:
                </p>
                <ul>
                    <li>Access to your cPanel account</li>
                    <li>FTP or File Manager access to upload files</li>
                    <li>Your hosting account database prefix (e.g., u181814817_)</li>
                </ul>

                <h3 id="installation-steps">Step-by-Step Instructions</h3>

                <h4>1. Database Setup in cPanel</h4>
                <ol>
                    <li>Log into your cPanel account</li>
                    <li>Navigate to MySQL Databases</li>
                    <li>Under "Create New Database":
                        <ul>
                            <li>Enter "tracking" as the database name</li>
                            <li>Note: Your hosting will automatically add your account prefix</li>
                        </ul>
                    </li>
                    <li>Under "MySQL Users":
                        <ul>
                            <li>Create a new user or use existing one</li>
                            <li>Note down the password</li>
                        </ul>
                    </li>
                    <li>Under "Add User To Database":
                        <ul>
                            <li>Select your database and user</li>
                            <li>Grant "ALL PRIVILEGES" to the user</li>
                        </ul>
                    </li>
                </ol>

                <h4>2. File Upload</h4>
                <p>
                    Upload all files to your web server using FTP or File Manager.
                </p>

                <h4>3. Run Installation Script</h4>
                <p>
                    Navigate to the installation script in your browser and use these settings:
                </p>
                <div class="code-block">
                    <pre>
Database Host: localhost
Database Name: [your_prefix]_tracking
Database User: [your_prefix]_root
Database Password: [your password]</pre>
                </div>

                <div class="alert alert-warning">
                    <h4>Important Notes:</h4>
                    <ul>
                        <li>Always use <code>localhost</code> as the Database Host</li>
                        <li>Database names and users must include your hosting prefix</li>
                        <li>Default admin credentials will be:
                            <ul>
                                <li>Username: admin</li>
                                <li>Password: admin123</li>
                            </ul>
                        </li>
                        <li>Change the default admin password immediately after installation</li>
                    </ul>
                </div>

                <h4>4. Post-Installation</h4>
                <ol>
                    <li>Log in to the admin panel using the default credentials</li>
                    <li>Change the admin password immediately</li>
                    <li>Configure your system settings</li>
                    <li>Remove the installation files:
                        <ul>
                            <li>Delete install.php</li>
                            <li>Delete the install/ directory if present</li>
                        </ul>
                    </li>
                </ol>
            </div>
        </section>

        <!-- Product Showcase Page -->
        <section class="content-page" id="product-showcase">
            <div class="page-header">
                <h2>Product Showcase</h2>
                <div class="page-indicator">3</div>
            </div>

            <div class="page-content">
                <div class="product-intro">
                    <h3>TransLogix Global Platform</h3>
                    <p>
                        Our comprehensive logistics management platform combines powerful functionality with intuitive design to deliver
                        a seamless experience for logistics providers, staff members, and end customers. The following showcase highlights
                        the key features and interfaces of our solution.
                    </p>
                </div>

                <div class="showcase-tabs">
                    <div class="tab-navigation">
                        <button class="tab-button active" data-tab="tracking">Shipment Tracking</button>
                        <button class="tab-button" data-tab="dashboard">User Dashboard</button>
                        <button class="tab-button" data-tab="admin">Admin Interface</button>
                        <button class="tab-button" data-tab="mobile">Mobile Experience</button>
                        <button class="tab-button" data-tab="pages">Key Pages</button>
                    </div>

                    <div class="tab-content">
                        <!-- Tracking Tab -->
                        <div class="tab-pane active" id="tracking-tab">
                            <div class="showcase-item">
                                <div class="showcase-image">
                                    <img src="screenshots/tracking page.png" alt="Shipment Tracking Interface">
                                </div>
                                <div class="showcase-description">
                                    <h4>Advanced Shipment Tracking</h4>
                                    <p>
                                        Our tracking interface provides real-time visibility into shipment status and location. The interactive map
                                        shows the complete route with animated path tracking using Leaflet's ant path animation, while the detailed timeline displays all shipment events.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Real-time location tracking with interactive Leaflet maps</li>
                                        <li><i data-feather="check"></i> Detailed shipment timeline with status updates and progress indicators</li>
                                        <li><i data-feather="check"></i> Estimated delivery time with countdown timer</li>
                                        <li><i data-feather="check"></i> One-click sharing of tracking information via social media or email</li>
                                        <li><i data-feather="check"></i> Package details including images, dimensions, and weight</li>
                                        <li><i data-feather="check"></i> Shipper and receiver information with contact details</li>
                                        <li><i data-feather="check"></i> Route visualization following actual roads without direction arrows</li>
                                        <li><i data-feather="check"></i> Distinctive pins for origin, current location, and destination</li>
                                        <li><i data-feather="check"></i> Ant path animation for visualizing shipment movement along routes</li>
                                        <li><i data-feather="check"></i> Geocoding support for converting addresses to map coordinates</li>
                                        <li><i data-feather="check"></i> Multiple map type options (street, satellite, terrain)</li>
                                        <li><i data-feather="check"></i> Support for manual coordinate entry for precise location tracking</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Reduces customer service inquiries by up to 70% while increasing customer satisfaction and loyalty through transparent shipment visibility.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Dashboard Tab -->
                        <div class="tab-pane" id="dashboard-tab">
                            <div class="showcase-item">
                                <div class="showcase-image">
                                    <img src="screenshots/customer page.png" alt="Customer Dashboard Interface">
                                </div>
                                <div class="showcase-description">
                                    <h4>Intuitive Customer Dashboard</h4>
                                    <p>
                                        The customer dashboard provides a comprehensive overview of all shipments and activities. Customers can quickly access
                                        key information, track shipments, and manage their account from a single, intuitive interface with real-time shipping data.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Personalized dashboard with shipment overview and performance metrics</li>
                                        <li><i data-feather="check"></i> Quick access to active shipments with status indicators</li>
                                        <li><i data-feather="check"></i> Shipment cards with visual status indicators and quick actions</li>
                                        <li><i data-feather="check"></i> Advanced search and filtering capabilities</li>
                                        <li><i data-feather="check"></i> One-click actions for tracking and sharing shipments</li>
                                        <li><i data-feather="check"></i> Notification center for shipment updates and alerts</li>
                                        <li><i data-feather="check"></i> Account management and profile settings</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Improves customer experience by providing self-service capabilities that reduce support costs while increasing satisfaction and retention.
                                    </div>
                                </div>
                            </div>

                            <div class="showcase-item reverse">
                                <div class="showcase-image">
                                    <img src="screenshots/staff login.png" alt="Staff Login Interface">
                                </div>
                                <div class="showcase-description">
                                    <h4>Secure Staff Portal</h4>
                                    <p>
                                        Our staff portal provides secure access to the system with role-based permissions. Staff members can manage shipments,
                                        handle customer inquiries, and access administrative functions based on their assigned roles.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Secure authentication with multi-factor options</li>
                                        <li><i data-feather="check"></i> Role-based access control for different staff levels</li>
                                        <li><i data-feather="check"></i> Comprehensive CRUD functionality for managing users and shipments</li>
                                        <li><i data-feather="check"></i> Session management with automatic timeout for security</li>
                                        <li><i data-feather="check"></i> Password recovery and account management</li>
                                        <li><i data-feather="check"></i> Activity logging for security and compliance</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Ensures data security while providing staff with the tools they need to efficiently manage operations and serve customers.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Admin Tab -->
                        <div class="tab-pane" id="admin-tab">
                            <div class="showcase-item">
                                <div class="showcase-image">
                                    <img src="screenshots/admin dashboard.png" alt="Admin Dashboard Interface">
                                </div>
                                <div class="showcase-description">
                                    <h4>Powerful Admin Dashboard</h4>
                                    <p>
                                        The admin dashboard provides a comprehensive overview of the entire operation with real-time analytics,
                                        performance metrics, and system management tools. Administrators can monitor all aspects of the business
                                        and make data-driven decisions through an intuitive interface with modern charts and graphs.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Real-time analytics with interactive charts and graphs</li>
                                        <li><i data-feather="check"></i> User management with role-based permissions system</li>
                                        <li><i data-feather="check"></i> System configuration and customization options</li>
                                        <li><i data-feather="check"></i> Comprehensive shipment management with CRUD functionality</li>
                                        <li><i data-feather="check"></i> Business intelligence reports and data exports</li>
                                        <li><i data-feather="check"></i> System settings with maintenance mode toggle</li>
                                        <li><i data-feather="check"></i> Two-column layout for efficient information organization</li>
                                        <li><i data-feather="check"></i> Performance metrics showing delivery statistics and trends</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Enables data-driven decision making and provides actionable insights that can increase operational efficiency by 25% while reducing management overhead.
                                    </div>
                                </div>
                            </div>

                            <div class="showcase-item reverse">
                                <div class="showcase-image">
                                    <img src="screenshots/dark mode home screen.png" alt="Admin Dashboard Interface - Dark Mode">
                                </div>
                                <div class="showcase-description">
                                    <h4>Dark Mode Admin Experience</h4>
                                    <p>
                                        Our admin interface includes a fully-featured dark mode that reduces eye strain during extended use and provides
                                        a modern, professional appearance. All functionality remains accessible with optimized contrast and readability.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> One-click theme switching between light and dark modes</li>
                                        <li><i data-feather="check"></i> Reduced eye strain for extended working sessions</li>
                                        <li><i data-feather="check"></i> Optimized color contrast for readability</li>
                                        <li><i data-feather="check"></i> Consistent branding and visual elements</li>
                                        <li><i data-feather="check"></i> Persistent theme preference across sessions</li>
                                        <li><i data-feather="check"></i> Automatic theme switching based on system preferences</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Improves staff comfort and productivity during long working hours while providing a modern user experience that enhances brand perception.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Tab -->
                        <div class="tab-pane" id="mobile-tab">
                            <div class="showcase-item">
                                <div class="showcase-image">
                                    <img src="screenshots/mobile view.png" alt="Mobile Responsive Design">
                                </div>
                                <div class="showcase-description">
                                    <h4>Mobile-First Responsive Design</h4>
                                    <p>
                                        Our platform is built with a mobile-first approach, ensuring a seamless experience across all devices.
                                        The responsive design adapts to different screen sizes while maintaining full functionality, allowing users
                                        to track shipments and manage their accounts on the go.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Optimized for smartphones and tablets of all sizes</li>
                                        <li><i data-feather="check"></i> Touch-friendly controls with appropriate sizing</li>
                                        <li><i data-feather="check"></i> Adaptive layouts that reorganize content for smaller screens</li>
                                        <li><i data-feather="check"></i> Fast loading times optimized for mobile networks</li>
                                        <li><i data-feather="check"></i> Full feature parity with desktop version</li>
                                        <li><i data-feather="check"></i> Mobile-optimized maps and tracking interfaces</li>
                                        <li><i data-feather="check"></i> Simplified navigation for smaller screens</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Enables customers and staff to access the system from anywhere, increasing engagement and productivity while providing a competitive advantage.
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Key Pages Tab -->
                        <div class="tab-pane" id="pages-tab">
                            <div class="showcase-item">
                                <div class="showcase-image">
                                    <img src="screenshots/about page.png" alt="About Page">
                                </div>
                                <div class="showcase-description">
                                    <h4>Informative About Page</h4>
                                    <p>
                                        Our About page provides comprehensive information about the company, its mission, vision, and values.
                                        Visitors can learn about our history, team, and commitment to excellence in logistics services.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Company history and background information</li>
                                        <li><i data-feather="check"></i> Mission, vision, and values statements</li>
                                        <li><i data-feather="check"></i> Team profiles with expertise highlights</li>
                                        <li><i data-feather="check"></i> Company achievements and milestones</li>
                                        <li><i data-feather="check"></i> Testimonials from satisfied clients</li>
                                        <li><i data-feather="check"></i> Interactive timeline of company growth</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Builds trust and credibility with potential clients by showcasing the company's expertise and commitment to quality service.
                                    </div>
                                </div>
                            </div>

                            <div class="showcase-item reverse">
                                <div class="showcase-image">
                                    <img src="screenshots/contact us page.png" alt="Contact Us Page">
                                </div>
                                <div class="showcase-description">
                                    <h4>Comprehensive Contact Page</h4>
                                    <p>
                                        Our Contact page makes it easy for visitors to reach out with inquiries, feedback, or support requests.
                                        Multiple contact options and a user-friendly form ensure that communication is simple and efficient.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Interactive contact form with validation</li>
                                        <li><i data-feather="check"></i> Multiple contact methods (phone, email, chat)</li>
                                        <li><i data-feather="check"></i> Office locations with interactive maps</li>
                                        <li><i data-feather="check"></i> Business hours and availability information</li>
                                        <li><i data-feather="check"></i> Department-specific contact details</li>
                                        <li><i data-feather="check"></i> Social media links for alternative contact methods</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Improves customer service by providing multiple channels for communication, leading to faster issue resolution and higher satisfaction.
                                    </div>
                                </div>
                            </div>

                            <div class="showcase-item">
                                <div class="showcase-image">
                                    <img src="screenshots/services page.png" alt="Services Page">
                                </div>
                                <div class="showcase-description">
                                    <h4>Detailed Services Page</h4>
                                    <p>
                                        Our Services page showcases the comprehensive range of logistics solutions we offer. Detailed descriptions,
                                        benefits, and visual elements help visitors understand our capabilities and how we can meet their needs.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Comprehensive service catalog with detailed descriptions</li>
                                        <li><i data-feather="check"></i> Service benefits and unique selling points</li>
                                        <li><i data-feather="check"></i> Visual representations of service processes</li>
                                        <li><i data-feather="check"></i> Pricing information and package options</li>
                                        <li><i data-feather="check"></i> Service comparison tools for decision-making</li>
                                        <li><i data-feather="check"></i> Call-to-action buttons for service inquiries</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Drives conversions by clearly communicating service offerings and their benefits, helping potential clients make informed decisions.
                                    </div>
                                </div>
                            </div>

                            <div class="showcase-item reverse">
                                <div class="showcase-image">
                                    <img src="screenshots/industries.png" alt="Industries Page">
                                </div>
                                <div class="showcase-description">
                                    <h4>Industry-Specific Solutions</h4>
                                    <p>
                                        Our Industries page highlights how our logistics solutions are tailored to meet the unique needs of different
                                        sectors. Industry-specific case studies and benefits demonstrate our expertise across various markets.
                                    </p>
                                    <ul class="feature-list">
                                        <li><i data-feather="check"></i> Industry-specific logistics solutions and approaches</li>
                                        <li><i data-feather="check"></i> Case studies demonstrating success in each sector</li>
                                        <li><i data-feather="check"></i> Specialized features for different industry requirements</li>
                                        <li><i data-feather="check"></i> Compliance information for regulated industries</li>
                                        <li><i data-feather="check"></i> Industry-specific metrics and performance indicators</li>
                                        <li><i data-feather="check"></i> Testimonials from clients in various sectors</li>
                                    </ul>
                                    <div class="business-value">
                                        <strong>Business Value:</strong> Demonstrates expertise in specific industries, building credibility and trust with potential clients in those sectors.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#market-analysis" class="page-nav-link"><i class="fas fa-arrow-left"></i> Market Analysis</a>
                    <a href="#business-model" class="page-nav-link">Business Model <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Business Model Page -->
        <section class="content-page" id="business-model">
            <div class="page-header">
                <h2>Business Model & Investment Opportunity</h2>
                <div class="page-indicator">4</div>
            </div>

            <div class="page-content">
                <div class="business-model-intro">
                    <h3>Revenue Model</h3>
                    <p>
                        TransLogix Global operates on a Software-as-a-Service (SaaS) model, providing a scalable, recurring revenue
                        stream with high margins and predictable cash flow. Our tiered subscription plans cater to businesses of all
                        sizes, from small logistics providers to enterprise-level corporations.
                    </p>
                </div>

                <div class="revenue-streams">
                    <div class="revenue-stream-item">
                        <div class="revenue-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="revenue-content">
                            <h4>Subscription Plans</h4>
                            <p>Monthly or annual subscription fees based on user count, shipment volume, and feature access.</p>
                            <div class="revenue-metrics">
                                <div class="metric">
                                    <span class="metric-label">Contribution:</span>
                                    <span class="metric-value">70%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Gross Margin:</span>
                                    <span class="metric-value">85%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="revenue-stream-item">
                        <div class="revenue-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="revenue-content">
                            <h4>Implementation Services</h4>
                            <p>One-time setup, configuration, data migration, and training services for new customers.</p>
                            <div class="revenue-metrics">
                                <div class="metric">
                                    <span class="metric-label">Contribution:</span>
                                    <span class="metric-value">15%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Gross Margin:</span>
                                    <span class="metric-value">60%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="revenue-stream-item">
                        <div class="revenue-icon">
                            <i class="fas fa-plug"></i>
                        </div>
                        <div class="revenue-content">
                            <h4>API & Integration</h4>
                            <p>Access to our API for custom integrations and connections to third-party systems.</p>
                            <div class="revenue-metrics">
                                <div class="metric">
                                    <span class="metric-label">Contribution:</span>
                                    <span class="metric-value">10%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Gross Margin:</span>
                                    <span class="metric-value">90%</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="revenue-stream-item">
                        <div class="revenue-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <div class="revenue-content">
                            <h4>Premium Support</h4>
                            <p>Enhanced support packages with dedicated account managers and priority response times.</p>
                            <div class="revenue-metrics">
                                <div class="metric">
                                    <span class="metric-label">Contribution:</span>
                                    <span class="metric-value">5%</span>
                                </div>
                                <div class="metric">
                                    <span class="metric-label">Gross Margin:</span>
                                    <span class="metric-value">75%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="financial-projections">
                    <h3>Financial Projections</h3>
                    <p>
                        Based on our market analysis and early customer interest, we project strong growth over the next five years,
                        with a clear path to profitability and significant return on investment.
                    </p>

                    <div class="projections-grid">
                        <div class="projection-item">
                            <h4>Year 1</h4>
                            <div class="projection-metrics">
                                <div class="projection-metric">
                                    <span class="metric-label">Revenue:</span>
                                    <span class="metric-value">$1.2M</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">Customers:</span>
                                    <span class="metric-value">50</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">Gross Margin:</span>
                                    <span class="metric-value">75%</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">EBITDA:</span>
                                    <span class="metric-value">-$300K</span>
                                </div>
                            </div>
                        </div>

                        <div class="projection-item">
                            <h4>Year 3</h4>
                            <div class="projection-metrics">
                                <div class="projection-metric">
                                    <span class="metric-label">Revenue:</span>
                                    <span class="metric-value">$5.8M</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">Customers:</span>
                                    <span class="metric-value">200</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">Gross Margin:</span>
                                    <span class="metric-value">82%</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">EBITDA:</span>
                                    <span class="metric-value">$1.2M</span>
                                </div>
                            </div>
                        </div>

                        <div class="projection-item">
                            <h4>Year 5</h4>
                            <div class="projection-metrics">
                                <div class="projection-metric">
                                    <span class="metric-label">Revenue:</span>
                                    <span class="metric-value">$15.5M</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">Customers:</span>
                                    <span class="metric-value">500</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">Gross Margin:</span>
                                    <span class="metric-value">85%</span>
                                </div>
                                <div class="projection-metric">
                                    <span class="metric-label">EBITDA:</span>
                                    <span class="metric-value">$5.2M</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="investment-opportunity">
                    <h3>Investment Opportunity</h3>
                    <p>
                        We are seeking $2.5 million in Series A funding to accelerate our growth, expand our market reach, and enhance
                        our product capabilities. This investment will be allocated to the following key areas:
                    </p>

                    <div class="investment-allocation">
                        <div class="allocation-item">
                            <div class="allocation-percentage">40%</div>
                            <div class="allocation-label">Product Development</div>
                            <div class="allocation-description">Enhancing existing features and developing new capabilities</div>
                        </div>

                        <div class="allocation-item">
                            <div class="allocation-percentage">30%</div>
                            <div class="allocation-label">Sales & Marketing</div>
                            <div class="allocation-description">Expanding market reach and customer acquisition</div>
                        </div>

                        <div class="allocation-item">
                            <div class="allocation-percentage">20%</div>
                            <div class="allocation-label">Operations</div>
                            <div class="allocation-description">Scaling infrastructure and customer support</div>
                        </div>

                        <div class="allocation-item">
                            <div class="allocation-percentage">10%</div>
                            <div class="allocation-label">Working Capital</div>
                            <div class="allocation-description">Ensuring operational flexibility and stability</div>
                        </div>
                    </div>

                    <div class="investment-returns">
                        <h4>Projected Returns</h4>
                        <ul class="returns-list">
                            <li><i class="fas fa-check-circle"></i> <strong>5X ROI</strong> within 5 years based on conservative growth projections</li>
                            <li><i class="fas fa-check-circle"></i> <strong>Break-even</strong> expected within 24 months of funding</li>
                            <li><i class="fas fa-check-circle"></i> <strong>Exit opportunities</strong> through acquisition by larger logistics or technology companies</li>
                            <li><i class="fas fa-check-circle"></i> <strong>Potential IPO</strong> in 5-7 years with continued growth and market expansion</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#product-showcase" class="page-nav-link"><i class="fas fa-arrow-left"></i> Product Showcase</a>
                    <a href="#contact" class="page-nav-link">Contact <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>

        <!-- Configuration Section -->
        <section class="content-page" id="configuration-setup">
            <div class="page-header">
                <h2>System Configuration</h2>
                <div class="page-indicator">5</div>
            </div>

            <div class="page-content">
                <h3>1. Basic Configuration</h3>
                <div class="config-section">
                    <h4>Database Configuration</h4>
                    <p>Edit <code>includes/config.php</code> to set your database parameters:</p>
                    <div class="code-block">
                        <pre>
define('DB_HOST', 'localhost');    // Database host
define('DB_USER', 'root');        // Database username
define('DB_PASS', '');            // Database password
define('DB_NAME', 'tracking_cms'); // Database name</pre>
                    </div>

                    <h4>Site Configuration</h4>
                    <div class="code-block">
                        <pre>
define('SITE_NAME', 'TransLogix Tracking System'); // Your site name
define('SITE_URL', $base_path);                    // Site base URL
define('ADMIN_EMAIL', '<EMAIL>');     // Admin email</pre>
                    </div>
                </div>

                <h3>3. Utility Scripts</h3>
                <div class="config-section">
                    <div class="alert alert-warning">
                        <h4>Security Warning:</h4>
                        <ul>
                            <li>These scripts should be stored securely offline</li>
                            <li>Upload only when needed</li>
                            <li>Delete immediately after use</li>
                        </ul>
                    </div>

                    <h4>Password Reset Script</h4>
                    <p>Use <code>reset_admin.php</code> to reset admin password:</p>
                    <div class="error-item">
                        <ul>
                            <li>Resets password to: admin123</li>
                            <li>Upload to server root directory</li>
                            <li>Access via browser</li>
                            <li>Delete after successful reset</li>
                        </ul>
                    </div>

                    <h4>Maintenance Mode Scripts</h4>
                    <div class="error-item">
                        <span class="error-code">disable_maintenance_web.php</span>
                        <p class="error-desc">Visual interface to disable maintenance mode</p>
                        <div class="error-solution">
                            <ul>
                                <li>Use when locked out of admin panel</li>
                                <li>Provides visual confirmation</li>
                                <li>Includes homepage return link</li>
                            </ul>
                        </div>
                    </div>

                    <div class="error-item">
                        <span class="error-code">disable_maintenance_direct.php</span>
                        <p class="error-desc">Minimal script to disable maintenance mode</p>
                        <div class="error-solution">
                            <ul>
                                <li>Lightweight alternative</li>
                                <li>Use when web interface fails</li>
                                <li>Basic text output only</li>
                            </ul>
                        </div>
                    </div>

                    <h4>Cache Management</h4>
                    <div class="error-item">
                        <span class="error-code">clear_cache.php</span>
                        <p class="error-desc">System cache clearing utility</p>
                        <div class="error-solution">
                            <ul>
                                <li>Clears PHP session data</li>
                                <li>Resets PHP opcache if enabled</li>
                                <li>Clears APC cache if available</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <h3>2. Error Codes & Solutions</h3>
                <div class="error-codes-grid">
                    <div class="error-card">
                        <h4>Database Errors (1000-1999)</h4>
                        <div class="error-item">
                            <span class="error-code">Error 1001</span>
                            <p class="error-desc">Database connection failed</p>
                            <div class="error-solution">
                                <strong>Solution:</strong>
                                <ul>
                                    <li>Verify DB_HOST is correct (usually 'localhost')</li>
                                    <li>Check if MySQL service is running</li>
                                    <li>Confirm database credentials</li>
                                </ul>
                            </div>
                        </div>
                        <div class="error-item">
                            <span class="error-code">Error 1002</span>
                            <p class="error-desc">Table creation failed</p>
                            <div class="error-solution">
                                <strong>Solution:</strong>
                                <ul>
                                    <li>Check database user permissions</li>
                                    <li>Verify database exists</li>
                                    <li>Ensure no table name conflicts</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="error-card">
                        <h4>Authentication Errors (2000-2999)</h4>
                        <div class="error-item">
                            <span class="error-code">Error 2001</span>
                            <p class="error-desc">Session initialization failed</p>
                            <div class="error-solution">
                                <strong>Solution:</strong>
                                <ul>
                                    <li>Check PHP session configuration</li>
                                    <li>Verify write permissions on session directory</li>
                                    <li>Clear browser cookies</li>
                                </ul>
                            </div>
                        </div>
                        <div class="error-item">
                            <span class="error-code">Error 2002</span>
                            <p class="error-desc">Invalid authentication token</p>
                            <div class="error-solution">
                                <strong>Solution:</strong>
                                <ul>
                                    <li>Clear browser cache and cookies</li>
                                    <li>Check session timeout settings</li>
                                    <li>Verify HTTPS configuration</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="error-card">
                        <h4>File System Errors (3000-3999)</h4>
                        <div class="error-item">
                            <span class="error-code">Error 3001</span>
                            <p class="error-desc">File upload failed</p>
                            <div class="error-solution">
                                <strong>Solution:</strong>
                                <ul>
                                    <li>Check directory permissions (755)</li>
                                    <li>Verify PHP upload_max_filesize setting</li>
                                    <li>Ensure sufficient disk space</li>
                                </ul>
                            </div>
                        </div>
                        <div class="error-item">
                            <span class="error-code">Error 3002</span>
                            <p class="error-desc">Configuration file not found</p>
                            <div class="error-solution">
                                <strong>Solution:</strong>
                                <ul>
                                    <li>Verify config.php exists in includes directory</li>
                                    <li>Check file permissions (644)</li>
                                    <li>Restore from backup if corrupted</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>3. Advanced Configuration</h3>
                <div class="advanced-config">
                    <h4>PHP Settings</h4>
                    <p>Recommended PHP configuration in php.ini:</p>
                    <div class="code-block">
                        <pre>
memory_limit = 256M
max_execution_time = 300
post_max_size = 64M
upload_max_filesize = 64M
display_errors = Off
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
date.timezone = UTC</pre>
                    </div>

                    <h4>Security Configuration</h4>
                    <div class="security-settings">
                        <div class="setting-item">
                            <h5>Session Security</h5>
                            <div class="code-block">
                                <pre>
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1</pre>
                            </div>
                        </div>
                        <div class="setting-item">
                            <h5>Error Handling</h5>
                            <div class="code-block">
                                <pre>
error_reporting = E_ALL
display_errors = Off
log_errors = On
error_log = /path/to/error.log</pre>
                            </div>
                        </div>
                    </div>
                </div>

                <h3>4. Troubleshooting Tips</h3>
                <div class="troubleshooting-grid">
                    <div class="tip-card">
                        <h4>Performance Issues</h4>
                        <ul>
                            <li>Enable PHP OpCache</li>
                            <li>Optimize MySQL queries</li>
                            <li>Use content caching</li>
                            <li>Compress static assets</li>
                        </ul>
                    </div>
                    <div class="tip-card">
                        <h4>Security Issues</h4>
                        <ul>
                            <li>Keep all dependencies updated</li>
                            <li>Use prepared statements</li>
                            <li>Implement rate limiting</li>
                            <li>Enable HTTPS</li>
                        </ul>
                    </div>
                    <div class="tip-card">
                        <h4>Common Fixes</h4>
                        <ul>
                            <li>Clear cache directories</li>
                            <li>Reset file permissions</li>
                            <li>Verify .htaccess rules</li>
                            <li>Check error logs</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Page -->
        <section class="content-page" id="contact">
            <div class="page-header">
                <h2>Contact Information</h2>
                <div class="page-indicator">5</div>
            </div>

            <div class="page-content">
                <div class="contact-intro">
                    <h3>1. Get in Touch</h3>
                    <p>
                        We welcome the opportunity to discuss our investment opportunity in more detail. Please feel free to contact us
                        using the information below to schedule a demonstration or to request additional information.
                    </p>
                </div>

                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i data-feather="user"></i>
                        </div>
                        <div class="contact-info">
                            <h4>Tembi Kelvin</h4>
                            <p>Founder & CEO</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i data-feather="mail"></i>
                        </div>
                        <div class="contact-info">
                            <h4>Email</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i data-feather="phone"></i>
                        </div>
                        <div class="contact-info">
                            <h4>Phone</h4>
                            <p>+237 676 428 445</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">
                            <i data-feather="globe"></i>
                        </div>
                        <div class="contact-info">
                            <h4>Website</h4>
                            <p><a href="https://tembikelvin.github.io/" target="_blank">https://tembikelvin.github.io/</a></p>
                        </div>
                    </div>
                </div>

                <div class="cta-section">
                    <h3>2. Ready to Transform Logistics?</h3>
                    <p>Join us in revolutionizing the global logistics industry with cutting-edge technology and innovative solutions.</p>
                    <div class="cta-buttons">
                        <a href="#" class="cta-button">Schedule a Demo</a>
                        <a href="#" class="cta-button secondary">Download Pitch Deck</a>
                    </div>
                </div>
            </div>

            <div class="page-footer">
                <div class="page-nav">
                    <a href="#business-model" class="page-nav-link"><i class="fas fa-arrow-left"></i> Business Model</a>
                    <a href="#cover" class="page-nav-link">Back to Cover <i class="fas fa-arrow-right"></i></a>
                </div>
            </div>
        </section>



        <!-- Navigation -->
        <div class="presentation-nav">
            <a href="#cover" class="nav-button" title="Cover Page">
                <i data-feather="home"></i>
            </a>
            <a href="#table-of-contents" class="nav-button" title="Table of Contents">
                <i data-feather="list"></i>
            </a>
            <a href="#executive-summary" class="nav-button" title="Executive Summary">
                <i data-feather="clipboard"></i>
            </a>
            <a href="#market-analysis" class="nav-button" title="Market Analysis">
                <i data-feather="trending-up"></i>
            </a>
            <a href="#introduction" class="nav-button" title="Introduction">
                <i data-feather="info"></i>
            </a>
            <a href="#features" class="nav-button" title="Features">
                <i data-feather="star"></i>
            </a>
            <a href="#requirements" class="nav-button" title="Requirements">
                <i data-feather="check-square"></i>
            </a>
            <a href="#installation" class="nav-button" title="Installation">
                <i data-feather="download"></i>
            </a>
            <a href="#product-showcase" class="nav-button" title="Product Showcase">
                <i data-feather="layout"></i>
            </a>
            <a href="#business-model" class="nav-button" title="Business Model">
                <i data-feather="dollar-sign"></i>
            </a>
            <a href="#configuration-setup" class="nav-button" title="Configuration">
                <i data-feather="settings"></i>
            </a>
            <a href="#contact" class="nav-button" title="Contact">
                <i data-feather="mail"></i>
            </a>
        </div>
    </div>
    <!-- JavaScript for Tab Functionality -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Tab functionality for Product Showcase
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanes = document.querySelectorAll('.tab-pane');

            // Set first tab as active by default
            if (tabButtons.length > 0 && tabPanes.length > 0) {
                tabButtons[0].classList.add('active');
                if (tabPanes[0]) {
                    tabPanes[0].classList.add('active');
                }
            }

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and panes
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanes.forEach(pane => pane.classList.remove('active'));

                    // Add active class to clicked button
                    this.classList.add('active');

                    // Get the tab to show
                    const tabToShow = this.getAttribute('data-tab');

                    // Show the corresponding tab pane
                    const tabPane = document.getElementById(tabToShow + '-tab');
                    if (tabPane) {
                        tabPane.classList.add('active');
                    }
                });
            });

            // Smooth scrolling for navigation links (disabled for PDF)
            const isPrintMode = window.matchMedia('print').matches ||
                                (document.location.href.indexOf('print-pdf') !== -1);

            if (!isPrintMode) {
                const navLinks = document.querySelectorAll('a[href^="#"]');

                navLinks.forEach(link => {
                    link.addEventListener('click', function(e) {
                        e.preventDefault();

                        const targetId = this.getAttribute('href');
                        const targetElement = document.querySelector(targetId);

                        if (targetElement) {
                            window.scrollTo({
                                top: targetElement.offsetTop,
                                behavior: 'smooth'
                            });
                        }
                    });
                });
            }
        });
    </script>

    <script>
        // Initialize Feather Icons
        document.addEventListener('DOMContentLoaded', function() {
            feather.replace();

            // Add print button for easy PDF generation
            const printButton = document.createElement('button');
            printButton.innerHTML = '<i data-feather="printer"></i> Save as PDF';
            printButton.className = 'print-button';
            printButton.style.position = 'fixed';
            printButton.style.bottom = '90px'; // Increased to avoid overlap with nav buttons
            printButton.style.right = '20px';
            printButton.style.zIndex = '98'; // Reduced z-index to be below nav buttons
            printButton.style.padding = '10px 15px';
            printButton.style.backgroundColor = '#4a6cf7';
            printButton.style.color = 'white';
            printButton.style.border = 'none';
            printButton.style.borderRadius = '5px';
            printButton.style.cursor = 'pointer';
            printButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
            printButton.style.display = 'flex';
            printButton.style.alignItems = 'center';
            printButton.style.gap = '5px';

            // Update the presentation-nav z-index to ensure it's above the print button
            const navStyle = document.createElement('style');
            navStyle.textContent = `
                .presentation-nav {
                    z-index: 99;
                }

                /* Add responsive positioning for print button */
                @media screen and (max-width: 768px) {
                    .print-button {
                        bottom: 120px; /* Even more space on mobile */
                    }
                }
            `;
            document.head.appendChild(navStyle);

            printButton.addEventListener('click', function() {
                // Hide the print button before printing
                this.style.display = 'none';

                // Set print options to hide headers and footers
                const style = document.createElement('style');
                style.innerHTML = `
                    @page {
                        margin: 2.5cm 2cm 2cm 2cm;
                        size: A4;
                    }
                    @page :first {
                        margin: 0;
                    }
                    @page {
                        @top-left { content: none; }
                        @top-center { content: none; }
                        @top-right { content: none; }
                        @bottom-left { content: none; }
                        @bottom-center { content: none; }
                        @bottom-right { content: none; }
                    }
                    /* Special handling for mobile view image to limit its size */
                    #mobile-tab .showcase-image img {
                        max-height: 16cm !important; /* Limit height to fit on at most 2 pages */
                        object-fit: cover !important; /* Show only part of the image if needed */
                        object-position: top !important; /* Show the top part of the image */
                    }

                    /* Match the original layout for showcase items */
                    .showcase-item {
                        display: grid !important;
                        grid-template-columns: 1fr 1fr !important;
                        gap: 2rem !important;
                        page-break-inside: avoid !important;
                        break-inside: avoid !important;
                    }

                    .showcase-item.reverse {
                        direction: rtl !important;
                    }

                    .showcase-item.reverse .showcase-description {
                        direction: ltr !important;
                    }

                    /* Ensure cards are visible */
                    .card, .grid-item, .highlight-card, .metric-item, .audience-item, .challenge-item, .segment {
                        page-break-inside: avoid !important;
                        break-inside: avoid !important;
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                    }

                    /* Preserve grid layouts */
                    .grid, .card-grid, .metrics-grid, .audience-grid, .challenges-grid, .segments-grid {
                        display: grid !important;
                        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
                        gap: 1rem !important;
                        margin-bottom: 1cm !important;
                    }
                `;
                document.head.appendChild(style);

                // Trigger print dialog
                window.print();

                // Show the button again after print dialog is closed
                setTimeout(() => {
                    this.style.display = 'flex';
                    feather.replace();
                    // Remove the temporary style
                    document.head.removeChild(style);
                }, 1000);
            });

            document.body.appendChild(printButton);
            feather.replace();
        });

        // Backup initialization in case DOMContentLoaded already fired
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            setTimeout(function() {
                feather.replace();
            }, 100);
        }

        // Add event listener for beforeprint to prepare document
        window.addEventListener('beforeprint', function() {
            // Any preparation needed before printing
            document.querySelectorAll('.print-button').forEach(btn => {
                btn.style.display = 'none';
            });
        });

        // Add event listener for afterprint to restore document
        window.addEventListener('afterprint', function() {
            // Restore any elements after printing
            setTimeout(() => {
                document.querySelectorAll('.print-button').forEach(btn => {
                    btn.style.display = 'flex';
                });
                feather.replace();
            }, 100);
        });
    </script>
</body>
</html>











