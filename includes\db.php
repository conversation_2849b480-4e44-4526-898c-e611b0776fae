<?php
// Create PDO connection
try {
    $conn = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    die("Connection failed: " . $e->getMessage());
}

// Database wrapper class
class DB {
    private $pdo;
    private $stmt;
    private $error;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    // Prepare statement with query
    public function query($sql) {
        $this->stmt = $this->pdo->prepare($sql);
        return $this;
    }

    // Bind values
    public function bind($param, $value, $type = null) {
        if(is_null($type)) {
            switch(true) {
                case is_int($value):
                    $type = PDO::PARAM_INT;
                    break;
                case is_bool($value):
                    $type = PDO::PARAM_BOOL;
                    break;
                case is_null($value):
                    $type = PDO::PARAM_NULL;
                    break;
                default:
                    $type = PDO::PARAM_STR;
            }
        }

        $this->stmt->bindValue($param, $value, $type);
        return $this;
    }

    // Execute the prepared statement
    public function execute() {
        try {
            return $this->stmt->execute();
        } catch(PDOException $e) {
            $this->error = $e->getMessage();
            error_log("Database execution error: " . $this->error);
            return false;
        }
    }

    // Get result set as array of objects
    public function resultSet() {
        if (!$this->execute()) {
            error_log("Execute failed in resultSet(): " . $this->error);
            return false;
        }

        $results = $this->stmt->fetchAll();
        if (empty($results)) {
            // No rows returned, but query executed successfully
            error_log("No rows returned in resultSet()");
            return [];
        }

        return $results;
    }

    // Get single record as object
    public function single() {
        if (!$this->execute()) {
            error_log("Execute failed in single(): " . $this->error);
            return false;
        }

        $result = $this->stmt->fetch();
        if ($result === false) {
            // No rows returned, but query executed successfully
            error_log("No rows returned in single()");
            return [];
        }

        return $result;
    }

    // Get row count
    public function rowCount() {
        return $this->stmt->rowCount();
    }

    // Get last insert ID
    public function lastInsertId() {
        return $this->pdo->lastInsertId();
    }
}

// Initialize DB object
$db = new DB($conn);
