<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in and is admin
if(!isLoggedIn()) {
    setMessage('Please login to access the dashboard', 'error');
    redirect('../index.php');
}

if(!isAdmin()) {
    setMessage('You do not have permission to access this page', 'error');
    redirect('index.php');
}

// Process form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Add/Edit User
    if (isset($_POST['action']) && ($_POST['action'] === 'add' || $_POST['action'] === 'edit')) {
        $username = sanitize($_POST['username']);
        $email = sanitize($_POST['email']);
        $role = sanitize($_POST['role']);
        $password = $_POST['password'] ?? '';
        $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;

        // Validate input
        if (empty($username) || empty($email) || empty($role)) {
            setMessage('All fields are required', 'error');
        } else {
            // Check if username already exists (for new users)
            if ($_POST['action'] === 'add') {
                $db->query("SELECT id FROM users WHERE username = :username");
                $db->bind(':username', $username);
                $existingUser = $db->single();

                if ($existingUser) {
                    setMessage('Username already exists', 'error');
                } else if (empty($password)) {
                    setMessage('Password is required for new users', 'error');
                } else {
                    // Add new user
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $db->query("INSERT INTO users (username, email, password, role) VALUES (:username, :email, :password, :role)");
                    $db->bind(':username', $username);
                    $db->bind(':email', $email);
                    $db->bind(':password', $hashedPassword);
                    $db->bind(':role', $role);

                    if ($db->execute()) {
                        setMessage('User added successfully', 'success');
                    } else {
                        setMessage('Error adding user', 'error');
                    }
                }
            } else {
                // Edit existing user
                if (!empty($password)) {
                    // Update with new password
                    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                    $db->query("UPDATE users SET username = :username, email = :email, password = :password, role = :role WHERE id = :id");
                    $db->bind(':password', $hashedPassword);
                } else {
                    // Update without changing password
                    $db->query("UPDATE users SET username = :username, email = :email, role = :role WHERE id = :id");
                }

                $db->bind(':username', $username);
                $db->bind(':email', $email);
                $db->bind(':role', $role);
                $db->bind(':id', $userId);

                if ($db->execute()) {
                    setMessage('User updated successfully', 'success');
                } else {
                    setMessage('Error updating user', 'error');
                }
            }
        }
    }

    // Delete User
    if (isset($_POST['action']) && $_POST['action'] === 'delete') {
        $userId = (int)$_POST['user_id'];

        // Prevent deleting your own account
        if ($userId === (int)$_SESSION['user_id']) {
            setMessage('You cannot delete your own account', 'error');
        } else {
            $db->query("DELETE FROM users WHERE id = :id");
            $db->bind(':id', $userId);

            if ($db->execute()) {
                setMessage('User deleted successfully', 'success');
            } else {
                setMessage('Error deleting user', 'error');
            }
        }
    }

    // Redirect to prevent form resubmission
    redirect('manage-users.php');
}

// Get users with pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

$db->query("SELECT COUNT(*) as total FROM users");
$totalUsers = $db->single()['total'];
$totalPages = ceil($totalUsers / $perPage);

$db->query("SELECT * FROM users ORDER BY created_at DESC LIMIT :offset, :per_page");
$db->bind(':offset', $offset, PDO::PARAM_INT);
$db->bind(':per_page', $perPage, PDO::PARAM_INT);
$users = $db->resultSet();

// Include header
include_once '../includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-users"></i> Manage Users</h1>
            <div class="admin-actions">
                <button class="btn add-btn" data-toggle="modal" data-target="#addUserModal"><i class="fas fa-plus"></i> Add User</button>
                <a href="index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <?php displayMessage(); ?>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Users</h2>
                    <div class="card-actions">
                        <div class="search-box">
                            <input type="text" id="userSearch" placeholder="Search users...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($users)): ?>
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <p>No users found</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table mobile-card-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Username</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td><?php echo $user['id']; ?></td>
                                            <td><?php echo $user['username']; ?></td>
                                            <td><?php echo $user['email']; ?></td>
                                            <td>
                                                <span class="badge <?php echo $user['role']; ?>">
                                                    <?php echo ucfirst($user['role']); ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatDate($user['created_at']); ?></td>
                                            <td class="actions">
                                                <button class="btn-icon edit-btn" data-toggle="modal" data-target="#editUserModal"
                                                        data-id="<?php echo $user['id']; ?>"
                                                        data-username="<?php echo $user['username']; ?>"
                                                        data-email="<?php echo $user['email']; ?>"
                                                        data-role="<?php echo $user['role']; ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <?php if ($user['id'] !== $_SESSION['user_id']): ?>
                                                    <button class="btn-icon delete-btn" data-toggle="modal" data-target="#deleteUserModal" data-id="<?php echo $user['id']; ?>">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <?php if ($totalPages > 1): ?>
                            <div class="pagination">
                                <?php if ($page > 1): ?>
                                    <a href="?page=<?php echo $page - 1; ?>" class="page-link"><i class="fas fa-chevron-left"></i></a>
                                <?php endif; ?>

                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <a href="?page=<?php echo $i; ?>" class="page-link <?php echo $i === $page ? 'active' : ''; ?>"><?php echo $i; ?></a>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <a href="?page=<?php echo $page + 1; ?>" class="page-link"><i class="fas fa-chevron-right"></i></a>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add User Modal -->
<div class="modal" id="addUserModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Add User</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <form action="manage-users.php" method="POST">
                <input type="hidden" name="action" value="add">

                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                </div>

                <div class="form-group">
                    <label for="role">Role</label>
                    <select id="role" name="role" required>
                        <option value="admin">Admin</option>
                        <option value="staff">Staff</option>
                        <option value="customer">Customer</option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn submit-btn">Add User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal" id="editUserModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Edit User</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <form action="manage-users.php" method="POST">
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="user_id" id="edit_user_id">

                <div class="form-group">
                    <label for="edit_username">Username</label>
                    <input type="text" id="edit_username" name="username" required>
                </div>

                <div class="form-group">
                    <label for="edit_email">Email</label>
                    <input type="email" id="edit_email" name="email" required>
                </div>

                <div class="form-group">
                    <label for="edit_password">Password (leave blank to keep current)</label>
                    <input type="password" id="edit_password" name="password">
                </div>

                <div class="form-group">
                    <label for="edit_role">Role</label>
                    <select id="edit_role" name="role" required>
                        <option value="admin">Admin</option>
                        <option value="staff">Staff</option>
                        <option value="customer">Customer</option>
                    </select>
                </div>

                <div class="form-actions">
                    <button type="button" class="btn cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn submit-btn">Update User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Modal -->
<div class="modal" id="deleteUserModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Delete User</h2>
            <button class="close-modal">&times;</button>
        </div>
        <div class="modal-body">
            <p>Are you sure you want to delete this user? This action cannot be undone.</p>
            <form action="manage-users.php" method="POST">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="user_id" id="delete_user_id">

                <div class="form-actions">
                    <button type="button" class="btn cancel-btn" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn delete-btn">Delete User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Modal functionality
    const modals = document.querySelectorAll('.modal');
    const modalTriggers = document.querySelectorAll('[data-toggle="modal"]');
    const modalClosers = document.querySelectorAll('.close-modal, .cancel-btn, [data-dismiss="modal"]');

    // Open modal
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function() {
            const modalId = this.getAttribute('data-target');
            const modal = document.querySelector(modalId);

            if (modal) {
                modal.style.display = 'block';

                // If it's an edit button, populate the form
                if (this.classList.contains('edit-btn')) {
                    document.getElementById('edit_user_id').value = this.getAttribute('data-id');
                    document.getElementById('edit_username').value = this.getAttribute('data-username');
                    document.getElementById('edit_email').value = this.getAttribute('data-email');
                    document.getElementById('edit_role').value = this.getAttribute('data-role');
                }

                // If it's a delete button, set the user ID
                if (this.classList.contains('delete-btn')) {
                    document.getElementById('delete_user_id').value = this.getAttribute('data-id');
                }
            }
        });
    });

    // Close modal
    modalClosers.forEach(closer => {
        closer.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.style.display = 'none';
            }
        });
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        modals.forEach(modal => {
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        });
    });

    // User search functionality
    const userSearch = document.getElementById('userSearch');
    if (userSearch) {
        userSearch.addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const tableRows = document.querySelectorAll('tbody tr');

            tableRows.forEach(row => {
                const username = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                const email = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                const role = row.querySelector('td:nth-child(4)').textContent.toLowerCase();

                if (username.includes(searchValue) || email.includes(searchValue) || role.includes(searchValue)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
});
</script>

<?php
// Add mobile-friendly CSS and JS
echo "<style>";
readfile(__DIR__ . '/../assets/css/admin-mobile.css');
readfile(__DIR__ . '/../assets/css/sidebar-fix.css'); // Add sidebar fix CSS
echo "</style>";

echo "<script>";
readfile(__DIR__ . '/../assets/js/admin-mobile.js');
echo "</script>";

// Add additional script to ensure sidebar close button works
echo "<script>
// Ensure sidebar close button is properly initialized
document.addEventListener('DOMContentLoaded', function() {
    // Check if sidebar close button exists, if not create it
    const dashboardSidebar = document.querySelector('.dashboard-sidebar');
    if (dashboardSidebar && !dashboardSidebar.querySelector('.sidebar-close')) {
        const closeButton = document.createElement('button');
        closeButton.className = 'sidebar-close';
        closeButton.innerHTML = '<i class=\"fas fa-times\"></i>';
        closeButton.setAttribute('aria-label', 'Close Sidebar');
        closeButton.style.cursor = 'pointer';
        dashboardSidebar.prepend(closeButton);

        // Add event listener to close button
        closeButton.addEventListener('click', function() {
            dashboardSidebar.classList.remove('active');
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.classList.remove('active');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            if (sidebarToggle) sidebarToggle.classList.remove('active');
            document.body.style.overflow = '';
        });

        console.log('Sidebar close button initialized');
    }
});
</script>";

include_once '../includes/footer.php';
?>

<!-- Script to handle action parameter -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if there's an action parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');

    // If action is 'add', open the add user modal
    if (action === 'add') {
        const addUserModal = document.getElementById('addUserModal');
        if (addUserModal) {
            addUserModal.style.display = 'block';
        }
    }
});
</script>

<!-- Mobile Admin Enhancements -->
<script src="../assets/js/mobile-admin.js"></script>
