/**
 * Route Generator Utility
 * Generates realistic routes between origin and destination points
 */

/**
 * Generate intermediate points along a route between origin and destination
 * @param {Array} origin - [lat, lng] of origin point
 * @param {Array} destination - [lat, lng] of destination point
 * @param {Number} numPoints - Number of intermediate points to generate (default: 3)
 * @param {Number} deviation - Maximum deviation from straight line (default: 0.5)
 * @return {Array} Array of [lat, lng] points including origin, intermediate points, and destination
 */
function generateRoutePoints(origin, destination, numPoints = 3, deviation = 0.5) {
    // Ensure we have valid coordinates
    if (!origin || !destination || !Array.isArray(origin) || !Array.isArray(destination)) {
        console.error('Invalid origin or destination coordinates');
        return [origin, destination]; // Return direct route if invalid
    }
    
    const route = [origin];
    
    // Calculate distance and bearing between origin and destination
    const distance = calculateDistance(origin[0], origin[1], destination[0], destination[1]);
    const bearing = calculateBearing(origin[0], origin[1], destination[0], destination[1]);
    
    // Generate intermediate points
    for (let i = 1; i <= numPoints; i++) {
        // Calculate position along the direct route (0 to 1)
        const fraction = i / (numPoints + 1);
        
        // Calculate point along the direct route
        const directPoint = calculateIntermediatePoint(
            origin[0], origin[1], 
            destination[0], destination[1], 
            fraction
        );
        
        // Add some randomness to the route
        // Deviation is higher in the middle of the route and lower near endpoints
        const deviationFactor = Math.sin(fraction * Math.PI) * deviation;
        
        // Calculate perpendicular bearing
        const perpBearing = (bearing + 90) % 360;
        
        // Random deviation (positive or negative)
        const randomDev = (Math.random() * 2 - 1) * deviationFactor;
        
        // Calculate deviated point
        const deviatedPoint = calculateDestinationPoint(
            directPoint[0], directPoint[1],
            Math.abs(randomDev) * distance, // Scale deviation by distance
            randomDev > 0 ? perpBearing : (perpBearing + 180) % 360
        );
        
        route.push(deviatedPoint);
    }
    
    route.push(destination);
    return route;
}

/**
 * Calculate the distance between two points in kilometers using the Haversine formula
 */
function calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radius of the Earth in km
    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);
    const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * 
        Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
}

/**
 * Calculate the bearing between two points in degrees
 */
function calculateBearing(lat1, lon1, lat2, lon2) {
    const y = Math.sin(toRadians(lon2 - lon1)) * Math.cos(toRadians(lat2));
    const x = Math.cos(toRadians(lat1)) * Math.sin(toRadians(lat2)) -
              Math.sin(toRadians(lat1)) * Math.cos(toRadians(lat2)) * Math.cos(toRadians(lon2 - lon1));
    const bearing = toDegrees(Math.atan2(y, x));
    return (bearing + 360) % 360;
}

/**
 * Calculate an intermediate point along a great circle path
 */
function calculateIntermediatePoint(lat1, lon1, lat2, lon2, fraction) {
    lat1 = toRadians(lat1);
    lon1 = toRadians(lon1);
    lat2 = toRadians(lat2);
    lon2 = toRadians(lon2);
    
    const d = 2 * Math.asin(
        Math.sqrt(
            Math.pow(Math.sin((lat1 - lat2) / 2), 2) +
            Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin((lon1 - lon2) / 2), 2)
        )
    );
    
    const A = Math.sin((1 - fraction) * d) / Math.sin(d);
    const B = Math.sin(fraction * d) / Math.sin(d);
    
    const x = A * Math.cos(lat1) * Math.cos(lon1) + B * Math.cos(lat2) * Math.cos(lon2);
    const y = A * Math.cos(lat1) * Math.sin(lon1) + B * Math.cos(lat2) * Math.sin(lon2);
    const z = A * Math.sin(lat1) + B * Math.sin(lat2);
    
    const lat = Math.atan2(z, Math.sqrt(Math.pow(x, 2) + Math.pow(y, 2)));
    const lon = Math.atan2(y, x);
    
    return [toDegrees(lat), toDegrees(lon)];
}

/**
 * Calculate destination point given start point, distance, and bearing
 */
function calculateDestinationPoint(lat, lon, distance, bearing) {
    const R = 6371; // Earth's radius in km
    const d = distance / R; // Angular distance
    const bearingRad = toRadians(bearing);
    const latRad = toRadians(lat);
    const lonRad = toRadians(lon);
    
    const sinLat2 = Math.sin(latRad) * Math.cos(d) + Math.cos(latRad) * Math.sin(d) * Math.cos(bearingRad);
    const lat2 = Math.asin(sinLat2);
    const y = Math.sin(bearingRad) * Math.sin(d) * Math.cos(latRad);
    const x = Math.cos(d) - Math.sin(latRad) * sinLat2;
    const lon2 = lonRad + Math.atan2(y, x);
    
    return [toDegrees(lat2), toDegrees(lon2)];
}

/**
 * Convert degrees to radians
 */
function toRadians(degrees) {
    return degrees * Math.PI / 180;
}

/**
 * Convert radians to degrees
 */
function toDegrees(radians) {
    return radians * 180 / Math.PI;
}

/**
 * Generate timestamps for route points
 * @param {Array} points - Array of route points
 * @param {Date} startDate - Start date/time
 * @param {Date} endDate - End date/time (optional)
 * @return {Array} Array of timestamps
 */
function generateTimestamps(points, startDate, endDate = null) {
    if (!points || points.length < 2) return [];
    
    const timestamps = [];
    const start = startDate instanceof Date ? startDate : new Date(startDate);
    
    // If end date is provided, distribute timestamps evenly
    if (endDate) {
        const end = endDate instanceof Date ? endDate : new Date(endDate);
        const totalDuration = end.getTime() - start.getTime();
        
        for (let i = 0; i < points.length; i++) {
            const fraction = i / (points.length - 1);
            const timestamp = new Date(start.getTime() + totalDuration * fraction);
            timestamps.push(timestamp);
        }
    } 
    // Otherwise, generate reasonable timestamps based on distance
    else {
        let currentTime = start.getTime();
        timestamps.push(new Date(currentTime));
        
        for (let i = 1; i < points.length; i++) {
            const distance = calculateDistance(
                points[i-1][0], points[i-1][1],
                points[i][0], points[i][1]
            );
            
            // Assume average speed of 60 km/h = 1 km/min
            const timeInMinutes = distance * 60; // Convert to minutes
            currentTime += timeInMinutes * 60 * 1000; // Convert to milliseconds
            timestamps.push(new Date(currentTime));
        }
    }
    
    return timestamps;
}

/**
 * Generate status descriptions for route points
 * @param {Number} numPoints - Number of points
 * @return {Array} Array of status descriptions
 */
function generateStatusDescriptions(numPoints) {
    const statuses = [];
    
    // First point is always origin
    statuses.push('Shipment picked up');
    
    // Generate intermediate statuses
    const intermediateStatuses = [
        'In transit',
        'Arrived at sorting facility',
        'Departed from sorting facility',
        'In transit to next facility',
        'Arrived at distribution center',
        'Departed from distribution center',
        'In transit to destination',
        'Out for delivery'
    ];
    
    // Randomly select statuses for intermediate points
    for (let i = 1; i < numPoints - 1; i++) {
        const randomIndex = Math.floor(Math.random() * intermediateStatuses.length);
        statuses.push(intermediateStatuses[randomIndex]);
    }
    
    // Last point is always destination
    statuses.push('Delivered');
    
    return statuses;
}

/**
 * Generate a complete route with coordinates, timestamps, and statuses
 * @param {Array} origin - [lat, lng] of origin point
 * @param {Array} destination - [lat, lng] of destination point
 * @param {Object} options - Configuration options
 * @return {Array} Array of route points with coordinates, timestamps, and statuses
 */
function generateCompleteRoute(origin, destination, options = {}) {
    const {
        numPoints = 3,
        deviation = 0.5,
        startDate = new Date(),
        endDate = null,
        originLocation = 'Origin',
        destinationLocation = 'Destination'
    } = options;
    
    // Generate route points
    const points = generateRoutePoints(origin, destination, numPoints, deviation);
    
    // Generate timestamps
    const timestamps = generateTimestamps(points, startDate, endDate);
    
    // Generate statuses
    const statuses = generateStatusDescriptions(points.length);
    
    // Generate locations (first and last are provided, others are generic)
    const locations = [];
    locations.push(originLocation);
    
    for (let i = 1; i < points.length - 1; i++) {
        locations.push(`Transit Point ${i}`);
    }
    
    locations.push(destinationLocation);
    
    // Combine everything into a complete route
    return points.map((point, index) => ({
        latitude: point[0],
        longitude: point[1],
        timestamp: timestamps[index].toISOString(),
        status: statuses[index],
        location: locations[index],
        notes: index === 0 ? 'Shipment has been picked up from origin' :
               index === points.length - 1 ? 'Shipment has been delivered to destination' : ''
    }));
}
