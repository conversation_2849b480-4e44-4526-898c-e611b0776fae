// Admin JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any admin-specific functionality

    // Handle form submissions with confirmation
    const confirmForms = document.querySelectorAll('.confirm-form');
    confirmForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const message = this.getAttribute('data-confirm') || 'Are you sure you want to perform this action?';
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Handle delete buttons with confirmation
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const message = this.getAttribute('data-confirm') || 'Are you sure you want to delete this item?';
            if (!confirm(message)) {
                e.preventDefault();
            }
        });
    });

    // Initialize datepickers if available
    if (typeof flatpickr !== 'undefined') {
        flatpickr('.datepicker', {
            enableTime: false,
            dateFormat: 'Y-m-d'
        });

        flatpickr('.datetimepicker', {
            enableTime: true,
            dateFormat: 'Y-m-d H:i'
        });
    }

    // Initialize select2 if available
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            theme: 'bootstrap4'
        });
    }

    // Auto-hide messages after 5 seconds
    const messages = document.querySelectorAll('.message');
    messages.forEach(message => {
        setTimeout(() => {
            message.style.opacity = '0';
            setTimeout(() => {
                message.style.display = 'none';
            }, 500);
        }, 5000);
    });

    // Add refresh button functionality for dashboard stats
    const refreshButtons = document.querySelectorAll('.refresh-stats');
    refreshButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const chartContainer = this.closest('.chart-container');
            if (chartContainer) {
                // Add loading state
                const chartWrapper = chartContainer.querySelector('.chart-wrapper');
                if (chartWrapper) {
                    // Create and add loading overlay
                    const loadingOverlay = document.createElement('div');
                    loadingOverlay.className = 'loading-overlay';
                    loadingOverlay.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                    chartWrapper.appendChild(loadingOverlay);

                    // Get chart ID
                    const canvas = chartWrapper.querySelector('canvas');
                    if (canvas) {
                        const chartId = canvas.id;

                        // Make AJAX request to refresh data
                        fetch('refresh-chart.php?chart=' + chartId, {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            // Remove loading overlay
                            loadingOverlay.remove();

                            // Update chart with new data
                            if (window[chartId + 'Chart']) {
                                window[chartId + 'Chart'].data = data;
                                window[chartId + 'Chart'].update();
                            }

                            // Show success message
                            const message = document.createElement('div');
                            message.className = 'message success';
                            message.textContent = 'Chart refreshed successfully';
                            document.body.appendChild(message);

                            // Auto-hide the message
                            setTimeout(() => {
                                message.style.opacity = '0';
                                setTimeout(() => {
                                    message.remove();
                                }, 500);
                            }, 3000);
                        })
                        .catch(error => {
                            // Remove loading overlay
                            loadingOverlay.remove();

                            // Show error message
                            const message = document.createElement('div');
                            message.className = 'message error';
                            message.textContent = 'Error refreshing chart: ' + error.message;
                            document.body.appendChild(message);

                            // Auto-hide the message
                            setTimeout(() => {
                                message.style.opacity = '0';
                                setTimeout(() => {
                                    message.remove();
                                }, 500);
                            }, 3000);
                        });
                    } else {
                        // If no canvas found, just remove loading and show message
                        loadingOverlay.remove();
                        window.location.reload(); // Fallback to page reload
                    }
                } else {
                    // If no chart wrapper found, just reload the page
                    window.location.reload();
                }
            } else {
                // If button is not in a chart container, just reload the page
                window.location.reload();
            }
        });
    });

    // Sidebar toggle functionality is now handled in admin-mobile.js

    // Add tooltip functionality
    const tooltips = document.querySelectorAll('[data-tooltip]');
    tooltips.forEach(tooltip => {
        tooltip.addEventListener('mouseenter', function() {
            const tooltipText = this.getAttribute('data-tooltip');
            const tooltipEl = document.createElement('div');
            tooltipEl.className = 'tooltip';
            tooltipEl.textContent = tooltipText;
            document.body.appendChild(tooltipEl);

            const rect = this.getBoundingClientRect();
            tooltipEl.style.top = rect.top - tooltipEl.offsetHeight - 10 + 'px';
            tooltipEl.style.left = rect.left + (rect.width / 2) - (tooltipEl.offsetWidth / 2) + 'px';
            tooltipEl.style.opacity = '1';
        });

        tooltip.addEventListener('mouseleave', function() {
            const tooltipEl = document.querySelector('.tooltip');
            if (tooltipEl) {
                tooltipEl.style.opacity = '0';
                setTimeout(() => {
                    tooltipEl.remove();
                }, 300);
            }
        });
    });
});
