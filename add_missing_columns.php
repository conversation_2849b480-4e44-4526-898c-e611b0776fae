<?php
/**
 * Add Missing Columns to Shipments Table
 * 
 * This script adds missing columns to the shipments table
 */
require_once 'includes/config.php';
require_once 'includes/db.php';
require_once 'includes/functions.php';

// Check if user is admin
if(!isAdmin()) {
    setErrorNotification('You do not have permission to access this page');
    redirect('index.php');
    exit;
}

// Set page title
$pageTitle = 'Add Missing Columns';

// Include header
include_once 'includes/header.php';
?>

<section class="admin-section" style="padding-top: 100px;">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-database"></i> Add Missing Columns to Shipments Table</h1>
            <div class="admin-actions">
                <a href="admin/index.php" class="btn back-btn"><i class="fas fa-arrow-left"></i> Back to Dashboard</a>
            </div>
        </div>

        <div class="admin-content">
            <div class="card">
                <div class="card-header">
                    <h2>Adding Missing Columns</h2>
                </div>
                <div class="card-body">
                    <?php
                    try {
                        // Get the database connection
                        global $conn;
                        
                        // Get the current table structure
                        $stmt = $conn->query("DESCRIBE shipments");
                        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        // Create an array of existing column names
                        $existingColumns = array_column($columns, 'Field');
                        
                        // Define the columns that should be in the table
                        $requiredColumns = [
                            'shopper_name' => "ALTER TABLE shipments ADD COLUMN shopper_name VARCHAR(100) NULL AFTER estimated_delivery",
                            'shopper_email' => "ALTER TABLE shipments ADD COLUMN shopper_email VARCHAR(100) NULL AFTER shopper_name",
                            'shopper_phone' => "ALTER TABLE shipments ADD COLUMN shopper_phone VARCHAR(20) NULL AFTER shopper_email",
                            'shopper_address' => "ALTER TABLE shipments ADD COLUMN shopper_address TEXT NULL AFTER shopper_phone",
                            'receiver_name' => "ALTER TABLE shipments ADD COLUMN receiver_name VARCHAR(100) NULL AFTER shopper_address",
                            'receiver_email' => "ALTER TABLE shipments ADD COLUMN receiver_email VARCHAR(100) NULL AFTER receiver_name",
                            'receiver_phone' => "ALTER TABLE shipments ADD COLUMN receiver_phone VARCHAR(20) NULL AFTER receiver_email",
                            'receiver_address' => "ALTER TABLE shipments ADD COLUMN receiver_address TEXT NULL AFTER receiver_phone",
                            'package_weight' => "ALTER TABLE shipments ADD COLUMN package_weight DECIMAL(10,2) NULL AFTER receiver_address",
                            'package_dimensions' => "ALTER TABLE shipments ADD COLUMN package_dimensions VARCHAR(50) NULL AFTER package_weight",
                            'shipping_service' => "ALTER TABLE shipments ADD COLUMN shipping_service VARCHAR(50) NULL AFTER package_dimensions",
                            'shipping_cost' => "ALTER TABLE shipments ADD COLUMN shipping_cost DECIMAL(10,2) NULL AFTER shipping_service",
                            'package_picture' => "ALTER TABLE shipments ADD COLUMN package_picture VARCHAR(255) NULL AFTER shipping_cost"
                        ];
                        
                        // Check which columns are missing and add them
                        $addedColumns = [];
                        $existingRequiredColumns = [];
                        
                        foreach ($requiredColumns as $column => $alterSql) {
                            if (!in_array($column, $existingColumns)) {
                                // Column is missing, add it
                                $conn->exec($alterSql);
                                $addedColumns[] = $column;
                            } else {
                                $existingRequiredColumns[] = $column;
                            }
                        }
                        
                        // Display results
                        if (count($addedColumns) > 0) {
                            echo "<div class='alert alert-success'><i class='fas fa-check-circle'></i> Successfully added the following columns to the shipments table:</div>";
                            echo "<ul>";
                            foreach ($addedColumns as $column) {
                                echo "<li>" . htmlspecialchars($column) . "</li>";
                            }
                            echo "</ul>";
                        } else {
                            echo "<div class='alert alert-info'><i class='fas fa-info-circle'></i> No columns needed to be added. All required columns already exist.</div>";
                        }
                        
                        if (count($existingRequiredColumns) > 0) {
                            echo "<div class='alert alert-info'><i class='fas fa-info-circle'></i> The following required columns already exist in the shipments table:</div>";
                            echo "<ul>";
                            foreach ($existingRequiredColumns as $column) {
                                echo "<li>" . htmlspecialchars($column) . "</li>";
                            }
                            echo "</ul>";
                        }
                        
                        // Get the updated table structure
                        $stmt = $conn->query("DESCRIBE shipments");
                        $updatedColumns = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        
                        echo "<h3>Updated Shipments Table Structure</h3>";
                        echo "<div class='table-responsive'>";
                        echo "<table class='table'>";
                        echo "<thead><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr></thead>";
                        echo "<tbody>";
                        
                        foreach ($updatedColumns as $column) {
                            echo "<tr>";
                            echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
                            echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
                            echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
                            echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
                            echo "<td>" . (isset($column['Default']) ? htmlspecialchars($column['Default']) : 'NULL') . "</td>";
                            echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                        echo "</div>";
                        
                    } catch (PDOException $e) {
                        echo "<div class='alert alert-danger'><i class='fas fa-times-circle'></i> Database error: " . $e->getMessage() . "</div>";
                    }
                    ?>
                </div>
            </div>
        </div>
        
        <div class="admin-actions mt-4">
            <a href="admin/manage-shipments.php" class="btn primary-btn"><i class="fas fa-box"></i> Manage Shipments</a>
            <a href="admin/index.php" class="btn secondary-btn"><i class="fas fa-arrow-left"></i> Return to Dashboard</a>
        </div>
    </div>
</section>

<style>
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 8px;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
    color: #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
    color: #dc3545;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-left: 4px solid #ffc107;
    color: #856404;
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-left: 4px solid #17a2b8;
    color: #17a2b8;
}

code, pre {
    background-color: rgba(0, 0, 0, 0.05);
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

pre {
    padding: 10px;
    overflow-x: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
}

.mt-4 {
    margin-top: 20px;
}

.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 8px;
    border-bottom: 1px solid #ddd;
    text-align: left;
}

.table th {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-responsive {
    overflow-x: auto;
}
</style>

<?php include_once 'includes/footer.php'; ?>
