-- Add package picture field to shipments table
ALTER TABLE shipments ADD COLUMN package_picture VARCHAR(255) DEFAULT NULL;

-- Add shopper info fields to shipments table
ALTER TABLE shipments ADD COLUMN shopper_name VARCHAR(100) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN shopper_email VARCHAR(100) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN shopper_phone VARCHAR(50) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN shopper_address TEXT DEFAULT NULL;

-- Add receiver info fields to shipments table
ALTER TABLE shipments ADD COLUMN receiver_name VARCHAR(100) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN receiver_email VARCHAR(100) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN receiver_phone VARCHAR(50) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN receiver_address TEXT DEFAULT NULL;

-- Add additional shipment info fields
ALTER TABLE shipments ADD COLUMN package_weight DECIMAL(10,2) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN package_dimensions VARCHAR(50) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN shipping_service VARCHAR(100) DEFAULT NULL;
ALTER TABLE shipments ADD COLUMN shipping_cost DECIMAL(10,2) DEFAULT NULL;
