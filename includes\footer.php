    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-grid">
                <div class="footer-col">
                    <h3><?php echo htmlspecialchars(getSetting('site_name', 'Global TransLogix')); ?></h3>
                    <p>Your trusted partner in global logistics and transportation solutions.</p>
                    <div class="social-icons">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>

                <div class="footer-col">
                    <h4>Services</h4>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#ocean-freight">Ocean Freight</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#land-transport">Land Transport</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#air-freight">Air Freight</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#warehousing">Warehousing</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#supply-chain">Supply Chain</a></li>
                    </ul>
                </div>

                <div class="footer-col">
                    <h4>Industries</h4>
                    <ul>
                        <li><a href="<?php echo SITE_URL; ?>/industries.php#retail">Retail</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/industries.php#automotive">Automotive</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/industries.php#healthcare">Healthcare</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/industries.php#chemicals">Chemicals</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/industries.php#technology">Technology</a></li>
                    </ul>
                </div>

                <div class="footer-col">
                    <h4>Contact Us</h4>
                    <ul class="footer-nav-links">
                        <li><i class="fas fa-map-marker-alt"></i> 123 Logistics Way, Transport City</li>
                        <li><i class="fas fa-phone"></i> <?php echo htmlspecialchars(getSetting('contact_phone', '+****************')); ?></li>
                        <li><i class="fas fa-envelope"></i> <?php echo htmlspecialchars(getSetting('contact_email', '<EMAIL>')); ?></li>
                    </ul>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; <?php echo date('Y'); ?> <?php echo htmlspecialchars(getSetting('company_name', 'Global TransLogix')); ?>. All rights reserved.</p>
                <ul class="footer-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Sitemap</a></li>
                </ul>
                <div class="developer-info">
                    <p>Developed by <a href="https://tembikelvin.github.io/" target="_blank">Tembi Kelvin</a> (defabrika)</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bottom Navigation for Mobile -->
    <?php echo generateBottomNav(); ?>

    <!-- Fixed Mobile Logo (only on mobile) -->
    <div class="mobile-logo-container">
        <div class="logo mobile-logo">
            <a href="<?php echo SITE_URL; ?>" class="site-logo">
                <div class="logo-icon">
                    <div class="logo-icon-bg"></div>
                    <i class="fas fa-map-marker-alt logo-icon-inner"></i>
                </div>
            </a>
        </div>
    </div>

    <!-- Theme Toggle and Mobile Menu Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Feather icons
            if (typeof feather !== 'undefined') {
                feather.replace();
            }

            // Theme Toggle Functionality
            const themeToggle = document.getElementById('theme-toggle');
            const mobileThemeToggle = document.getElementById('mobile-theme-toggle');
            const body = document.body;

            function toggleTheme() {
                body.classList.toggle('dark-theme');

                // Update header theme toggle icon (Font Awesome)
                if (themeToggle) {
                    const headerIcon = themeToggle.querySelector('i');
                    if (headerIcon) {
                        if (body.classList.contains('dark-theme')) {
                            headerIcon.classList.replace('fa-moon', 'fa-sun');
                        } else {
                            headerIcon.classList.replace('fa-sun', 'fa-moon');
                        }
                    }
                }

                // Update mobile theme toggle icon (Feather)
                if (mobileThemeToggle) {
                    const mobileIcon = mobileThemeToggle.querySelector('i');
                    if (mobileIcon) {
                        if (body.classList.contains('dark-theme')) {
                            mobileIcon.setAttribute('data-feather', 'sun');
                        } else {
                            mobileIcon.setAttribute('data-feather', 'moon');
                        }
                        // Re-render the specific icon
                        if (typeof feather !== 'undefined') {
                            feather.replace();
                        }
                    }
                }

                // Save theme preference
                if (body.classList.contains('dark-theme')) {
                    document.cookie = "theme=dark; path=/; max-age=31536000"; // 1 year
                } else {
                    document.cookie = "theme=light; path=/; max-age=31536000"; // 1 year
                }
            }

            // Add event listeners to both theme toggles
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleTheme);
            }

            // Mobile theme toggle with retry mechanism
            function attachMobileThemeToggle() {
                const mobileToggle = document.getElementById('mobile-theme-toggle');
                if (mobileToggle && !mobileToggle.hasAttribute('data-listener-attached')) {
                    mobileToggle.addEventListener('click', toggleTheme);
                    mobileToggle.setAttribute('data-listener-attached', 'true');
                }
            }

            // Try immediately and with delays
            attachMobileThemeToggle();
            setTimeout(attachMobileThemeToggle, 100);
            setTimeout(attachMobileThemeToggle, 500);

            // Mobile menu functionality removed - using bottom navigation instead
        });

        // Login Popup Functionality
        const loginButton = document.getElementById('login-button');
        if (loginButton) {
            // Create login popup elements if they don't exist
            if (!document.getElementById('login-popup')) {
                // Create overlay
                const loginOverlay = document.createElement('div');
                loginOverlay.id = 'login-overlay';
                loginOverlay.className = 'login-overlay';
                document.body.appendChild(loginOverlay);

                // Create popup
                const loginPopup = document.createElement('div');
                loginPopup.id = 'login-popup';
                loginPopup.className = 'login-popup';
                loginPopup.innerHTML = `
                    <div class="login-popup-content">
                        <div class="login-header">
                            <h3>Login to Your Account</h3>
                            <button id="close-login" class="close-login"><i class="fas fa-times"></i></button>
                        </div>
                        <form class="login-form" id="login-form" method="POST">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="login_username" name="login_username" placeholder="Enter your username" required>
                            </div>
                            <div class="form-group">
                                <label for="login_password">Password</label>
                                <input type="password" id="login_password" name="login_password" placeholder="Enter your password" required>
                            </div>
                            <div class="form-options">
                                <div class="remember-me">
                                    <input type="checkbox" id="remember" name="remember">
                                    <label for="remember">Remember me</label>
                                </div>
                                <a href="#" class="forgot-password">Forgot Password?</a>
                            </div>
                            <button type="submit" class="btn primary-btn login-submit">Login</button>
                        </form>
                        <div class="login-footer">
                            <p>Don't have an account? <a href="<?php echo SITE_URL; ?>/contact.php">Register Now</a></p>
                        </div>
                    </div>
                `;
                document.body.appendChild(loginPopup);

                // Get the close button and attach event
                const closeLogin = document.getElementById('close-login');

                // Open login popup
                loginButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    loginPopup.classList.add('active');
                    loginOverlay.classList.add('active');
                });

                // Close login popup
                closeLogin.addEventListener('click', function() {
                    loginPopup.classList.remove('active');
                    loginOverlay.classList.remove('active');
                });

                // Close on overlay click
                loginOverlay.addEventListener('click', function() {
                    loginPopup.classList.remove('active');
                    loginOverlay.classList.remove('active');
                });

                // Handle login form submission
                const loginForm = document.getElementById('login-form');
                loginForm.addEventListener('submit', function(e) {
                    e.preventDefault();

                    const formData = new FormData();
                    formData.append('login_username', document.getElementById('login_username').value);
                    formData.append('login_password', document.getElementById('login_password').value);
                    formData.append('remember', document.getElementById('remember').checked ? '1' : '0');

                    // Use absolute URL to ensure it works from any page including subdirectories
                    const baseUrl = window.location.protocol + '//' + window.location.host;
                    const pathParts = window.location.pathname.split('/');
                    // Find the TRACK SITE part in the path
                    let basePath = '';
                    for (let i = 0; i < pathParts.length; i++) {
                        if (pathParts[i].toUpperCase() === 'TRACK' && pathParts[i+1] && pathParts[i+1].toUpperCase() === 'SITE') {
                            basePath = '/' + pathParts.slice(0, i+2).join('/');
                            break;
                        }
                    }
                    // If we couldn't find it, use the SITE_URL from PHP
                    const loginUrl = basePath ? baseUrl + basePath + '/includes/handle-login.php' : '<?php echo SITE_URL; ?>/includes/handle-login.php';

                    console.log('Login URL:', loginUrl);

                    fetch(loginUrl, {
                        method: 'POST',
                        body: formData,
                        headers: {
                            'X-Requested-With': 'XMLHttpRequest'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            window.location.href = data.redirect;
                        } else {
                            alert(data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred during login');
                    });
                });

                // Close login popup when Escape key is pressed
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && loginPopup.classList.contains('active')) {
                        loginPopup.classList.remove('active');
                        loginOverlay.classList.remove('active');
                    }
                });
            }
        }

        // Retry mechanism for mobile login button
        function attachMobileLoginButton() {
            const mobileLoginButton = document.getElementById('login-button');
            if (mobileLoginButton && !mobileLoginButton.hasAttribute('data-mobile-listener-attached')) {
                // Only attach if it's in the mobile navigation (not desktop)
                if (mobileLoginButton.closest('.mobile-bottom-nav')) {
                    mobileLoginButton.addEventListener('click', function(e) {
                        e.preventDefault();
                        const loginPopup = document.getElementById('login-popup');
                        const loginOverlay = document.getElementById('login-overlay');
                        if (loginPopup && loginOverlay) {
                            loginPopup.classList.add('active');
                            loginOverlay.classList.add('active');
                        }
                    });
                    mobileLoginButton.setAttribute('data-mobile-listener-attached', 'true');
                }
            }
        }

        // Try to attach mobile login button with delays
        setTimeout(attachMobileLoginButton, 100);
        setTimeout(attachMobileLoginButton, 500);

        // Mobile Logo Dynamic System - Match Desktop Logo
        const mobileLogo = document.querySelector('.mobile-logo-icon-inner');
        if (mobileLogo) {
            // Get the selected logo variation from localStorage or use default
            const selectedVariation = localStorage.getItem('selectedLogo') || 'pin-track';

            // Logo variations (same as desktop)
            const logoVariations = {
                'pin-track': 'fas fa-map-marker-alt',
                'globe-route': 'fas fa-globe-americas',
                'box-track': 'fas fa-box',
                'route-path': 'fas fa-route',
                'truck-delivery': 'fas fa-truck'
            };

            // Update mobile logo icon to match desktop
            const iconClass = logoVariations[selectedVariation] || logoVariations['pin-track'];
            mobileLogo.className = iconClass + ' mobile-logo-icon-inner';
        }

        // Mobile logo now uses the same system as desktop - no additional JS needed

        // Mobile logo now uses direct HTML - no JavaScript needed
    </script>

    <?php if(isset($additionalJs)): ?>
        <?php foreach($additionalJs as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Notification System -->
    <script src="<?php echo SITE_URL; ?>/assets/js/notifications.js"></script>
    <?php displayNotifications(); ?>

    <!-- Logo Generator -->
    <script src="<?php echo SITE_URL; ?>/assets/js/logo-generator.js"></script>

    <!-- Mobile Enhancements -->
    <script src="<?php echo SITE_URL; ?>/assets/js/mobile-enhancements.js"></script>

    <!-- Mobile Navigation Effects -->
    <script src="<?php echo SITE_URL; ?>/assets/js/mobile-nav-effects.js"></script>

    <!-- Mobile Button Fixes -->
    <script src="<?php echo SITE_URL; ?>/assets/js/mobile-fixes.js"></script>

    <!-- Add CSS for login popup and developer info -->
    <style>
        /* Developer Info Styles */
        .developer-info {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            font-size: 0.85rem;
            color: rgba(255, 255, 255, 0.7);
        }

        .developer-info a {
            color: var(--primary-color);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .developer-info a:hover {
            color: var(--secondary-color);
            text-decoration: underline;
        }

        /* Login Popup Styles */
        .login-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .login-overlay.active {
            display: block;
        }

        .login-popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: var(--glass-bg);
            backdrop-filter: var(--glass-blur);
            -webkit-backdrop-filter: var(--glass-blur);
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            border-radius: 10px;
            width: 90%;
            max-width: 400px;
            z-index: 1001;
        }

        .login-popup.active {
            display: block;
        }

        .login-popup-content {
            padding: 20px;
        }

        .login-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .login-header h3 {
            margin: 0;
            color: var(--primary-color);
        }

        .close-login {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: var(--text-color);
        }

        .login-form .form-group {
            margin-bottom: 15px;
        }

        .login-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .login-form input {
            width: 100%;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background-color: rgba(255, 255, 255, 0.8);
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }

        .remember-me {
            display: flex;
            align-items: center;
        }

        .remember-me input {
            width: auto;
            margin-right: 5px;
        }

        .login-footer {
            margin-top: 20px;
            text-align: center;
            font-size: 0.9rem;
        }

        .dark-theme .login-form input {
            background-color: rgba(30, 30, 30, 0.8);
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: var(--text-color);
        }
    </style>
</body>
</html>


