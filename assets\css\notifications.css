/* Notification System Styles */

/* Container positioning */
.notification-container {
    position: fixed;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 100%;
    width: 350px;
    pointer-events: none;
}

/* Container positions */
.notification-container.top-right {
    top: 20px;
    right: 20px;
}

.notification-container.top-left {
    top: 20px;
    left: 20px;
}

.notification-container.bottom-right {
    bottom: 20px;
    right: 20px;
    flex-direction: column-reverse;
}

.notification-container.bottom-left {
    bottom: 20px;
    left: 20px;
    flex-direction: column-reverse;
}

.notification-container.top-center {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.notification-container.bottom-center {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    flex-direction: column-reverse;
}

/* Individual notification */
.notification {
    display: flex;
    align-items: flex-start;
    background-color: var(--bg-secondary, #ffffff);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 15px;
    margin-bottom: 10px;
    max-width: 100%;
    pointer-events: auto;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Notification states */
.notification.notification-show {
    opacity: 1;
    transform: translateX(0);
}

.notification.notification-hide {
    opacity: 0;
    transform: translateX(50px);
}

/* Notification types */
.notification.notification-success {
    border-left: 4px solid #28a745;
}

.notification.notification-error {
    border-left: 4px solid #dc3545;
}

.notification.notification-warning {
    border-left: 4px solid #ffc107;
}

.notification.notification-info {
    border-left: 4px solid #17a2b8;
}

/* Notification icon */
.notification-icon {
    margin-right: 15px;
    font-size: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-success .notification-icon {
    color: #28a745;
}

.notification-error .notification-icon {
    color: #dc3545;
}

.notification-warning .notification-icon {
    color: #ffc107;
}

.notification-info .notification-icon {
    color: #17a2b8;
}

/* Notification content */
.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--text-color, #333333);
}

.notification-message {
    color: var(--text-secondary, #666666);
    font-size: 0.9rem;
    line-height: 1.4;
    word-wrap: break-word;
}

/* Close button */
.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary, #666666);
    cursor: pointer;
    font-size: 16px;
    padding: 0;
    margin-left: 10px;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.notification-close:hover {
    opacity: 1;
}

/* Progress bar */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: rgba(0, 0, 0, 0.1);
}

.notification-progress-inner {
    height: 100%;
    width: 100%;
    transform-origin: left;
    transform: scaleX(0);
}

.notification-success .notification-progress-inner {
    background-color: #28a745;
}

.notification-error .notification-progress-inner {
    background-color: #dc3545;
}

.notification-warning .notification-progress-inner {
    background-color: #ffc107;
}

.notification-info .notification-progress-inner {
    background-color: #17a2b8;
}

.notification-progress-active {
    animation: progress-animation linear forwards;
}

@keyframes progress-animation {
    0% {
        transform: scaleX(1);
    }
    100% {
        transform: scaleX(0);
    }
}

/* Dark theme overrides */
.dark-theme .notification {
    background-color: #2d3748;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-theme .notification-title {
    color: #e2e8f0;
}

.dark-theme .notification-message {
    color: #cbd5e0;
}

.dark-theme .notification-close {
    color: #a0aec0;
}

.dark-theme .notification-progress {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Mobile responsiveness */
@media (max-width: 480px) {
    .notification-container {
        width: calc(100% - 40px);
        padding: 0;
    }
    
    .notification-container.top-center,
    .notification-container.bottom-center {
        width: calc(100% - 40px);
        left: 20px;
        transform: none;
    }
    
    .notification {
        width: 100%;
    }
}

/* Flash messages container (for PHP integration) */
#flash-messages {
    display: none;
}
