<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if(!isLoggedIn()) {
    // Redirect to home page where the login popup can be used
    setMessage('Please login to access the reports', 'error');
    redirect('../index.php');
}

// Get date range from query parameters or set defaults
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$reportType = isset($_GET['report_type']) ? $_GET['report_type'] : 'status';

// Get report data based on type
$reportData = [];
$chartLabels = [];
$chartData = [];
$chartColors = [];

switch($reportType) {
    case 'status':
        // Get shipment status distribution
        $reportData = getShipmentStatusReport($startDate, $endDate);

        // Prepare chart data
        foreach($reportData as $status => $count) {
            $chartLabels[] = ucfirst($status);
            $chartData[] = $count;

            // Set colors based on status
            switch($status) {
                case 'pending':
                    $chartColors[] = 'rgba(255, 193, 7, 0.8)';
                    break;
                case 'in_transit':
                    $chartColors[] = 'rgba(0, 123, 255, 0.8)';
                    break;
                case 'delivered':
                    $chartColors[] = 'rgba(40, 167, 69, 0.8)';
                    break;
                case 'delayed':
                    $chartColors[] = 'rgba(255, 152, 0, 0.8)';
                    break;
                case 'cancelled':
                    $chartColors[] = 'rgba(220, 53, 69, 0.8)';
                    break;
                default:
                    $chartColors[] = 'rgba(92, 43, 226, 0.8)';
            }
        }
        break;

    case 'monthly':
        // Get monthly shipment counts
        $reportData = getMonthlyShipmentReport($startDate, $endDate);

        // Prepare chart data
        foreach($reportData as $month => $count) {
            $chartLabels[] = $month;
            $chartData[] = $count;
            $chartColors[] = 'rgba(92, 43, 226, 0.8)';
        }
        break;

    case 'destinations':
        // Get top destinations
        $reportData = getTopDestinationsReport($startDate, $endDate);

        // Prepare chart data
        foreach($reportData as $destination => $count) {
            $chartLabels[] = $destination;
            $chartData[] = $count;
            $chartColors[] = 'rgba(92, 43, 226, 0.8)';
        }
        break;

    case 'delivery_time':
        // Get average delivery time by route
        $reportData = getDeliveryTimeReport($startDate, $endDate);

        // Prepare chart data
        foreach($reportData as $route => $avgDays) {
            $chartLabels[] = $route;
            $chartData[] = $avgDays;
            $chartColors[] = 'rgba(92, 43, 226, 0.8)';
        }
        break;
}

// Set page title
$pageTitle = 'Shipment Reports';

// Include header
include_once '../includes/header.php';
?>

<!-- Admin Dashboard Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">
            <h1>Shipment Reports</h1>
            <p>Analyze shipment data and generate reports</p>
            <div class="admin-actions">
                <a href="index.php" class="btn secondary-btn">Back to Dashboard</a>
                <a href="#" class="btn primary-btn" id="export-report">Export Report</a>
            </div>
        </div>
    </div>
</section>

<!-- Report Controls Section -->
<section class="report-controls">
    <div class="container">
        <div class="report-filters">
            <form action="reports.php" method="GET" class="filter-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="report_type">Report Type</label>
                        <select id="report_type" name="report_type" class="form-control">
                            <option value="status" <?php echo $reportType == 'status' ? 'selected' : ''; ?>>Shipment Status Distribution</option>
                            <option value="monthly" <?php echo $reportType == 'monthly' ? 'selected' : ''; ?>>Monthly Shipment Volume</option>
                            <option value="destinations" <?php echo $reportType == 'destinations' ? 'selected' : ''; ?>>Top Destinations</option>
                            <option value="delivery_time" <?php echo $reportType == 'delivery_time' ? 'selected' : ''; ?>>Average Delivery Time</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="start_date">Start Date</label>
                        <input type="date" id="start_date" name="start_date" class="form-control datepicker" value="<?php echo $startDate; ?>">
                    </div>
                    <div class="form-group">
                        <label for="end_date">End Date</label>
                        <input type="date" id="end_date" name="end_date" class="form-control datepicker" value="<?php echo $endDate; ?>">
                    </div>
                    <div class="form-group submit-group">
                        <button type="submit" class="btn primary-btn">Generate Report</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Report Visualization Section -->
<section class="report-visualization">
    <div class="container">
        <div class="report-container">
            <div class="report-header">
                <h2 class="section-title">
                    <?php
                    switch($reportType) {
                        case 'status':
                            echo 'Shipment Status Distribution';
                            break;
                        case 'monthly':
                            echo 'Monthly Shipment Volume';
                            break;
                        case 'destinations':
                            echo 'Top Destinations';
                            break;
                        case 'delivery_time':
                            echo 'Average Delivery Time (Days)';
                            break;
                    }
                    ?>
                </h2>
                <p class="report-date-range">
                    <?php echo date('F j, Y', strtotime($startDate)); ?> - <?php echo date('F j, Y', strtotime($endDate)); ?>
                </p>
            </div>

            <div class="chart-container">
                <div class="chart-wrapper">
                    <canvas id="reportChart"></canvas>
                </div>

                <div class="chart-type-selector">
                    <button class="chart-type-btn active" data-type="bar">Bar</button>
                    <button class="chart-type-btn" data-type="line">Line</button>
                    <button class="chart-type-btn" data-type="pie">Pie</button>
                </div>
            </div>

            <div class="data-summary">
                <h3>Summary</h3>
                <div class="summary-grid">
                    <?php if($reportType == 'status'): ?>
                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Total Shipments</h4>
                                <p class="summary-number"><?php echo array_sum($chartData); ?></p>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-icon delivered">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Delivery Rate</h4>
                                <p class="summary-number">
                                    <?php
                                    $deliveredIndex = array_search('Delivered', $chartLabels);
                                    $deliveryRate = ($deliveredIndex !== false && array_sum($chartData) > 0)
                                        ? round(($chartData[$deliveredIndex] / array_sum($chartData)) * 100, 1) . '%'
                                        : '0%';
                                    echo $deliveryRate;
                                    ?>
                                </p>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-icon delayed">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Delay Rate</h4>
                                <p class="summary-number">
                                    <?php
                                    $delayedIndex = array_search('Delayed', $chartLabels);
                                    $delayRate = ($delayedIndex !== false && array_sum($chartData) > 0)
                                        ? round(($chartData[$delayedIndex] / array_sum($chartData)) * 100, 1) . '%'
                                        : '0%';
                                    echo $delayRate;
                                    ?>
                                </p>
                            </div>
                        </div>
                    <?php elseif($reportType == 'monthly'): ?>
                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Total Shipments</h4>
                                <p class="summary-number"><?php echo array_sum($chartData); ?></p>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Monthly Average</h4>
                                <p class="summary-number">
                                    <?php echo count($chartData) > 0 ? round(array_sum($chartData) / count($chartData), 1) : 0; ?>
                                </p>
                            </div>
                        </div>

                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="fas fa-arrow-trend-up"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Growth Rate</h4>
                                <p class="summary-number">
                                    <?php
                                    if(count($chartData) >= 2) {
                                        $firstMonth = $chartData[0];
                                        $lastMonth = end($chartData);
                                        $growthRate = $firstMonth > 0
                                            ? round((($lastMonth - $firstMonth) / $firstMonth) * 100, 1) . '%'
                                            : 'N/A';
                                        echo $growthRate;
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </p>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="summary-card">
                            <div class="summary-icon">
                                <i class="fas fa-box"></i>
                            </div>
                            <div class="summary-info">
                                <h4>Total Records</h4>
                                <p class="summary-number"><?php echo array_sum($chartData); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Data Table Section -->
<section class="report-data-table">
    <div class="container">
        <h2 class="section-title">Detailed Data</h2>

        <div class="table-container">
            <table class="data-table mobile-card-table">
                <thead>
                    <tr>
                        <?php if($reportType == 'status'): ?>
                            <th>Status</th>
                            <th>Count</th>
                            <th>Percentage</th>
                        <?php elseif($reportType == 'monthly'): ?>
                            <th>Month</th>
                            <th>Shipment Count</th>
                        <?php elseif($reportType == 'destinations'): ?>
                            <th>Destination</th>
                            <th>Shipment Count</th>
                        <?php elseif($reportType == 'delivery_time'): ?>
                            <th>Route</th>
                            <th>Average Delivery Time (Days)</th>
                        <?php endif; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php if(empty($reportData)): ?>
                        <tr>
                            <td colspan="3" class="no-data">No data available for the selected period</td>
                        </tr>
                    <?php else: ?>
                        <?php
                        $total = array_sum($chartData);
                        foreach($reportData as $key => $value):
                        ?>
                            <tr>
                                <td><?php echo $reportType == 'status' ? ucfirst($key) : $key; ?></td>
                                <td><?php echo $value; ?></td>
                                <?php if($reportType == 'status'): ?>
                                    <td><?php echo $total > 0 ? round(($value / $total) * 100, 1) . '%' : '0%'; ?></td>
                                <?php endif; ?>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</section>

<!-- Add Chart.js with proper fallback -->
<script>
// Check if Chart.js is already loaded
if (typeof Chart === 'undefined') {
    // Define a function to load Chart.js from different sources
    function loadChartJS(source, callback) {
        const script = document.createElement('script');
        script.src = source;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load Chart.js from ' + source);
            if (source.includes('jsdelivr')) {
                console.log('Trying fallback CDN...');
                loadChartJS('https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js', callback);
            } else if (source.includes('cdnjs')) {
                console.log('Trying local fallback...');
                loadChartJS('<?php echo SITE_URL; ?>/assets/js/chart.min.js', callback);
            }
        };
        document.head.appendChild(script);
    }

    // Load a specific version of Chart.js to avoid compatibility issues
    loadChartJS('https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js', function() {
        console.log('Chart.js is loaded. Version:', Chart.version);
        // Initialize charts once loaded
        initializeReportCharts();
    });
} else {
    console.log('Chart.js is already loaded. Version:', Chart.version);
    // Initialize charts since Chart.js is already available
    setTimeout(initializeReportCharts, 100);
}

// Rename the initialization function to avoid conflicts
function initializeReportCharts() {

// Initialize charts when DOM is loaded
    // Chart data
    const chartLabels = <?php echo json_encode($chartLabels); ?>;
    const chartData = <?php echo json_encode($chartData); ?>;
    const chartColors = <?php echo json_encode($chartColors); ?>;

    // Get chart context
    const ctx = document.getElementById('reportChart').getContext('2d');

    // Create initial chart
    let reportChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartLabels,
            datasets: [{
                label: '<?php echo $pageTitle; ?>',
                data: chartData,
                backgroundColor: chartColors,
                borderColor: chartColors.map(color => color.replace('0.8', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: <?php echo $reportType == 'status' ? 'true' : 'false'; ?>,
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            label += context.parsed.y;
                            return label;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });

    // Chart type selector
    const chartTypeBtns = document.querySelectorAll('.chart-type-btn');
    chartTypeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            chartTypeBtns.forEach(b => b.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Get chart type
            const chartType = this.getAttribute('data-type');

            // Destroy current chart
            reportChart.destroy();

            // Create new chart with selected type
            reportChart = new Chart(ctx, {
                type: chartType,
                data: {
                    labels: chartLabels,
                    datasets: [{
                        label: '<?php echo $pageTitle; ?>',
                        data: chartData,
                        backgroundColor: chartColors,
                        borderColor: chartColors.map(color => color.replace('0.8', '1')),
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: chartType === 'pie',
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (chartType === 'pie') {
                                        label += ': ';
                                        label += context.parsed;
                                        label += ' (' + Math.round((context.parsed / context.dataset.data.reduce((a, b) => a + b, 0)) * 100) + '%)';
                                    } else {
                                        label = context.dataset.label || '';
                                        if (label) {
                                            label += ': ';
                                        }
                                        label += context.parsed.y;
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            display: chartType !== 'pie',
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        },
                        x: {
                            display: chartType !== 'pie'
                        }
                    }
                }
            });
        });
    });

    // Export report functionality
    document.getElementById('export-report').addEventListener('click', function(e) {
        e.preventDefault();

        // Create a temporary link
        const link = document.createElement('a');
        link.download = 'shipment-report-<?php echo date('Y-m-d'); ?>.png';

        // Convert chart to image
        link.href = document.getElementById('reportChart').toDataURL('image/png');

        // Trigger download
        link.click();
    });
}
</script>

<?php
// Add additional CSS with direct inclusion
// Debug information
echo "<!-- Debug: SITE_URL = " . SITE_URL . " -->";

// Direct CSS inclusion to bypass potential path issues
echo "<style>";
readfile(__DIR__ . '/../assets/css/admin.css');
readfile(__DIR__ . '/../assets/css/reports.css');
readfile(__DIR__ . '/../assets/css/admin-mobile.css');
readfile(__DIR__ . '/../assets/css/sidebar-fix.css'); // Add sidebar fix CSS
echo "</style>";

// Add jQuery before other scripts
echo "<script src='https://code.jquery.com/jquery-3.6.0.min.js'></script>";

// Direct JS inclusion to bypass potential path issues
echo "<script>";
readfile(__DIR__ . '/../assets/js/admin.js');
readfile(__DIR__ . '/../assets/js/admin-mobile.js');
echo "</script>";

// Add additional script to ensure sidebar close button works
echo "<script>
// Ensure sidebar close button is properly initialized
document.addEventListener('DOMContentLoaded', function() {
    // Check if sidebar close button exists, if not create it
    const dashboardSidebar = document.querySelector('.dashboard-sidebar');
    if (dashboardSidebar && !dashboardSidebar.querySelector('.sidebar-close')) {
        const closeButton = document.createElement('button');
        closeButton.className = 'sidebar-close';
        closeButton.innerHTML = '<i class=\"fas fa-times\"></i>';
        closeButton.setAttribute('aria-label', 'Close Sidebar');
        closeButton.style.cursor = 'pointer';
        dashboardSidebar.prepend(closeButton);

        // Add event listener to close button
        closeButton.addEventListener('click', function() {
            dashboardSidebar.classList.remove('active');
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.classList.remove('active');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            if (sidebarToggle) sidebarToggle.classList.remove('active');
            document.body.style.overflow = '';
        });

        console.log('Sidebar close button initialized');
    }
});
</script>";

// Keep these empty so the header/footer don't try to include them again
$additionalCss = [];
$additionalJs = [];

// Include footer
include_once '../includes/footer.php';
?>
