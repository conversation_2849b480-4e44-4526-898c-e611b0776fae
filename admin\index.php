<?php
require_once '../includes/config.php';
require_once '../includes/db.php';
require_once '../includes/functions.php';

// Check if user is logged in
if(!isLoggedIn()) {
    // Redirect to home page where the login popup can be used
    setMessage('Please login to access the dashboard', 'error');
    redirect('../index.php');
}

// Get shipment statistics - using direct PDO query to avoid any issues with the DB class
try {
    $statsQuery = $conn->query("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as `delayed`,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
                COUNT(DISTINCT origin) as origin_count,
                COUNT(DISTINCT destination) as destination_count,
                COUNT(DISTINCT customer_name) as customer_count
                FROM shipments");

    $stats = $statsQuery->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "<!-- Error: Stats query failed: " . $e->getMessage() . " -->";
    // Initialize with default values if query fails
    $stats = [
        'total' => 0,
        'pending' => 0,
        'in_transit' => 0,
        'delivered' => 0,
        'delayed' => 0,
        'cancelled' => 0,
        'origin_count' => 0,
        'destination_count' => 0,
        'customer_count' => 0
    ];
}

// Debug: Check what statuses exist in the database
$db->query("SELECT DISTINCT status FROM shipments");
$existingStatuses = $db->resultSet();

// If query failed, initialize with default values
if (!$stats) {
    $stats = [
        'total' => 0,
        'pending' => 0,
        'in_transit' => 0,
        'delivered' => 0,
        'delayed' => 0,
        'cancelled' => 0,
        'origin_count' => 0,
        'destination_count' => 0,
        'customer_count' => 0
    ];
}

// Get shipment statistics for current month - using direct PDO query
$currentMonth = date('Y-m');
try {
    $currentMonthQuery = $conn->prepare("SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'in_transit' THEN 1 ELSE 0 END) as in_transit,
                SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as delivered,
                SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) as `delayed`,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled
                FROM shipments
                WHERE DATE_FORMAT(created_at, '%Y-%m') = :current_month");
    $currentMonthQuery->bindValue(':current_month', $currentMonth);
    $currentMonthQuery->execute();
    $currentMonthStats = $currentMonthQuery->fetch(PDO::FETCH_ASSOC);

    // Debug: Print the current month stats array
    echo "<!-- Debug Current Month Stats: " . print_r($currentMonthStats, true) . " -->";
} catch (PDOException $e) {
    echo "<!-- Error: Current month stats query failed: " . $e->getMessage() . " -->";
    $currentMonthStats = false;
}

// If query failed, initialize with default values
if (!$currentMonthStats) {
    $currentMonthStats = [
        'total' => 0,
        'pending' => 0,
        'in_transit' => 0,
        'delivered' => 0,
        'delayed' => 0,
        'cancelled' => 0
    ];
}

// Calculate month-over-month growth
$lastMonth = date('Y-m', strtotime('-1 month'));
$db->query("SELECT COUNT(*) as total FROM shipments WHERE DATE_FORMAT(created_at, '%Y-%m') = :last_month");
$db->bind(':last_month', $lastMonth);
$lastMonthResult = $db->single();
$lastMonthTotal = $lastMonthResult['total'] ?? 0;

$monthlyGrowth = 0;
if ($lastMonthTotal > 0) {
    $monthlyGrowth = (($currentMonthStats['total'] - $lastMonthTotal) / $lastMonthTotal) * 100;
}

// Get recent shipments
$db->query("SELECT * FROM shipments ORDER BY created_at DESC LIMIT 5");
$recentShipments = $db->resultSet();

// If query failed, initialize with empty array
if (!$recentShipments) {
    $recentShipments = [];
}

// Get monthly shipment data for the current year
$currentYear = date('Y');
$db->query("SELECT
            MONTH(created_at) as month,
            COUNT(*) as count
            FROM shipments
            WHERE YEAR(created_at) = :year
            GROUP BY MONTH(created_at)
            ORDER BY month");
$db->bind(':year', $currentYear);
$monthlyData = $db->resultSet();

// If query failed, initialize with empty array
if (!$monthlyData) {
    $monthlyData = [];
}

// Format monthly data for chart
$monthlyShipments = array_fill(0, 12, 0); // Initialize with zeros for all months
foreach ($monthlyData as $data) {
    $monthIndex = (int)$data['month'] - 1; // Convert to 0-based index
    $monthlyShipments[$monthIndex] = (int)$data['count'];
}

// Get top destinations
$db->query("SELECT
            destination,
            COUNT(*) as count
            FROM shipments
            GROUP BY destination
            ORDER BY count DESC
            LIMIT 5");
$topDestinations = $db->resultSet();

// If query failed, initialize with empty array
if (!$topDestinations) {
    $topDestinations = [];
}

// Calculate performance metrics - using direct PDO query
try {
    $metricsQuery = $conn->query("SELECT
                (SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delivery_rate,
                (SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as `delay_rate`,
                IFNULL(AVG(CASE WHEN delivered_at IS NOT NULL THEN DATEDIFF(delivered_at, created_at) END), 0) as avg_delivery_time,
                IFNULL((SUM(CASE WHEN status = 'delivered' AND delivered_at IS NOT NULL AND DATEDIFF(delivered_at, created_at) <= 3 THEN 1 ELSE 0 END) /
                 NULLIF(SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END), 0)) * 100, 0) as on_time_rate,
                IFNULL(MAX(CASE WHEN delivered_at IS NOT NULL THEN DATEDIFF(delivered_at, created_at) END), 0) as max_delivery_time,
                IFNULL(MIN(CASE WHEN delivered_at IS NOT NULL THEN DATEDIFF(delivered_at, created_at) END), 0) as min_delivery_time
                FROM shipments");
    $performanceMetrics = $metricsQuery->fetch(PDO::FETCH_ASSOC);

    // Debug: Print the performance metrics array
    echo "<!-- Debug Performance Metrics: " . print_r($performanceMetrics, true) . " -->";
} catch (PDOException $e) {
    echo "<!-- Error: Performance metrics query failed: " . $e->getMessage() . " -->";
    $performanceMetrics = false;
}

// If query failed, initialize with default values
if (!$performanceMetrics) {
    $performanceMetrics = [
        'delivery_rate' => 0,
        'delay_rate' => 0,
        'avg_delivery_time' => 0,
        'on_time_rate' => 0,
        'max_delivery_time' => 0,
        'min_delivery_time' => 0
    ];
}

// Calculate performance metrics for current month - using direct PDO query
try {
    $currentMonthPerformanceQuery = $conn->prepare("SELECT
                (SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as delivery_rate,
                (SUM(CASE WHEN status = 'delayed' THEN 1 ELSE 0 END) / NULLIF(COUNT(*), 0)) * 100 as `delay_rate`,
                IFNULL(AVG(CASE WHEN delivered_at IS NOT NULL THEN DATEDIFF(delivered_at, created_at) END), 0) as avg_delivery_time,
                IFNULL((SUM(CASE WHEN status = 'delivered' AND delivered_at IS NOT NULL AND DATEDIFF(delivered_at, created_at) <= 3 THEN 1 ELSE 0 END) /
                 NULLIF(SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END), 0)) * 100, 0) as on_time_rate
                FROM shipments
                WHERE DATE_FORMAT(created_at, '%Y-%m') = :current_month");
    $currentMonthPerformanceQuery->bindValue(':current_month', $currentMonth);
    $currentMonthPerformanceQuery->execute();
    $currentMonthPerformance = $currentMonthPerformanceQuery->fetch(PDO::FETCH_ASSOC);

    // Debug: Print the current month performance metrics array
    echo "<!-- Debug Current Month Performance: " . print_r($currentMonthPerformance, true) . " -->";
} catch (PDOException $e) {
    echo "<!-- Error: Current month performance metrics query failed: " . $e->getMessage() . " -->";
    $currentMonthPerformance = false;
}

// If query failed, initialize with default values
if (!$currentMonthPerformance) {
    $currentMonthPerformance = [
        'delivery_rate' => 0,
        'delay_rate' => 0,
        'avg_delivery_time' => 0,
        'on_time_rate' => 0
    ];
}

// Get busiest routes - using direct PDO query
try {
    $routesQuery = $conn->query("SELECT
                CONCAT(origin, ' to ', destination) as route,
                COUNT(*) as shipment_count,
                IFNULL(AVG(CASE WHEN delivered_at IS NOT NULL THEN DATEDIFF(delivered_at, created_at) END), 0) as avg_delivery_time
                FROM shipments
                GROUP BY origin, destination
                ORDER BY shipment_count DESC
                LIMIT 3");
    $busiestRoutes = $routesQuery->fetchAll(PDO::FETCH_ASSOC);

    // Debug: Print the busiest routes array
    echo "<!-- Debug Busiest Routes: " . print_r($busiestRoutes, true) . " -->";
} catch (PDOException $e) {
    echo "<!-- Error: Busiest routes query failed: " . $e->getMessage() . " -->";
    $busiestRoutes = [];
}

// If query failed, initialize with empty array
if (!$busiestRoutes) {
    $busiestRoutes = [];
}

// Get recent activity - using direct PDO query
try {
    $activityQuery = $conn->query("SELECT
                'shipment' as type,
                id,
                tracking_number,
                status,
                created_at as timestamp,
                CASE
                    WHEN status = 'pending' THEN 'New shipment created'
                    WHEN status = 'in_transit' THEN 'Shipment in transit'
                    WHEN status = 'delivered' THEN 'Shipment delivered'
                    WHEN status = 'delayed' THEN 'Shipment delayed'
                    WHEN status = 'cancelled' THEN 'Shipment cancelled'
                    ELSE 'Shipment status updated'
                END as activity_title
                FROM shipments
                ORDER BY created_at DESC
                LIMIT 4");
    $recentActivity = $activityQuery->fetchAll(PDO::FETCH_ASSOC);

    // Debug: Print the recent activity array
    echo "<!-- Debug Recent Activity: " . print_r($recentActivity, true) . " -->";
} catch (PDOException $e) {
    echo "<!-- Error: Recent activity query failed: " . $e->getMessage() . " -->";
    $recentActivity = [];
}

// If query failed, initialize with empty array
if (!$recentActivity) {
    $recentActivity = [];
}

// Include header
include_once '../includes/header.php';
?>

<!-- Admin Dashboard Hero Section -->
<section class="hero admin-hero">
    <div class="container">
        <div class="hero-content">

            <p>Welcome back, <?php echo isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'User'; ?>!</p>
            <div class="admin-actions">
                <a href="manage-shipments.php" class="btn primary-btn"><i class="fas fa-shipping-fast"></i> Manage Shipments</a>
                <?php if(isAdmin()): ?>
                <a href="manage-users.php" class="btn secondary-btn"><i class="fas fa-users"></i> Manage Users</a>
                <a href="settings.php" class="btn purple-btn"><i class="fas fa-cogs"></i> System Settings</a>
                <a href="../maintenance.php" class="btn orange-btn"><i class="fas fa-tools"></i> Maintenance Tools</a>
                <?php endif; ?>
                <a href="logout.php" class="btn teal-btn"><i class="fas fa-sign-out-alt"></i> Logout</a>
            </div>
        </div>
    </div>
</section>

<!-- Dashboard Overview Section -->
<section class="dashboard-overview">
    <!-- Sidebar Toggle Button for Mobile -->
    <button class="sidebar-toggle" aria-label="Toggle Sidebar">
        <i class="fas fa-bars"></i>
    </button>

    <div class="container">
        <div class="dashboard-grid">
            <!-- Main Dashboard Content -->
            <div class="dashboard-main">
                <!-- Stats Overview -->
                <div class="stats-container">
                    <div class="stats-header">
                        <h2>Shipment Overview</h2>
                        <div class="header-actions">
                            <button class="refresh-stats" title="Refresh Statistics"><i class="fas fa-sync-alt"></i></button>
                            <a href="manage-shipments.php" class="view-all">Manage Shipments</a>
                        </div>
                    </div>

                    <!-- Overall Stats -->
                    <div class="stats-section">
                        <h3 class="stats-subheader">Overall Statistics</h3>
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <i class="fas fa-box"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>Total Shipments</h3>
                                    <p class="stat-number"><?php echo isset($stats['total']) ? $stats['total'] : 0; ?></p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon pending">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>Pending</h3>
                                    <p class="stat-number"><?php echo isset($stats['pending']) ? $stats['pending'] : 0; ?></p>
                                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['pending'])) ? round(($stats['pending'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon transit">
                                    <i class="fas fa-shipping-fast"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>In Transit</h3>
                                    <p class="stat-number"><?php echo isset($stats['in_transit']) ? $stats['in_transit'] : 0; ?></p>
                                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['in_transit'])) ? round(($stats['in_transit'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon delivered">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>Delivered</h3>
                                    <p class="stat-number"><?php echo isset($stats['delivered']) ? $stats['delivered'] : 0; ?></p>
                                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['delivered'])) ? round(($stats['delivered'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon delayed">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>Delayed</h3>
                                    <p class="stat-number"><?php echo isset($stats['delayed']) ? $stats['delayed'] : 0; ?></p>
                                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['delayed'])) ? round(($stats['delayed'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon cancelled">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="stat-info">
                                    <h3>Cancelled</h3>
                                    <p class="stat-number"><?php echo isset($stats['cancelled']) ? $stats['cancelled'] : 0; ?></p>
                                    <p class="stat-percentage"><?php echo (isset($stats['total']) && $stats['total'] > 0 && isset($stats['cancelled'])) ? round(($stats['cancelled'] / $stats['total']) * 100, 1) : 0; ?>%</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Month Stats -->
                    <div class="stats-section">
                        <h3 class="stats-subheader">Current Month (<?php echo date('F Y'); ?>)</h3>
                        <div class="stats-summary">
                            <div class="summary-row">
                                <div class="summary-label">Total Shipments</div>
                                <div class="summary-value"><?php echo $currentMonthStats['total'] ?? 0; ?></div>
                            </div>
                            <div class="summary-row">
                                <div class="summary-label">Month-over-Month Growth</div>
                                <div class="summary-value <?php echo $monthlyGrowth >= 0 ? 'positive' : 'negative'; ?>">
                                    <?php echo round($monthlyGrowth, 1); ?>%
                                    <i class="fas <?php echo $monthlyGrowth >= 0 ? 'fa-arrow-up' : 'fa-arrow-down'; ?>"></i>
                                </div>
                            </div>
                            <div class="summary-row">
                                <div class="summary-label">Delivered</div>
                                <div class="summary-value"><?php echo $currentMonthStats['delivered'] ?? 0; ?> (<?php echo (isset($currentMonthStats['total']) && $currentMonthStats['total'] > 0 && isset($currentMonthStats['delivered'])) ? round(($currentMonthStats['delivered'] / $currentMonthStats['total']) * 100, 1) : 0; ?>%)</div>
                            </div>
                            <div class="summary-row">
                                <div class="summary-label">In Transit</div>
                                <div class="summary-value"><?php echo $currentMonthStats['in_transit'] ?? 0; ?> (<?php echo (isset($currentMonthStats['total']) && $currentMonthStats['total'] > 0 && isset($currentMonthStats['in_transit'])) ? round(($currentMonthStats['in_transit'] / $currentMonthStats['total']) * 100, 1) : 0; ?>%)</div>
                            </div>
                            <div class="summary-row">
                                <div class="summary-label">Delayed</div>
                                <div class="summary-value"><?php echo $currentMonthStats['delayed'] ?? 0; ?> (<?php echo (isset($currentMonthStats['total']) && $currentMonthStats['total'] > 0 && isset($currentMonthStats['delayed'])) ? round(($currentMonthStats['delayed'] / $currentMonthStats['total']) * 100, 1) : 0; ?>%)</div>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Stats -->
                    <div class="stats-section">
                        <h3 class="stats-subheader">Network Coverage</h3>
                        <div class="stats-summary">
                            <div class="summary-row">
                                <div class="summary-label">Origin Locations</div>
                                <div class="summary-value"><?php echo $stats['origin_count'] ?? 0; ?></div>
                            </div>
                            <div class="summary-row">
                                <div class="summary-label">Destination Locations</div>
                                <div class="summary-value"><?php echo $stats['destination_count'] ?? 0; ?></div>
                            </div>
                            <div class="summary-row">
                                <div class="summary-label">Total Customers</div>
                                <div class="summary-value"><?php echo $stats['customer_count'] ?? 0; ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shipment Status Chart -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h2>Shipment Status Distribution</h2>
                        <div class="header-actions">
                            <button class="refresh-stats" title="Refresh Chart"><i class="fas fa-sync-alt"></i></button>
                        </div>
                    </div>

                    <!-- Debug Info -->
                    <div class="debug-info" style="margin-bottom: 15px; font-size: 0.8rem; color: #666;">
                        <p><strong>Debug:</strong> Stats data:
                            Total: <?php echo $stats['total'] ?? 0; ?>,
                            Pending: <?php echo $stats['pending'] ?? 0; ?>,
                            In Transit: <?php echo $stats['in_transit'] ?? 0; ?>,
                            Delivered: <?php echo $stats['delivered'] ?? 0; ?>,
                            Delayed: <?php echo $stats['delayed'] ?? 0; ?>,
                            Cancelled: <?php echo $stats['cancelled'] ?? 0; ?>
                        </p>
                        <p><strong>Existing Statuses:</strong>
                            <?php foreach($existingStatuses as $status): ?>
                                <?php echo $status['status']; ?>,
                            <?php endforeach; ?>
                        </p>
                    </div>

                    <div class="chart-wrapper">
                        <canvas id="statusChart"></canvas>
                    </div>
                </div>

                <!-- Monthly Shipments Chart -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h2>Monthly Shipment Volume</h2>
                        <div class="header-actions">
                            <button class="refresh-stats" title="Refresh Chart"><i class="fas fa-sync-alt"></i></button>
                            <a href="manage-shipments.php" class="view-all">View All Shipments</a>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="monthlyChart"></canvas>
                    </div>
                </div>

                <!-- Top Destinations Chart -->
                <div class="chart-container">
                    <div class="chart-header">
                        <h2>Top Destinations</h2>
                        <div class="header-actions">
                            <button class="refresh-stats" title="Refresh Chart"><i class="fas fa-sync-alt"></i></button>
                            <a href="manage-shipments.php" class="view-all">View All Shipments</a>
                        </div>
                    </div>
                    <div class="chart-wrapper">
                        <canvas id="destinationsChart"></canvas>
                    </div>
                </div>

                <!-- Recent Shipments Section -->
                <div class="recent-shipments">
                    <div class="section-header">
                        <h2 class="section-title">Recent Shipments</h2>
                        <a href="manage-shipments.php" class="view-all">View All</a>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Tracking #</th>
                                    <th>Customer</th>
                                    <th>Origin</th>
                                    <th>Destination</th>
                                    <th>Status</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if(empty($recentShipments)): ?>
                                    <tr>
                                        <td colspan="7" class="no-data">No shipments found</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach($recentShipments as $shipment): ?>
                                        <tr>
                                            <td><?php echo $shipment['tracking_number']; ?></td>
                                            <td><?php echo $shipment['customer_name']; ?></td>
                                            <td><?php echo $shipment['origin']; ?></td>
                                            <td><?php echo $shipment['destination']; ?></td>
                                            <td><span class="status-badge <?php echo getStatusClass($shipment['status']); ?>"><?php echo ucfirst($shipment['status']); ?></span></td>
                                            <td><?php echo formatDate($shipment['created_at']); ?></td>
                                            <td class="actions">
                                                <a href="manage-shipments.php?action=view&id=<?php echo $shipment['id']; ?>" class="action-btn view-btn" title="View Details"><i class="fas fa-eye"></i></a>
                                                <a href="manage-shipments.php?action=edit&id=<?php echo $shipment['id']; ?>" class="action-btn edit-btn" title="Edit Shipment"><i class="fas fa-edit"></i></a>
                                                <a href="manage-shipments.php?action=add_update&id=<?php echo $shipment['id']; ?>" class="action-btn update-btn" title="Add Update"><i class="fas fa-plus-circle"></i></a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Dashboard Sidebar -->
            <div class="dashboard-sidebar">
                <!-- Quick Actions Section -->
                <div class="quick-actions">
                    <h2 class="section-title">Quick Actions</h2>

                    <div class="actions-grid">
                        <a href="add-shipment.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-plus-circle"></i>
                            </div>
                            <div class="action-info">
                                <h3>Add New Shipment</h3>
                                <p>Create a new shipment record</p>
                            </div>
                        </a>

                        <a href="add-user.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="action-info">
                                <h3>Add New User</h3>
                                <p>Create a new user account</p>
                            </div>
                        </a>

                        <a href="reports.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="action-info">
                                <h3>Generate Reports</h3>
                                <p>Create shipment reports</p>
                            </div>
                        </a>

                        <a href="settings.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="action-info">
                                <h3>System Settings</h3>
                                <p>Configure system settings</p>
                            </div>
                        </a>

                        <a href="geocoding-settings.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-map-marked-alt"></i>
                            </div>
                            <div class="action-info">
                                <h3>Geocoding Settings</h3>
                                <p>Configure map and location settings</p>
                            </div>
                        </a>

                        <a href="tracking-settings.php" class="action-card">
                            <div class="action-icon">
                                <i class="fas fa-globe-americas"></i>
                            </div>
                            <div class="action-info">
                                <h3>Tracking Settings</h3>
                                <p>Configure advanced tracking options</p>
                            </div>
                        </a>

                        <?php if (isAdmin()): ?>
                        <a href="../maintenance.php" class="action-card maintenance-card">
                            <div class="action-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="action-info">
                                <h3>Maintenance Dashboard</h3>
                                <p>Access all maintenance and debug tools</p>
                            </div>
                        </a>

                        <a href="../manage_tools.php" class="action-card maintenance-card">
                            <div class="action-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="action-info">
                                <h3>Manage Tools</h3>
                                <p>Enable, disable, or delete maintenance tools</p>
                            </div>
                        </a>

                        <?php
                        // Get current maintenance mode status
                        $maintenanceMode = getSetting('maintenance_mode', '0');
                        $maintenanceActive = $maintenanceMode === '1';
                        ?>
                        <a href="toggle-maintenance.php" class="action-card <?php echo $maintenanceActive ? 'danger-card' : ''; ?>">
                            <div class="action-icon">
                                <i class="fas <?php echo $maintenanceActive ? 'fa-toggle-on' : 'fa-toggle-off'; ?>"></i>
                            </div>
                            <div class="action-info">
                                <h3>Maintenance Mode</h3>
                                <p><?php echo $maintenanceActive ? 'Currently ON - Click to disable' : 'Currently OFF - Click to enable'; ?></p>
                            </div>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="performance-metrics">
                    <div class="metrics-header">
                        <h2 class="section-title">Performance Metrics</h2>
                        <div class="header-actions">
                            <button class="refresh-stats" title="Refresh Metrics"><i class="fas fa-sync-alt"></i></button>
                        </div>
                    </div>

                    <!-- Overall Performance -->
                    <div class="metrics-section">
                        <h3 class="metrics-subheader">Overall Performance</h3>

                        <div class="metric-item">
                            <div class="metric-label">
                                <div class="metric-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="metric-name">Delivery Success Rate</div>
                            </div>
                            <?php
                            $deliveryRate = round($performanceMetrics['delivery_rate'] ?? 0, 1);
                            ?>
                            <div class="metric-value"><?php echo $deliveryRate; ?>%</div>
                            <div class="progress-container">
                                <div class="progress-bar" style="width: <?php echo $deliveryRate; ?>%"></div>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-label">
                                <div class="metric-icon">
                                    <i class="fas fa-truck"></i>
                                </div>
                                <div class="metric-name">On-Time Delivery Rate</div>
                            </div>
                            <?php
                            $onTimeRate = round($performanceMetrics['on_time_rate'] ?? 0, 1);
                            ?>
                            <div class="metric-value"><?php echo $onTimeRate; ?>%</div>
                            <div class="progress-container">
                                <div class="progress-bar" style="width: <?php echo $onTimeRate; ?>%"></div>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-label">
                                <div class="metric-icon">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="metric-name">Delay Rate</div>
                            </div>
                            <?php
                            $delayRate = round($performanceMetrics['delay_rate'] ?? 0, 1);
                            ?>
                            <div class="metric-value"><?php echo $delayRate; ?>%</div>
                            <div class="progress-container">
                                <div class="progress-bar" style="width: <?php echo $delayRate; ?>%"></div>
                            </div>
                        </div>

                        <div class="metric-item">
                            <div class="metric-label">
                                <div class="metric-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <div class="metric-name">Avg. Delivery Time</div>
                            </div>
                            <?php
                            $avgDeliveryTime = round($performanceMetrics['avg_delivery_time'] ?? 0, 1);
                            ?>
                            <div class="metric-value"><?php echo $avgDeliveryTime; ?> days</div>
                        </div>
                    </div>

                    <!-- Current Month Performance -->
                    <div class="metrics-section">
                        <h3 class="metrics-subheader">Current Month Performance</h3>

                        <div class="metrics-summary">
                            <div class="summary-row">
                                <div class="summary-label">Delivery Success Rate</div>
                                <div class="summary-value">
                                    <?php echo round($currentMonthPerformance['delivery_rate'] ?? 0, 1); ?>%
                                    <?php
                                    $rateChange = ($currentMonthPerformance['delivery_rate'] ?? 0) - ($performanceMetrics['delivery_rate'] ?? 0);
                                    if (abs($rateChange) >= 0.1):
                                    ?>
                                    <span class="change-indicator <?php echo $rateChange >= 0 ? 'positive' : 'negative'; ?>">
                                        <?php echo round(abs($rateChange), 1); ?>% <?php echo $rateChange >= 0 ? 'better' : 'worse'; ?> than average
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="summary-row">
                                <div class="summary-label">On-Time Delivery Rate</div>
                                <div class="summary-value">
                                    <?php echo round($currentMonthPerformance['on_time_rate'] ?? 0, 1); ?>%
                                    <?php
                                    $rateChange = ($currentMonthPerformance['on_time_rate'] ?? 0) - ($performanceMetrics['on_time_rate'] ?? 0);
                                    if (abs($rateChange) >= 0.1):
                                    ?>
                                    <span class="change-indicator <?php echo $rateChange >= 0 ? 'positive' : 'negative'; ?>">
                                        <?php echo round(abs($rateChange), 1); ?>% <?php echo $rateChange >= 0 ? 'better' : 'worse'; ?> than average
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="summary-row">
                                <div class="summary-label">Delay Rate</div>
                                <div class="summary-value">
                                    <?php echo round($currentMonthPerformance['delay_rate'] ?? 0, 1); ?>%
                                    <?php
                                    $rateChange = ($performanceMetrics['delay_rate'] ?? 0) - ($currentMonthPerformance['delay_rate'] ?? 0);
                                    if (abs($rateChange) >= 0.1):
                                    ?>
                                    <span class="change-indicator <?php echo $rateChange >= 0 ? 'positive' : 'negative'; ?>">
                                        <?php echo round(abs($rateChange), 1); ?>% <?php echo $rateChange >= 0 ? 'better' : 'worse'; ?> than average
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="summary-row">
                                <div class="summary-label">Avg. Delivery Time</div>
                                <div class="summary-value">
                                    <?php echo round($currentMonthPerformance['avg_delivery_time'] ?? 0, 1); ?> days
                                    <?php
                                    $timeChange = ($performanceMetrics['avg_delivery_time'] ?? 0) - ($currentMonthPerformance['avg_delivery_time'] ?? 0);
                                    if (abs($timeChange) >= 0.1):
                                    ?>
                                    <span class="change-indicator <?php echo $timeChange >= 0 ? 'positive' : 'negative'; ?>">
                                        <?php echo round(abs($timeChange), 1); ?> days <?php echo $timeChange >= 0 ? 'faster' : 'slower'; ?> than average
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Busiest Routes -->
                    <div class="metrics-section">
                        <h3 class="metrics-subheader">Busiest Routes</h3>

                        <div class="routes-list">
                            <?php if(empty($busiestRoutes)): ?>
                                <div class="no-routes">No route data available</div>
                            <?php else: ?>
                                <?php foreach($busiestRoutes as $index => $route): ?>
                                    <div class="route-item">
                                        <div class="route-rank">#<?php echo $index + 1; ?></div>
                                        <div class="route-details">
                                            <div class="route-name"><?php echo $route['route']; ?></div>
                                            <div class="route-stats">
                                                <span class="route-count"><?php echo $route['shipment_count']; ?> shipments</span>
                                                <span class="route-time"><?php echo round($route['avg_delivery_time'], 1); ?> days avg. delivery time</span>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Activity Feed -->
                <div class="activity-feed">
                    <h2 class="section-title">Recent Activity</h2>

                    <div class="activity-list">
                        <?php if(empty($recentActivity)): ?>
                            <div class="no-activity">No recent activity found</div>
                        <?php else: ?>
                            <?php foreach($recentActivity as $activity): ?>
                                <div class="activity-item">
                                    <div class="activity-icon">
                                        <?php
                                        $icon = 'fas fa-box';
                                        $activityTitle = $activity['activity_title'] ?? '';

                                        if (!empty($activityTitle)) {
                                            if (strpos($activityTitle, 'created') !== false) {
                                                $icon = 'fas fa-plus';
                                            } elseif (strpos($activityTitle, 'transit') !== false) {
                                                $icon = 'fas fa-shipping-fast';
                                            } elseif (strpos($activityTitle, 'delivered') !== false) {
                                                $icon = 'fas fa-check';
                                            } elseif (strpos($activityTitle, 'delayed') !== false) {
                                                $icon = 'fas fa-exclamation-triangle';
                                            } elseif (strpos($activityTitle, 'cancelled') !== false) {
                                                $icon = 'fas fa-times';
                                            }
                                        }
                                        ?>
                                        <i class="<?php echo $icon; ?>"></i>
                                    </div>
                                    <div class="activity-content">
                                        <div class="activity-title">
                                            <?php
                                            if (empty($activityTitle)) {
                                                // Generate a title based on status if activity_title is empty
                                                if (!empty($activity['status'])) {
                                                    echo 'Shipment status: ' . ucfirst(str_replace('_', ' ', $activity['status']));
                                                } else {
                                                    echo 'Shipment updated';
                                                }
                                            } else {
                                                echo $activityTitle;
                                            }
                                            ?>
                                            <?php if(!empty($activity['tracking_number'])): ?>
                                                #<?php echo $activity['tracking_number']; ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="activity-time"><?php echo timeAgo($activity['timestamp'] ?? ''); ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Add Chart.js with proper fallback -->
<script>
// Check if Chart.js is already loaded
if (typeof Chart === 'undefined') {
    // Define a function to load Chart.js from different sources
    function loadChartJS(source, callback) {
        const script = document.createElement('script');
        script.src = source;
        script.onload = callback;
        script.onerror = function() {
            console.error('Failed to load Chart.js from ' + source);
            if (source.includes('jsdelivr')) {
                console.log('Trying fallback CDN...');
                loadChartJS('https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js', callback);
            } else if (source.includes('cdnjs')) {
                console.log('Trying local fallback...');
                loadChartJS('<?php echo SITE_URL; ?>/assets/js/chart.min.js', callback);
            }
        };
        document.head.appendChild(script);
    }

    // Load a specific version of Chart.js to avoid compatibility issues
    loadChartJS('https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js', function() {
        console.log('Chart.js is loaded. Version:', Chart.version);
        // Initialize charts once loaded
        initializeCharts();
    });
} else {
    console.log('Chart.js is already loaded. Version:', Chart.version);
    // Initialize charts since Chart.js is already available
    setTimeout(initializeCharts, 100);
}
</script>

<!-- Initialize Charts -->
<script>
// Function to initialize all charts
function initializeCharts() {
    if (typeof Chart === 'undefined') {
        console.error('Chart.js is still not loaded after all fallbacks. Charts will not be displayed.');
        return;
    }
    // Status Chart
    const statusCtx = document.getElementById('statusChart');

    // Check if the canvas element exists
    if (statusCtx) {
        const statusCtx2d = statusCtx.getContext('2d');
        const statusData = {
            labels: ['Pending', 'In Transit', 'Delivered', 'Delayed', 'Cancelled'],
            datasets: [{
                label: 'Shipment Status',
                data: [
                    <?php echo $stats['pending'] ?? 0; ?>,
                    <?php echo $stats['in_transit'] ?? 0; ?>,
                    <?php echo $stats['delivered'] ?? 0; ?>,
                    <?php echo $stats['delayed'] ?? 0; ?>,
                    <?php echo $stats['cancelled'] ?? 0; ?>
                ],
                backgroundColor: [
                    'rgba(255, 193, 7, 0.8)',
                    'rgba(0, 123, 255, 0.8)',
                    'rgba(40, 167, 69, 0.8)',
                    'rgba(255, 152, 0, 0.8)',
                    'rgba(220, 53, 69, 0.8)'
                ],
                borderColor: [
                    'rgba(255, 193, 7, 1)',
                    'rgba(0, 123, 255, 1)',
                    'rgba(40, 167, 69, 1)',
                    'rgba(255, 152, 0, 1)',
                    'rgba(220, 53, 69, 1)'
                ],
                borderWidth: 1
            }]
        };

        // Add console logging for debugging
        console.log('Status Chart Data:', statusData);

        try {
            new Chart(statusCtx2d, {
                type: 'doughnut',
                data: statusData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        }
                    }
                }
            });
            console.log('Status chart created successfully');
        } catch (error) {
            console.error('Error creating status chart:', error);
        }
    } else {
        console.error('Status chart canvas element not found');
    }

    // Monthly Chart
    const monthlyCtx = document.getElementById('monthlyChart');

    // Check if the canvas element exists
    if (monthlyCtx) {
        const monthlyCtx2d = monthlyCtx.getContext('2d');
        const monthlyData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [{
                label: 'Shipments in <?php echo $currentYear; ?>',
                data: <?php echo json_encode($monthlyShipments); ?>,
                backgroundColor: 'rgba(92, 43, 226, 0.2)',
                borderColor: 'rgba(92, 43, 226, 1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }]
        };

        // Add console logging for debugging
        console.log('Monthly Chart Data:', monthlyData);

        try {
            new Chart(monthlyCtx2d, {
                type: 'line',
                data: monthlyData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
            console.log('Monthly chart created successfully');
        } catch (error) {
            console.error('Error creating monthly chart:', error);
        }
    } else {
        console.error('Monthly chart canvas element not found');
    }

    // Top Destinations Chart
    const destinationsCtx = document.getElementById('destinationsChart');

    // Check if the canvas element exists
    if (destinationsCtx) {
        const destinationsCtx2d = destinationsCtx.getContext('2d');

        <?php
        $destinations = [];
        $counts = [];
        $colors = [];
        foreach($topDestinations as $index => $destination) {
            $destinations[] = "'" . addslashes($destination['destination']) . "'";
            $counts[] = $destination['count'];
            // Generate a color based on index
            $hue = ($index * 50) % 360;
            $colors[] = "'rgba(" . rand(50, 200) . ", " . rand(50, 200) . ", " . rand(50, 200) . ", 0.8)'";
        }
        ?>

        const destinationsData = {
            labels: [<?php echo !empty($destinations) ? implode(', ', $destinations) : ''; ?>],
            datasets: [{
                label: 'Shipment Count',
                data: [<?php echo !empty($counts) ? implode(', ', $counts) : ''; ?>],
                backgroundColor: [<?php echo !empty($colors) ? implode(', ', $colors) : ''; ?>],
                borderColor: [<?php echo !empty($colors) ? implode(', ', $colors) : ''; ?>],
                borderWidth: 1
            }]
        };

        // Add console logging for debugging
        console.log('Destinations Chart Data:', destinationsData);

        try {
            new Chart(destinationsCtx2d, {
                type: 'bar',
                data: destinationsData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    scales: {
                        x: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    }
                }
            });
            console.log('Destinations chart created successfully');
        } catch (error) {
            console.error('Error creating destinations chart:', error);
        }
    } else {
        console.error('Destinations chart canvas element not found');
    }
}
// Close the initializeCharts function
</script>

<?php
// Add additional CSS with direct inclusion
// Debug information
echo "<!-- Debug: SITE_URL = " . SITE_URL . " -->";

// Direct CSS inclusion to bypass potential path issues
echo "<style>";
readfile(__DIR__ . '/../assets/css/admin.css');
readfile(__DIR__ . '/../assets/css/admin-mobile.css');
readfile(__DIR__ . '/../assets/css/sidebar-fix.css'); // Add sidebar fix CSS
readfile(__DIR__ . '/../assets/css/admin-hero-buttons.css'); // Add enhanced hero buttons CSS
echo "</style>";

// Add jQuery before other scripts
echo "<script src='https://code.jquery.com/jquery-3.6.0.min.js'></script>";

// Direct JS inclusion to bypass potential path issues
echo "<script>";
readfile(__DIR__ . '/../assets/js/admin.js');
readfile(__DIR__ . '/../assets/js/admin-mobile.js');
readfile(__DIR__ . '/../assets/js/admin-button-effects.js'); // Add button effects JS
echo "</script>";

// Add additional script to ensure sidebar close button works
echo "<script>
// Ensure sidebar close button is properly initialized
document.addEventListener('DOMContentLoaded', function() {
    // Check if sidebar close button exists, if not create it
    const dashboardSidebar = document.querySelector('.dashboard-sidebar');
    if (dashboardSidebar && !dashboardSidebar.querySelector('.sidebar-close')) {
        const closeButton = document.createElement('button');
        closeButton.className = 'sidebar-close';
        closeButton.innerHTML = '<i class=\"fas fa-times\"></i>';
        closeButton.setAttribute('aria-label', 'Close Sidebar');
        closeButton.style.cursor = 'pointer';
        dashboardSidebar.prepend(closeButton);

        // Add event listener to close button
        closeButton.addEventListener('click', function() {
            dashboardSidebar.classList.remove('active');
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.classList.remove('active');
            const sidebarToggle = document.querySelector('.sidebar-toggle');
            if (sidebarToggle) sidebarToggle.classList.remove('active');
            document.body.style.overflow = '';
        });

        console.log('Sidebar close button initialized');
    }
});
</script>";

// Keep these empty so the header/footer don't try to include them again
$additionalCss = [];
$additionalJs = ['../assets/js/mobile-admin.js'];

// Include footer
include_once '../includes/footer.php';
?>

