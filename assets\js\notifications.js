/**
 * Notification System
 * Displays success, error, warning, and info notifications as popups
 */

// Use an IIFE to prevent global variable conflicts
(function() {
    // Check if the notification system is already initialized
    if (window.notificationSystemInitialized) {
        return;
    }

    // Mark as initialized
    window.notificationSystemInitialized = true;

    // Notification configuration
    const notificationConfig = {
        duration: 5000,         // Default duration in milliseconds
        position: 'top-right',  // Default position
        maxNotifications: 3,    // Maximum number of notifications shown at once
        animations: true,       // Enable animations
        sounds: false,          // Disable sounds by default
        autoClose: true,        // Auto close notifications
        closeOnClick: true,     // Close when clicked
        showProgressBar: true,  // Show progress bar for auto-close
        pauseOnHover: true      // Pause auto-close on hover
    };

    // Notification counter for unique IDs
    let notificationCounter = 0;

// Active notifications tracking
const activeNotifications = [];

/**
 * Create and show a notification
 * @param {string} message - The notification message
 * @param {string} type - The notification type (success, error, warning, info)
 * @param {object} options - Optional configuration overrides
 */
window.showNotification = function(message, type = 'info', options = {}) {
    // Merge default config with options
    const config = { ...notificationConfig, ...options };

    // Create notification ID
    const notificationId = 'notification-' + (++notificationCounter);

    // Create notification container if it doesn't exist
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'notification-container ' + config.position;
        document.body.appendChild(container);
    }

    // Limit number of notifications
    if (activeNotifications.length >= config.maxNotifications) {
        const oldestNotification = document.getElementById(activeNotifications.shift());
        if (oldestNotification) {
            oldestNotification.remove();
        }
    }

    // Create notification element
    const notification = document.createElement('div');
    notification.id = notificationId;
    notification.className = 'notification notification-' + type;
    if (config.animations) {
        notification.classList.add('notification-animated');
    }

    // Create icon based on type
    const icon = document.createElement('div');
    icon.className = 'notification-icon';

    switch (type) {
        case 'success':
            icon.innerHTML = '<i class="fas fa-check-circle"></i>';
            break;
        case 'error':
            icon.innerHTML = '<i class="fas fa-times-circle"></i>';
            break;
        case 'warning':
            icon.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
            break;
        case 'info':
        default:
            icon.innerHTML = '<i class="fas fa-info-circle"></i>';
            break;
    }

    // Create content
    const content = document.createElement('div');
    content.className = 'notification-content';

    // Add title based on type
    const title = document.createElement('div');
    title.className = 'notification-title';

    switch (type) {
        case 'success':
            title.textContent = 'Success';
            break;
        case 'error':
            title.textContent = 'Error';
            break;
        case 'warning':
            title.textContent = 'Warning';
            break;
        case 'info':
        default:
            title.textContent = 'Information';
            break;
    }

    // Add message
    const messageElement = document.createElement('div');
    messageElement.className = 'notification-message';
    messageElement.textContent = message;

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.className = 'notification-close';
    closeButton.innerHTML = '<i class="fas fa-times"></i>';
    closeButton.setAttribute('aria-label', 'Close notification');
    closeButton.onclick = function() {
        closeNotification(notificationId);
    };

    // Add progress bar if enabled
    let progressBar = null;
    if (config.showProgressBar && config.autoClose) {
        progressBar = document.createElement('div');
        progressBar.className = 'notification-progress';

        const progressInner = document.createElement('div');
        progressInner.className = 'notification-progress-inner';
        progressBar.appendChild(progressInner);
    }

    // Assemble notification
    content.appendChild(title);
    content.appendChild(messageElement);
    notification.appendChild(icon);
    notification.appendChild(content);
    notification.appendChild(closeButton);
    if (progressBar) {
        notification.appendChild(progressBar);
    }

    // Add to container
    container.appendChild(notification);

    // Track active notification
    activeNotifications.push(notificationId);

    // Play sound if enabled
    if (config.sounds) {
        const sound = new Audio();
        sound.src = `/assets/sounds/notification-${type}.mp3`;
        sound.volume = 0.5;
        sound.play().catch(e => console.log('Sound could not be played', e));
    }

    // Set up auto close
    let autoCloseTimeout = null;
    let progressAnimation = null;

    if (config.autoClose) {
        autoCloseTimeout = setTimeout(() => {
            closeNotification(notificationId);
        }, config.duration);

        if (progressBar) {
            const progressInner = progressBar.querySelector('.notification-progress-inner');
            progressInner.style.animationDuration = (config.duration / 1000) + 's';
            progressInner.classList.add('notification-progress-active');
            progressAnimation = progressInner.getAnimations()[0];
        }
    }

    // Set up pause on hover
    if (config.pauseOnHover) {
        notification.addEventListener('mouseenter', () => {
            if (autoCloseTimeout) {
                clearTimeout(autoCloseTimeout);
            }
            if (progressAnimation) {
                progressAnimation.pause();
            }
        });

        notification.addEventListener('mouseleave', () => {
            if (config.autoClose) {
                autoCloseTimeout = setTimeout(() => {
                    closeNotification(notificationId);
                }, config.duration);

                if (progressAnimation) {
                    progressAnimation.play();
                }
            }
        });
    }

    // Set up close on click
    if (config.closeOnClick) {
        notification.addEventListener('click', (e) => {
            if (!e.target.closest('.notification-close')) {
                closeNotification(notificationId);
            }
        });
    }

    // Add entrance animation
    setTimeout(() => {
        notification.classList.add('notification-show');
    }, 10);

    // Return notification ID for potential manual closing
    return notificationId;
}

/**
 * Close a notification by ID
 * @param {string} id - The notification ID
 */
function closeNotification(id) {
    const notification = document.getElementById(id);
    if (!notification) return;

    // Add exit animation
    notification.classList.remove('notification-show');
    notification.classList.add('notification-hide');

    // Remove from active notifications
    const index = activeNotifications.indexOf(id);
    if (index > -1) {
        activeNotifications.splice(index, 1);
    }

    // Remove element after animation
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }

        // Remove container if empty
        const container = document.getElementById('notification-container');
        if (container && container.children.length === 0) {
            container.remove();
        }
    }, 300);
}

/**
 * Show a success notification
 * @param {string} message - The notification message
 * @param {object} options - Optional configuration overrides
 */
window.showSuccess = function(message, options = {}) {
    return showNotification(message, 'success', options);
};

/**
 * Show an error notification
 * @param {string} message - The notification message
 * @param {object} options - Optional configuration overrides
 */
window.showError = function(message, options = {}) {
    return showNotification(message, 'error', options);
};

/**
 * Show a warning notification
 * @param {string} message - The notification message
 * @param {object} options - Optional configuration overrides
 */
window.showWarning = function(message, options = {}) {
    return showNotification(message, 'warning', options);
};

/**
 * Show an info notification
 * @param {string} message - The notification message
 * @param {object} options - Optional configuration overrides
 */
window.showInfo = function(message, options = {}) {
    return showNotification(message, 'info', options);
};

/**
 * Close all active notifications
 */
window.closeAllNotifications = function() {
    const activeIds = [...activeNotifications];
    activeIds.forEach(id => closeNotification(id));
};

/**
 * Set error notification - used by PHP for error messages
 * @param {string} message - Error message
 * @param {number} duration - Duration to show notification
 */
window.setErrorNotification = function(message, duration = 5000) {
    return showNotification(message, 'error', { duration: duration });
};

/**
 * Check for server-side notifications on page load
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check for notifications in the data attribute
    const notificationsData = document.body.getAttribute('data-notifications');
    if (notificationsData) {
        try {
            const notifications = JSON.parse(notificationsData);
            notifications.forEach(notification => {
                showNotification(notification.message, notification.type, { duration: notification.duration || 5000 });
            });
            // Clear the attribute after processing
            document.body.removeAttribute('data-notifications');
        } catch (e) {
            console.error('Error parsing notifications:', e);
        }
    }

    // Check for flash messages from PHP
    const flashContainer = document.getElementById('flash-messages');
    if (flashContainer) {
        const messages = flashContainer.querySelectorAll('.flash-message');
        messages.forEach(message => {
            const type = message.getAttribute('data-type') || 'info';
            const text = message.textContent.trim();
            if (text) {
                showNotification(text, type);
            }
        });
        // Remove the container after processing
        flashContainer.remove();
    }
});

// Close the IIFE
})();
