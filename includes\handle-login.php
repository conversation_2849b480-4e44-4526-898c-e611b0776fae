<?php
require_once 'config.php';
require_once 'db.php';
require_once 'functions.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Debug POST data
error_log("POST data received: " . print_r($_POST, true));

// Only accept POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['status' => 'error', 'message' => 'Invalid request method']);
    exit;
}

// Get form data and handle both regular POST and multipart/form-data
$username = isset($_POST['login_username']) ? sanitize($_POST['login_username']) : '';
$password = isset($_POST['login_password']) ? $_POST['login_password'] : '';
$remember = isset($_POST['remember']) ? (bool)$_POST['remember'] : false;

// Debug remember me option
error_log("Remember me option: " . ($remember ? 'Yes' : 'No'));

// Debug received values
error_log("Username received: " . $username);
error_log("Password received: " . ($password ? 'Yes' : 'No'));

// Validate input
if(empty($username) || empty($password)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Please fill in all fields',
        'debug' => [
            'username_empty' => empty($username),
            'password_empty' => empty($password),
            'post_data' => $_POST
        ]
    ]);
    exit;
}

try {
    // Check if user exists
    $stmt = $conn->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute([$username]);
    $user = $stmt->fetch();

    // Debug logging
    error_log("User found: " . ($user ? 'Yes' : 'No'));
    if ($user) {
        error_log("Stored password hash: " . $user['password']);
        error_log("Password verification result: " . (password_verify($password, $user['password']) ? 'Success' : 'Failed'));
    }

    if($user && password_verify($password, $user['password'])) {
        // Set session variables
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['user_role'] = $user['role'];

        // Handle remember me option
        if ($remember) {
            // Set a longer session lifetime (30 days in seconds)
            ini_set('session.cookie_lifetime', 30 * 24 * 60 * 60);

            // Generate a secure remember token
            $selector = bin2hex(random_bytes(16));
            $validator = bin2hex(random_bytes(32));

            // Hash the validator for storage
            $hashedValidator = password_hash($validator, PASSWORD_DEFAULT);

            // Set expiry date (30 days from now)
            $expires = date('Y-m-d H:i:s', time() + 30 * 24 * 60 * 60);

            try {
                // Check if remember_tokens table exists
                $tableCheck = $conn->query("SHOW TABLES LIKE 'remember_tokens'");
                if ($tableCheck->rowCount() == 0) {
                    // Create remember_tokens table if it doesn't exist
                    $conn->exec("CREATE TABLE IF NOT EXISTS remember_tokens (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        selector VARCHAR(32) NOT NULL,
                        hashed_validator VARCHAR(255) NOT NULL,
                        expires DATETIME NOT NULL,
                        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    )");
                }

                // Delete any existing tokens for this user
                $deleteStmt = $conn->prepare("DELETE FROM remember_tokens WHERE user_id = ?");
                $deleteStmt->execute([$user['id']]);

                // Store the new token
                $insertStmt = $conn->prepare("INSERT INTO remember_tokens (user_id, selector, hashed_validator, expires) VALUES (?, ?, ?, ?)");
                $insertStmt->execute([$user['id'], $selector, $hashedValidator, $expires]);

                // Set the remember cookie (30 days)
                $cookieValue = $selector . ':' . $validator;
                setcookie(
                    'remember_me',
                    $cookieValue,
                    time() + 30 * 24 * 60 * 60,  // 30 days
                    '/',                          // Path
                    '',                           // Domain
                    false,                        // Secure (set to true in production with HTTPS)
                    true                          // HttpOnly
                );

                error_log("Remember me cookie set for user ID: {$user['id']}");
            } catch (PDOException $e) {
                error_log("Error setting remember token: " . $e->getMessage());
                // Continue with login even if remember me fails
            }
        }

        // Determine redirect based on user role
        $redirect = SITE_URL . '/admin/index.php';
        if ($user['role'] === 'customer' || $user['role'] === 'staff') {
            $redirect = SITE_URL . '/user/index.php';
        }

        // Log the redirect URL for debugging
        error_log("Redirect URL: {$redirect}");

        echo json_encode([
            'status' => 'success',
            'message' => 'Login successful',
            'redirect' => $redirect
        ]);
    } else {
        // Set error notification for next page load
        setErrorNotification('Invalid username or password');

        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid username or password'
        ]);
    }
} catch (PDOException $e) {
    error_log("Database error: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => 'An error occurred during login'
    ]);
}







