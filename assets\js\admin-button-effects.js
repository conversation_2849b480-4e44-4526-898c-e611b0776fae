/**
 * Admin Button Effects
 * Adds interactive effects to admin dashboard buttons
 */

document.addEventListener('DOMContentLoaded', function() {
    // Add ripple effect to admin hero buttons
    const buttons = document.querySelectorAll('.admin-hero .admin-actions .btn');
    
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Create ripple element
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            
            // Get position
            const rect = button.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            
            // Set position and size
            ripple.style.width = ripple.style.height = `${size}px`;
            ripple.style.left = `${e.clientX - rect.left - size/2}px`;
            ripple.style.top = `${e.clientY - rect.top - size/2}px`;
            
            // Remove after animation completes
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // Add icons to buttons if they don't have one
        if (!button.querySelector('i')) {
            const icon = document.createElement('i');
            icon.className = 'fas';
            
            // Set icon based on button class
            if (button.classList.contains('primary-btn')) {
                icon.classList.add('fa-tasks');
            } else if (button.classList.contains('secondary-btn')) {
                icon.classList.add('fa-users');
            } else if (button.classList.contains('purple-btn')) {
                icon.classList.add('fa-cogs');
            } else if (button.classList.contains('orange-btn')) {
                icon.classList.add('fa-tools');
            } else if (button.classList.contains('teal-btn')) {
                icon.classList.add('fa-sign-out-alt');
            }
            
            // Add icon before text
            button.prepend(icon);
            
            // Add space after icon
            const space = document.createTextNode(' ');
            icon.after(space);
        }
    });
    
    // Add hover effect to show button description
    buttons.forEach(button => {
        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.classList.add('button-tooltip');
        
        // Set tooltip text based on button content
        let tooltipText = '';
        if (button.textContent.includes('Manage Shipments')) {
            tooltipText = 'View and manage all shipment records';
        } else if (button.textContent.includes('Manage Users')) {
            tooltipText = 'Add, edit, or remove user accounts';
        } else if (button.textContent.includes('System Settings')) {
            tooltipText = 'Configure system-wide settings';
        } else if (button.textContent.includes('Maintenance')) {
            tooltipText = 'Access maintenance and debugging tools';
        } else if (button.textContent.includes('Logout')) {
            tooltipText = 'Sign out of your account';
        }
        
        tooltip.textContent = tooltipText;
        button.appendChild(tooltip);
        
        // Show/hide tooltip on hover
        button.addEventListener('mouseenter', () => {
            tooltip.style.opacity = '1';
            tooltip.style.transform = 'translateY(0)';
        });
        
        button.addEventListener('mouseleave', () => {
            tooltip.style.opacity = '0';
            tooltip.style.transform = 'translateY(10px)';
        });
    });
});
