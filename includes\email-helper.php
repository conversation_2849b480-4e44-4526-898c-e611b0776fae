<?php
/**
 * Email Helper Class
 * 
 * This class provides email functionality for the TransLogix Tracking System
 */
class EmailHelper {
    private $senderEmail;
    private $senderName;
    private $isEnabled;
    private $siteName;
    private $siteUrl;
    
    /**
     * Constructor
     */
    public function __construct() {
        // Get settings from database
        $this->isEnabled = getSetting('enable_email_notifications', '1') === '1';
        $this->senderEmail = getSetting('notification_sender_email', '<EMAIL>');
        $this->senderName = getSetting('company_name', 'TransLogix');
        $this->siteName = getSetting('site_name', 'TransLogix Tracking System');
        
        // Get site URL
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $this->siteUrl = $protocol . $host . SITE_URL;
    }
    
    /**
     * Send an email
     * 
     * @param string $to Recipient email address
     * @param string $subject Email subject
     * @param string $message Email message (HTML)
     * @param string $plainText Plain text version of the message
     * @return bool Whether the email was sent successfully
     */
    public function sendEmail($to, $subject, $message, $plainText = '') {
        // Check if email notifications are enabled
        if (!$this->isEnabled) {
            $this->logEmail($to, $subject, 'Email not sent: Notifications are disabled');
            return false;
        }
        
        // Validate email address
        if (!filter_var($to, FILTER_VALIDATE_EMAIL)) {
            $this->logEmail($to, $subject, 'Email not sent: Invalid recipient email');
            return false;
        }
        
        // Set headers
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: ' . $this->senderName . ' <' . $this->senderEmail . '>',
            'Reply-To: ' . $this->senderEmail,
            'X-Mailer: PHP/' . phpversion()
        ];
        
        // Apply email template
        $fullMessage = $this->applyTemplate($subject, $message);
        
        // Log mail configuration
        $smtpHost = ini_get('SMTP');
        $smtpPort = ini_get('smtp_port');
        $this->logEmail($to, $subject, "SMTP Host: $smtpHost, SMTP Port: $smtpPort");

        // Send email
        $result = mail($to, $subject, $fullMessage, implode("\r\n", $headers));
        
        // Log result
        $this->logEmail($to, $subject, "Email sent result: " . ($result ? 'success' : 'failure'));
        
        return $result;
    }
    
    /**
     * Apply email template
     * 
     * @param string $subject Email subject
     * @param string $content Email content
     * @return string Complete HTML email
     */
    private function applyTemplate($subject, $content) {
        $template = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>' . htmlspecialchars($subject) . '</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
        }
        .header {
            background-color: #4a6cf7;
            padding: 20px;
            text-align: center;
            color: white;
        }
        .content {
            padding: 20px;
        }
        .footer {
            background-color: #f4f4f4;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        a {
            color: #4a6cf7;
            text-decoration: none;
        }
        .button {
            display: inline-block;
            padding: 10px 20px;
            background-color: #4a6cf7;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>' . htmlspecialchars($this->siteName) . '</h1>
        </div>
        <div class="content">
            ' . $content . '
        </div>
        <div class="footer">
            <p>&copy; ' . date('Y') . ' ' . htmlspecialchars($this->senderName) . '. All rights reserved.</p>
            <p>This email was sent from a notification-only address that cannot accept incoming email.</p>
        </div>
    </div>
</body>
</html>';
        
        return $template;
    }
    
    /**
     * Log email activity
     * 
     * @param string $to Recipient email
     * @param string $subject Email subject
     * @param string $message Log message
     */
    private function logEmail($to, $subject, $message) {
        $logMessage = date('Y-m-d H:i:s') . " - Email: To: $to, Subject: $subject - $message";
        error_log($logMessage);
    }
    
    /**
     * Send shipment status update notification
     * 
     * @param array $shipment Shipment data
     * @param string $status New status
     * @param string $customerEmail Customer email address
     * @return bool Whether the email was sent successfully
     */
    public function sendShipmentStatusUpdate($shipment, $status, $customerEmail) {
        // Format status for display
        $formattedStatus = ucfirst(str_replace('_', ' ', $status));
        
        // Create subject
        $subject = "Shipment #{$shipment['tracking_number']} Status Update: $formattedStatus";
        
        // Create tracking URL
        $trackingUrl = $this->siteUrl . '/tracking.php?tracking_number=' . urlencode($shipment['tracking_number']);
        
        // Create message
        $message = "
            <h2>Shipment Status Update</h2>
            <p>Hello,</p>
            <p>Your shipment with tracking number <strong>{$shipment['tracking_number']}</strong> has been updated.</p>
            <p><strong>New Status:</strong> $formattedStatus</p>
            <p><strong>Origin:</strong> {$shipment['origin']}</p>
            <p><strong>Destination:</strong> {$shipment['destination']}</p>
            <p><strong>Updated:</strong> " . date('F j, Y, g:i a') . "</p>
            <p>You can track your shipment using the link below:</p>
            <p><a href=\"$trackingUrl\" class=\"button\">Track Your Shipment</a></p>
            <p>Or copy and paste this URL into your browser:</p>
            <p>$trackingUrl</p>
            <p>Thank you for choosing {$this->senderName}!</p>
        ";
        
        // Send email
        return $this->sendEmail($customerEmail, $subject, $message);
    }
    
    /**
     * Send new shipment notification
     * 
     * @param array $shipment Shipment data
     * @param string $customerEmail Customer email address
     * @return bool Whether the email was sent successfully
     */
    public function sendNewShipmentNotification($shipment, $customerEmail) {
        // Create subject
        $subject = "New Shipment Created: #{$shipment['tracking_number']}";
        
        // Create tracking URL
        $trackingUrl = $this->siteUrl . '/tracking.php?tracking_number=' . urlencode($shipment['tracking_number']);
        
        // Create message
        $message = "
            <h2>New Shipment Created</h2>
            <p>Hello,</p>
            <p>A new shipment has been created with the following details:</p>
            <p><strong>Tracking Number:</strong> {$shipment['tracking_number']}</p>
            <p><strong>Origin:</strong> {$shipment['origin']}</p>
            <p><strong>Destination:</strong> {$shipment['destination']}</p>
            <p><strong>Status:</strong> " . ucfirst(str_replace('_', ' ', $shipment['status'])) . "</p>
            <p><strong>Created:</strong> " . date('F j, Y, g:i a', strtotime($shipment['created_at'])) . "</p>
            <p>You can track your shipment using the link below:</p>
            <p><a href=\"$trackingUrl\" class=\"button\">Track Your Shipment</a></p>
            <p>Or copy and paste this URL into your browser:</p>
            <p>$trackingUrl</p>
            <p>Thank you for choosing {$this->senderName}!</p>
        ";
        
        // Send email
        return $this->sendEmail($customerEmail, $subject, $message);
    }
    
    /**
     * Send contact form notification to admin
     * 
     * @param array $formData Contact form data
     * @return bool Whether the email was sent successfully
     */
    public function sendContactFormNotification($formData) {
        // Get admin email
        $adminEmail = defined('ADMIN_EMAIL') ? ADMIN_EMAIL : '<EMAIL>';
        
        // Create subject
        $subject = "New Contact Form Submission: {$formData['subject']}";
        
        // Create message
        $message = "
            <h2>New Contact Form Submission</h2>
            <p><strong>Name:</strong> {$formData['name']}</p>
            <p><strong>Email:</strong> {$formData['email']}</p>
            <p><strong>Phone:</strong> {$formData['phone']}</p>
            <p><strong>Subject:</strong> {$formData['subject']}</p>
            <p><strong>Message:</strong></p>
            <p>" . nl2br(htmlspecialchars($formData['message'])) . "</p>
            <p><strong>Submitted:</strong> " . date('F j, Y, g:i a') . "</p>
        ";
        
        // Send email
        return $this->sendEmail($adminEmail, $subject, $message);
    }
    
    /**
     * Send support request notification to admin
     * 
     * @param array $formData Support form data
     * @param int $userId User ID
     * @param string $username Username
     * @return bool Whether the email was sent successfully
     */
    public function sendSupportRequestNotification($formData, $userId, $username) {
        // Get admin email
        $adminEmail = defined('ADMIN_EMAIL') ? ADMIN_EMAIL : '<EMAIL>';
        
        // Create subject
        $subject = "New Support Request: {$formData['subject']}";
        
        // Create message
        $message = "
            <h2>New Support Request</h2>
            <p><strong>User:</strong> $username (ID: $userId)</p>
            <p><strong>Subject:</strong> {$formData['subject']}</p>
            <p><strong>Priority:</strong> {$formData['priority']}</p>
            <p><strong>Message:</strong></p>
            <p>" . nl2br(htmlspecialchars($formData['message'])) . "</p>
            <p><strong>Submitted:</strong> " . date('F j, Y, g:i a') . "</p>
        ";
        
        // Send email
        return $this->sendEmail($adminEmail, $subject, $message);
    }
    
    /**
     * Send password reset email
     * 
     * @param string $email User email
     * @param string $resetToken Reset token
     * @return bool Whether the email was sent successfully
     */
    public function sendPasswordResetEmail($email, $resetToken) {
        // Create subject
        $subject = "Password Reset Request";
        
        // Create reset URL
        $resetUrl = $this->siteUrl . '/reset-password.php?token=' . urlencode($resetToken);
        
        // Create message
        $message = "
            <h2>Password Reset Request</h2>
            <p>Hello,</p>
            <p>We received a request to reset your password. If you didn't make this request, you can ignore this email.</p>
            <p>To reset your password, click the button below:</p>
            <p><a href=\"$resetUrl\" class=\"button\">Reset Password</a></p>
            <p>Or copy and paste this URL into your browser:</p>
            <p>$resetUrl</p>
            <p>This link will expire in 24 hours.</p>
            <p>Thank you,</p>
            <p>{$this->senderName} Team</p>
        ";
        
        // Send email
        return $this->sendEmail($email, $subject, $message);
    }
}
