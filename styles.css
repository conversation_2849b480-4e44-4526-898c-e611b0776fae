/* Global Styles */
:root {
    /* Theme colors */
    --primary-color: #5c2be2; /* Main purple */
    --primary-rgb: 92, 43, 226; /* RGB values for opacity control */
    --primary-dark: #4f25c2; /* Darker purple for hover */
    --primary-light: #7e56e8; /* Lighter purple for accents */

    --secondary-color: #6c757d; /* Gray - default secondary color */
    --secondary-color-rgb: 108, 117, 125; /* RGB values for opacity control */
    --secondary-dark: #5a6268; /* Darker gray for hover */
    --secondary-light: #868e96; /* Lighter gray for accents */

    /* Light theme (default) */
    --bg-color: #ffffff;
    --bg-secondary: #f4f7fb;
    --text-color: #333333;
    --text-secondary: #666666;
    --border-color: #e1e1e1;
    --box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);

    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.15);
    --glass-border: 1px solid rgba(255, 255, 255, 0.3);
    --glass-shadow: 0 8px 32px 0 rgba(92, 43, 226, 0.2);
    --glass-blur: blur(4px);
}

/* Dark theme class to be toggled with JavaScript */
.dark-theme {
    --bg-color: #121212;
    --bg-secondary: #1e1e1e;
    --text-color: #f5f5f5;
    --text-secondary: #cccccc;
    --border-color: #2c2c2c;
    --box-shadow: 0 2px 15px rgba(0, 0, 0, 0.4);

    /* Glassmorphism for dark theme */
    --glass-bg: rgba(30, 30, 30, 0.15);
    --glass-border: 1px solid rgba(255, 255, 255, 0.1);
    --glass-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Clean mobile reset without aggressive overrides */
@media (max-width: 768px) {
    html {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
    }

    body {
        margin: 0;
        padding: 0 0 70px 0;
        position: relative;
    }

    /* Ensure hero starts at top */
    .hero {
        margin-top: 0;
        padding-top: 0;
    }
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: clamp(14px, 2.5vw, 16px); /* Responsive base font size */
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

a {
    text-decoration: none;
    color: var(--primary-color);
}

ul {
    list-style: none;
}

/* ===== RESPONSIVE TYPOGRAPHY ===== */

/* Headings with fluid typography */
h1, .h1 {
    font-size: clamp(1.8rem, 5vw, 3rem);
    line-height: 1.2;
    margin-bottom: clamp(16px, 3vw, 24px);
    font-weight: 700;
}

h2, .h2 {
    font-size: clamp(1.5rem, 4vw, 2.2rem);
    line-height: 1.3;
    margin-bottom: clamp(14px, 2.5vw, 20px);
    font-weight: 600;
}

h3, .h3 {
    font-size: clamp(1.2rem, 3vw, 1.8rem);
    line-height: 1.3;
    margin-bottom: clamp(12px, 2vw, 18px);
    font-weight: 600;
}

h4, .h4 {
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    line-height: 1.4;
    margin-bottom: clamp(10px, 1.5vw, 16px);
    font-weight: 600;
}

h5, .h5 {
    font-size: clamp(1rem, 2vw, 1.25rem);
    line-height: 1.4;
    margin-bottom: clamp(8px, 1vw, 14px);
    font-weight: 600;
}

h6, .h6 {
    font-size: clamp(0.9rem, 1.5vw, 1.1rem);
    line-height: 1.4;
    margin-bottom: clamp(6px, 0.5vw, 12px);
    font-weight: 600;
}

/* Paragraph and text elements */
p {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: 1.6;
    margin-bottom: clamp(12px, 2vw, 16px);
}

/* Lists */
li {
    font-size: clamp(14px, 2.5vw, 16px);
    line-height: 1.6;
    margin-bottom: clamp(4px, 1vw, 8px);
}

/* Small text */
small, .small {
    font-size: clamp(14px, 2vw, 16px); /* Increased minimum from 12px to 14px */
    line-height: 1.5;
}

/* Large text */
.lead {
    font-size: clamp(16px, 3vw, 20px);
    line-height: 1.5;
    margin-bottom: clamp(16px, 3vw, 24px);
}

.btn {
    display: inline-block;
    padding: clamp(10px, 2vw, 12px) clamp(16px, 4vw, 24px);
    border-radius: 8px;
    font-size: clamp(14px, 2.5vw, 16px);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    /* Glassmorphism effect */
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    min-height: 44px; /* Minimum touch target size */
}

.primary-btn {
    background-color: var(--primary-color);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.primary-btn:hover {
    background-color: var(--secondary-color);
    box-shadow: 0 8px 25px rgba(92, 43, 226, 0.4);
    transform: translateY(-2px);
}

.secondary-btn {
    background-color: rgba(92, 43, 226, 0.1);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.secondary-btn:hover {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 8px 25px rgba(92, 43, 226, 0.4);
    transform: translateY(-2px);
}

.teal-btn {
    background-color: var(--secondary-color);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.4);
}

.teal-btn:hover {
    background-color: var(--secondary-dark);
    box-shadow: 0 8px 25px rgba(0, 212, 177, 0.4);
    transform: translateY(-2px);
}

.section-title {
    font-size: clamp(1.8rem, 5vw, 2.2rem);
    line-height: 1.2;
    margin-bottom: clamp(12px, 2vw, 15px);
    color: var(--dark-color);
    text-align: center;
}

.section-subtitle {
    font-size: clamp(1rem, 2.5vw, 1.1rem);
    line-height: 1.5;
    margin-bottom: clamp(30px, 5vw, 40px);
    text-align: center;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

/* Header Styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    position: fixed; /* Change to fixed to make it sticky while floating */
    top: 20px; /* Add some space from the top */
    left: 50%; /* Center horizontally */
    transform: translateX(-50%); /* Center horizontally */
    z-index: 100;
    transition: all 0.3s ease;
    width: 90%; /* Slightly narrower than 100% */
    max-width: 1200px; /* Maximum width */
    border-radius: 15px; /* Rounded corners */
    margin: 0 auto; /* Center the header */
}

.dark-theme header {
    background: rgba(30, 30, 30, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    z-index: 101;
}

.logo .logo-content { /* Style the new logo container */
    display: flex;
    align-items: center;
    gap: clamp(8px, 2vw, 10px);
    font-size: clamp(1rem, 3vw, 1.2rem); /* Responsive logo text size */
    font-weight: 600;
    color: var(--primary-color);
}

.logo .logo-icon {
    font-size: clamp(1.4rem, 4vw, 1.8rem); /* Responsive icon size */
    color: var(--secondary-color);
}

nav {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
}

nav ul {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
}

nav ul li {
    margin-left: 20px; /* Adjust spacing if needed */
}

nav ul li:first-child {
    margin-left: 0; /* Remove margin from the first item */
}

nav ul li a {
    color: var(--dark-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

nav ul li a:hover {
    color: var(--secondary-color);
    transform: translateY(-2px);
}

.login-btn a {
    background-color: var(--primary-color);
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
}

.login-btn a:hover {
    box-shadow: 0 8px 25px rgba(92, 43, 226, 0.4);
    transform: translateY(-2px);
}

/* Theme Toggle Button */
.theme-toggle {
    margin-left: 15px;
}

.theme-toggle button {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 10px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    min-width: 40px;
    min-height: 40px;
}

.theme-toggle button:hover {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    transform: translateY(-2px);
    border-radius: 8px;
}

.dark-theme .theme-toggle button {
    color: var(--secondary-color);
}

/* Hero Section */
.hero {
    background: linear-gradient(rgba(92, 43, 226, 0.8), rgba(79, 37, 194, 0.8)), url('https://images.unsplash.com/photo-1494412651409-8963ce7935a7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
    color: white;
    padding: 120px 0 60px 0; /* Reduced padding */
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 70vh; /* Reduced height */
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    z-index: 0;
}

.hero-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    margin-top: 5rem;
}

.hero h1 {
    font-size: clamp(2rem, 6vw, 3rem);
    line-height: 1.2;
    margin-bottom: clamp(16px, 3vw, 20px);
}

.hero p {
    font-size: clamp(1rem, 3vw, 1.2rem);
    line-height: 1.5;
    margin-bottom: clamp(24px, 4vw, 30px);
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Tracking Section */
.tracking {
    padding: 60px 0;
    background-color: var(--bg-secondary);
    transition: background-color 0.3s ease;
}

.tracking-box {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    max-width: 800px;
    margin: 0 auto;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    transition: background-color 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
}

.tracking-box h2 {
    text-align: center;
    margin-bottom: 25px;
    color: var(--dark-color);
}

.tracking-form {
    margin-bottom: 30px;
}


.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    font-size: 1rem;
    color: var(--text-color);
    /* Glassmorphism effect */
    background: var(--glass-bg);
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: 0 4px 12px rgba(31, 38, 135, 0.1);
    transition: all 0.3s ease, color 0.3s ease, background 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border: 1px solid var(--primary-color);
    box-shadow: 0 8px 20px rgba(92, 43, 226, 0.2);
}

.route-search h3 {
    margin-bottom: 15px;
    text-align: center;
    color: var(--dark-color);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
}

/* Services Section */
.services {
    padding: 80px 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.service-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 30px;
    text-align: center;
    transition: all 0.3s ease, background-color 0.3s ease, border 0.3s ease;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-color);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(92, 43, 226, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.service-icon {
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 20px;
    transition: color 0.3s ease;
}

.service-card h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
}

.service-card p {
    margin-bottom: 20px;
}

.learn-more {
    font-weight: 600;
    color: var(--secondary-color);
    transition: color 0.3s ease;
}

.learn-more:hover {
    color: var(--primary-color);
}

/* Industries Section */
.industries {
    padding: 80px 0;
    background-color: var(--bg-secondary);
    transition: background-color 0.3s ease;
}

.industries-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 20px;
}

.industry-card {
    background-color: var(--glass-bg);
    border-radius: 16px;
    padding: 25px;
    text-align: center;
    transition: all 0.3s ease, background-color 0.3s ease, border 0.3s ease;
    /* Glassmorphism effect */
    backdrop-filter: var(--glass-blur);
    -webkit-backdrop-filter: var(--glass-blur);
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    color: var(--text-color);
}

.industry-card:hover {
    transform: translateY(-5px);
    background-color: var(--primary-color);
    color: rgb(101, 148, 209);
    box-shadow: 0 15px 30px rgba(141, 106, 236, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.industry-card:hover .industry-icon {
    color: white;
}

.industry-icon {
    font-size: 2rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
    transition: color 0.3s ease;
}

.industry-card h3 {
    font-size: 1.1rem;
}

/* CTA Section */
.cta-section {
    padding: 80px 0;
    background: linear-gradient(rgba(92, 43, 226, 0.9), rgba(79, 37, 194, 0.9)), url('https://images.unsplash.com/photo-1566576721346-d4a3b4eaeb55?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80') no-repeat center center/cover;
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    backdrop-filter: blur(2px);
    -webkit-backdrop-filter: blur(2px);
    z-index: 0;
}

.cta-content {
    position: relative;
    z-index: 1;
    max-width: 700px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.cta-content p {
    font-size: 1.1rem;
    margin-bottom: 30px;
}

/* Footer */
footer {
    background-color: var(--bg-secondary);
    color: var(--text-color);
    padding: 60px 0 20px;
    transition: background-color 0.3s ease, color 0.3s ease;
}

.dark-theme footer {
    background-color: #0a0a0a;
}

.footer-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.footer-col h3, .footer-col h4 {
    margin-bottom: 20px;
    color: var(--text-color);
}

.footer-col p {
    margin-bottom: 20px;
    color: var(--text-secondary);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    color: var(--text-color);
    font-size: 1.2rem;
    transition: color 0.3s ease;
}

.social-icons a:hover {
    color: var(--secondary-color);
}

.footer-col ul li {
    margin-bottom: 10px;
}

.footer-col ul li a {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.footer-col ul li a:hover {
    color: var(--secondary-color);
}

.contact-info li {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: var(--text-secondary);
}

.footer-bottom {
    border-top: 1px solid var(--border-color);
    padding-top: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.footer-bottom p {
    color: var(--text-secondary);
}

.footer-links {
    display: flex;
    gap: 20px;
}

.footer-links a {
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--secondary-color);
}

/* Mobile Menu Styles - REMOVED (Using bottom nav only) */

/* Mobile Bottom Navigation - Clean Design */
.mobile-bottom-nav {
    display: none;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
    z-index: 9999;
    height: 70px;
    transition: all 0.3s ease;
}

.dark-theme .mobile-bottom-nav {
    background: rgba(18, 18, 18, 0.95);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
}

.mobile-nav-container {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 8px;
    overflow-x: auto;
    overflow-y: hidden;
    gap: 6px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
    justify-content: flex-start;
    position: relative;
    scroll-behavior: smooth;
}

.mobile-nav-container::-webkit-scrollbar {
    display: none; /* Chrome/Safari */
}

.mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #666;
    background: none;
    border: none;
    cursor: pointer;
    padding: 6px 4px;
    border-radius: 10px;
    transition: all 0.3s ease;
    min-width: 60px; /* Increased from 55px */
    width: auto; /* Allow flexible width */
    max-width: 70px; /* Prevent too wide */
    min-height: 48px;
    font-family: inherit;
    flex-shrink: 0;
    margin: 0 2px;
    position: relative;
    z-index: 1;
    scroll-snap-align: center;
}

/* Remove the spacing since logo is now fixed and not in the navigation container */

/* Ensure smooth scrolling and proper spacing */
.mobile-nav-container {
    scroll-snap-type: x proximity;
    justify-content: flex-start;
}

/* Scroll snap alignment moved to main mobile-nav-item rule */

.dark-theme .mobile-nav-item {
    color: #999;
}

.mobile-nav-item:hover,
.mobile-nav-item.active {
    color: var(--primary-color);
    background: rgba(92, 43, 226, 0.1);
    transform: translateY(-2px);
}

.mobile-nav-item i {
    width: 20px;
    height: 20px;
    stroke-width: 2;
    margin-bottom: 2px;
}

.mobile-nav-item span {
    font-size: 11px; /* Slightly smaller for better fit */
    font-weight: 500;
    line-height: 1.1; /* Allow slight line height for wrapping */
    text-align: center;
    word-break: break-word; /* Allow text to break */
    hyphens: auto; /* Enable hyphenation */
    max-width: 100%;
}

#mobile-theme-toggle i {
    transition: transform 0.3s ease;
}

/* Mobile Logo Container - Fixed in Center */
.mobile-logo-container {
    position: fixed !important;
    left: 50% !important;
    bottom: 15px !important;
    transform: translateX(-50%) !important;
    z-index: 10000 !important;
    width: 50px;
    height: 50px;
    display: none; /* Hidden by default, shown on mobile */
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(15px);
    border-radius: 50%;
    border: 2px solid rgba(92, 43, 226, 0.4);
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-logo {
    pointer-events: auto;
    cursor: pointer;
    display: flex !important;
    align-items: center;
    justify-content: center;
}

/* Mobile logo specific styles - show only icon */
.mobile-logo .site-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    width: 100%;
    height: 100%;
}

.mobile-logo .logo-icon {
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.mobile-logo .logo-icon-bg {
    width: 26px;
    height: 26px;
    border-radius: 4px;
    position: absolute;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    transform: rotate(45deg);
}

.mobile-logo .logo-icon-inner {
    font-size: 14px;
    color: white;
    position: relative;
    z-index: 2;
}

/* Hide text part of logo on mobile */
.mobile-logo .logo-text {
    display: none;
}

/* Fallback for mobile logo if JS doesn't load */
.mobile-logo:empty::before {
    content: "📦";
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

/* Additional fallback - show container even if empty */
@media (max-width: 768px) {
    .mobile-logo-container:empty {
        background: rgba(92, 43, 226, 0.8);
    }

    .mobile-logo-container:empty::before {
        content: "📦";
        font-size: 20px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }
}

/* Ensure mobile logo is always visible when container is shown */
@media (max-width: 768px) {
    .mobile-logo-container {
        display: flex !important;
        opacity: 1 !important;
        visibility: visible !important;
    }

    .mobile-logo-container .logo {
        display: flex !important;
        opacity: 1 !important;
        visibility: visible !important;
        width: 100%;
        height: 100%;
    }

    .mobile-logo-container .site-logo {
        display: flex !important;
        opacity: 1 !important;
        visibility: visible !important;
        width: 100%;
        height: 100%;
    }

    .mobile-logo-container .logo-icon {
        display: flex !important;
    }

    .mobile-logo-container .logo-icon-inner {
        display: block !important;
    }
}

/* Disturbance effect when items pass through logo */
.mobile-nav-item.passing-through {
    transform: scale(0.8) translateY(-2px);
    opacity: 0.6;
    filter: blur(1px);
    z-index: 2;
    transition: all 0.2s ease;
}

/* Enhanced disturbance effect for better visibility */
.mobile-nav-item.passing-through i {
    transform: rotate(5deg);
}

.mobile-nav-item.passing-through span {
    opacity: 0.4;
}

.mobile-logo-container.disturbed {
    transform: translate(-50%, -50%) scale(1.1);
    background: rgba(92, 43, 226, 0.2);
    border-color: rgba(92, 43, 226, 0.6);
    box-shadow: 0 0 20px rgba(92, 43, 226, 0.4);
}

/* Dark theme adjustments for mobile navigation */
.dark-theme .mobile-logo-container {
    background: rgba(30, 30, 40, 0.95);
    border-color: rgba(92, 43, 226, 0.5);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dark-theme .mobile-logo-container.disturbed {
    background: rgba(92, 43, 226, 0.4);
    border-color: rgba(92, 43, 226, 0.8);
    box-shadow: 0 0 25px rgba(92, 43, 226, 0.6);
}

/* Old mobile logo styles removed - now using desktop logo system */

/* Wave effects removed - using simple desktop logo system */

/* Animation keyframes removed - using desktop logo animations */

/* All old mobile logo styles removed - now using desktop logo system */

/* Responsive Styles */
@media (max-width: 992px) {
    /* Hide all header navigation on mobile - using bottom nav instead */
    nav#main-nav {
        display: none !important;
    }

    /* Hide header on mobile */
    header {
        display: none;
    }

    /* Remove body padding on mobile since no header */
    body:not(.has-hero) {
        padding-top: 0;
    }

    /* Show mobile bottom navigation */
    .mobile-bottom-nav {
        display: block !important;
    }

    /* Show mobile logo */
    .mobile-logo-container {
        display: flex !important;
    }

    /* Clean mobile body styles */
    body {
        padding-bottom: 70px;
        margin: 0;
        overflow-x: hidden;
        position: relative;
    }

    /* Clean footer styles */
    footer {
        margin-bottom: 0;
        padding-bottom: 20px;
    }

    /* Clean hero section for mobile */
    .hero {
        margin-top: 0;
        padding-top: 10px; /* Minimal top space */
        min-height: auto; /* Let content determine height */
        display: flex;
        align-items: flex-start; /* Align to top */
        justify-content: center;
        padding-bottom: 30px;
    }

    /* Bigger text in hero section on mobile */
    .hero h1 {
        font-size: 2.2rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    /* Increase general text size on mobile */
    body {
        font-size: 16px; /* Increase base font size */
    }

    p {
        font-size: 16px; /* Ensure paragraphs are readable */
    }

    /* Increase button text size */
    .btn {
        font-size: 16px;
    }



    .hero .container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 0 20px;
    }

    .hero-content {
        text-align: center;
        max-width: 100%;
        width: 100%;
    }

    .hero h1 {
        font-size: clamp(1.6rem, 6vw, 2.2rem) !important;
        margin-bottom: 12px !important;
        line-height: 1.1 !important;
        font-weight: 700 !important;
    }

    .hero p {
        font-size: clamp(0.85rem, 3.5vw, 1rem) !important;
        margin-bottom: 18px !important;
        line-height: 1.3 !important;
        opacity: 0.95;
    }

    .cta-buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: 10px !important;
        align-items: center !important;
        margin-top: 15px !important;
    }

    .cta-buttons .btn {
        width: 180px !important;
        padding: 10px 18px !important;
        font-size: 0.85rem !important;
        font-weight: 600 !important;
    }
}

@media (max-width: 768px) {
    /* Hero text sizes are now handled by responsive typography */

    .cta-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .cta-buttons .btn {
        width: 100%;
    }

    .form-row {
        flex-direction: column;
    }

    .tracking-box {
        padding: 20px;
    }

    .services-grid, .industries-grid {
        grid-template-columns: 1fr;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        margin-top: 15px;
        justify-content: center;
        flex-wrap: wrap;
    }

    /* Section title sizes are now handled by responsive typography */
}

@media (max-width: 480px) {
    /* Typography is now handled by responsive typography CSS */

    .footer-grid {
        grid-template-columns: 1fr;
    }
}
